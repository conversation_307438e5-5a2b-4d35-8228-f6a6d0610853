'use client'

import { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Volume2 } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface VoiceSearchProps {
  onResult: (text: string) => void
  placeholder?: string
  className?: string
}

export function VoiceSearch({ onResult, placeholder = "اضغط للبحث بالصوت...", className = "" }: VoiceSearchProps) {
  const [isListening, setIsListening] = useState(false)
  const [isSupported, setIsSupported] = useState(false)
  const [transcript, setTranscript] = useState('')
  const recognitionRef = useRef<any>(null)

  useEffect(() => {
    // التحقق من دعم المتصفح للتعرف على الصوت
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition
      if (SpeechRecognition) {
        setIsSupported(true)
        
        const recognition = new SpeechRecognition()
        recognition.continuous = false
        recognition.interimResults = true
        recognition.lang = 'ar-SA' // العربية السعودية
        
        recognition.onstart = () => {
          setIsListening(true)
        }
        
        recognition.onresult = (event: any) => {
          let finalTranscript = ''
          let interimTranscript = ''
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript
            if (event.results[i].isFinal) {
              finalTranscript += transcript
            } else {
              interimTranscript += transcript
            }
          }
          
          const currentTranscript = finalTranscript || interimTranscript
          setTranscript(currentTranscript)
          
          if (finalTranscript) {
            onResult(finalTranscript.trim())
            setTranscript('')
          }
        }
        
        recognition.onerror = (event: any) => {
          console.error('خطأ في التعرف على الصوت:', event.error)
          setIsListening(false)
          setTranscript('')
        }
        
        recognition.onend = () => {
          setIsListening(false)
        }
        
        recognitionRef.current = recognition
      }
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [onResult])

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setTranscript('')
      recognitionRef.current.start()
    }
  }

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
    }
  }

  if (!isSupported) {
    return null // إخفاء الزر إذا لم يكن مدعوماً
  }

  return (
    <div className={`relative ${className}`}>
      <Button
        type="button"
        variant={isListening ? "destructive" : "outline"}
        size="sm"
        onClick={isListening ? stopListening : startListening}
        className={`
          transition-all duration-300 ease-in-out
          ${isListening 
            ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg scale-110 animate-pulse' 
            : 'bg-white hover:bg-gray-50 text-gray-600 border-gray-300'
          }
        `}
        title={isListening ? "اضغط لإيقاف التسجيل" : "اضغط للبحث بالصوت"}
      >
        {isListening ? (
          <div className="flex items-center space-x-1 space-x-reverse">
            <MicOff className="h-4 w-4" />
            <div className="flex space-x-1">
              <div className="w-1 h-4 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-1 h-4 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-1 h-4 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        ) : (
          <Mic className="h-4 w-4" />
        )}
      </Button>
      
      {/* عرض النص المؤقت أثناء التسجيل */}
      {isListening && transcript && (
        <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg shadow-lg z-50">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Volume2 className="h-4 w-4 text-blue-600" />
            <span className="text-sm text-blue-800 font-medium">يتم التعرف على الصوت...</span>
          </div>
          <p className="text-blue-900 mt-1 font-arabic">{transcript}</p>
        </div>
      )}
      
      {/* رسالة التعليمات */}
      {isListening && !transcript && (
        <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-green-50 border border-green-200 rounded-lg shadow-lg z-50">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Mic className="h-4 w-4 text-green-600 animate-pulse" />
            <span className="text-sm text-green-800 font-medium">تحدث الآن...</span>
          </div>
          <p className="text-green-700 text-xs mt-1">قل ما تريد البحث عنه بوضوح</p>
        </div>
      )}
    </div>
  )
}
