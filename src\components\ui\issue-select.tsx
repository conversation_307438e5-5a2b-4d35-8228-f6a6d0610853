'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Search, FileText } from 'lucide-react'

interface Issue {
  id: number
  case_number: string
  title: string
  client_name: string
  court_name: string
  status: string
  issue_type: string
  amount: number
}

interface IssueSelectProps {
  value: string
  onChange: (issueId: string, issueData: Issue | null) => void
  label?: string
  placeholder?: string
  required?: boolean
}

export function IssueSelect({ value, onChange, label = "القضية", placeholder = "اختر القضية", required = false }: IssueSelectProps) {
  const [issues, setIssues] = useState<Issue[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const fetchIssues = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/issues')
      const result = await response.json()
      
      if (result.success) {
        setIssues(result.data)
      }
    } catch (error) {
      console.error('Error fetching issues:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchIssues()
  }, [])

  useEffect(() => {
    if (value && issues.length > 0) {
      const issue = issues.find(i => i.id.toString() === value)
      setSelectedIssue(issue || null)
    }
  }, [value, issues])

  const filteredIssues = issues.filter(issue =>
    issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    issue.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    issue.client_name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (issue: Issue) => {
    setSelectedIssue(issue)
    onChange(issue.id.toString(), issue)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedIssue(null)
    onChange('', null)
    setSearchTerm('')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'in_progress': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلقة'
      case 'in_progress': return 'قيد المعالجة'
      case 'completed': return 'مكتملة'
      case 'cancelled': return 'ملغية'
      default: return 'غير محدد'
    }
  }

  return (
    <div className="relative">
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      
      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            <FileText className="h-4 w-4 mr-2 text-gray-400" />
            <span className={selectedIssue ? 'text-gray-900' : 'text-gray-500'}>
              {selectedIssue ? `${selectedIssue.case_number} - ${selectedIssue.title}` : placeholder}
            </span>
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في القضايا..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة القضايا */}
            <div className="max-h-64 overflow-y-auto">
              {isLoading ? (
                <div className="p-3 text-center text-gray-500">جاري التحميل...</div>
              ) : filteredIssues.length > 0 ? (
                <>
                  {selectedIssue && (
                    <div
                      className="p-2 hover:bg-gray-100 cursor-pointer border-b text-red-600"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredIssues.map((issue) => (
                    <div
                      key={issue.id}
                      className="p-3 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                      onClick={() => handleSelect(issue)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-gray-400" />
                            <span className="font-medium text-gray-900">{issue.case_number}</span>
                            <Badge className={getStatusColor(issue.status)} size="sm" className="mr-2">
                              {getStatusText(issue.status)}
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">{issue.title}</div>
                          <div className="text-sm text-gray-500 mt-1">
                            الموكل: {issue.client_name} • المحكمة: {issue.court_name}
                            {issue.amount > 0 && ` • القيمة: ${issue.amount.toLocaleString()} ريال`}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-3 text-center text-gray-500">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد قضايا'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* عرض تفاصيل القضية المختارة */}
      {selectedIssue && (
        <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="text-sm text-green-800">
            <div className="grid grid-cols-2 gap-2">
              <div><strong>رقم القضية:</strong> {selectedIssue.case_number}</div>
              <div><strong>الموكل:</strong> {selectedIssue.client_name}</div>
              <div><strong>المحكمة:</strong> {selectedIssue.court_name}</div>
              <div><strong>النوع:</strong> {selectedIssue.issue_type}</div>
              {selectedIssue.amount > 0 && (
                <div className="col-span-2"><strong>قيمة القضية:</strong> {selectedIssue.amount.toLocaleString()} ريال</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* حقل مخفي للقيمة */}
      <input type="hidden" value={value} name="issue_id" />
    </div>
  )
}
