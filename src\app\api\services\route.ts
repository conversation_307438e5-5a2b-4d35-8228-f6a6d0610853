import { NextRequest, NextResponse } from 'next/server'

// GET - جلب جميع الخدمات
export async function GET() {
  try {
    // بيانات الخدمات مع الربط بالنسب المالية فقط
    const sampleData = [
      {
        id: 1,
        name: 'اعداد',
        lineage_id: 1,
        lineage_name: 'نسب القضايا المدنية',
        created_date: '2024-01-01'
      },
      {
        id: 2,
        name: 'جلسة',
        lineage_id: 1,
        lineage_name: 'نسب القضايا المدنية',
        created_date: '2024-01-01'
      },
      {
        id: 3,
        name: 'متابعة',
        lineage_id: 2,
        lineage_name: 'نسب القضايا التجارية',
        created_date: '2024-01-01'
      },
      {
        id: 4,
        name: 'اشراف',
        lineage_id: 2,
        lineage_name: 'نسب القضايا التجارية',
        created_date: '2024-01-01'
      },
      {
        id: 5,
        name: 'مصروفات قضائية',
        lineage_id: 3,
        lineage_name: 'نسب القضايا الجنائية',
        created_date: '2024-01-01'
      },
      {
        id: 6,
        name: 'استشارات قانونية',
        lineage_id: 4,
        lineage_name: 'نسب القضايا العمالية',
        created_date: '2024-01-01'
      },
      {
        id: 7,
        name: 'صياغة عقود',
        lineage_id: 5,
        lineage_name: 'نسب القضايا العقارية',
        created_date: '2024-01-01'
      },
      {
        id: 8,
        name: 'تمثيل قانوني',
        lineage_id: 6,
        lineage_name: 'نسب القضايا الإدارية',
        created_date: '2024-01-01'
      }
    ]

    return NextResponse.json({
      success: true,
      data: sampleData
    })
  } catch (error) {
    console.error('Error fetching services:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الخدمات' },
      { status: 500 }
    )
  }
}

// POST - إضافة خدمة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, lineage_id } = body

    if (!name || !name.trim()) {
      return NextResponse.json(
        { success: false, error: 'اسم الخدمة مطلوب' },
        { status: 400 }
      )
    }

    if (!lineage_id) {
      return NextResponse.json(
        { success: false, error: 'مجموعة النسب المالية مطلوبة' },
        { status: 400 }
      )
    }

    // محاكاة إضافة خدمة جديدة
    const newService = {
      id: Date.now(),
      name: name.trim(),
      lineage_id: Number(lineage_id),
      created_date: new Date().toISOString().split('T')[0]
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الخدمة بنجاح',
      data: newService
    })
  } catch (error) {
    console.error('Error creating service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الخدمة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث خدمة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, lineage_id } = body

    if (!id || !name || !name.trim()) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الخدمة مطلوبان' },
        { status: 400 }
      )
    }

    if (!lineage_id) {
      return NextResponse.json(
        { success: false, error: 'مجموعة النسب المالية مطلوبة' },
        { status: 400 }
      )
    }

    // محاكاة تحديث الخدمة
    return NextResponse.json({
      success: true,
      message: 'تم تحديث الخدمة بنجاح'
    })
  } catch (error) {
    console.error('Error updating service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الخدمة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف خدمة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الخدمة مطلوب' },
        { status: 400 }
      )
    }

    // محاكاة حذف الخدمة
    return NextResponse.json({
      success: true,
      message: 'تم حذف الخدمة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الخدمة' },
      { status: 500 }
    )
  }
}
