const { Client } = require('pg');

const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function updateAccountsHierarchy() {
  try {
    await client.connect();
    console.log('🔄 بدء تحديث نظام الحسابات الهرمي...');

    // 1. جعل جميع الحسابات الحالية رئيسية (لا تظهر في القوائم)
    console.log('\n📋 تحديث تصنيف الحسابات الحالية...');
    await client.query(`
      UPDATE chart_of_accounts
      SET is_main_account = true,
          is_sub_account = false,
          allow_posting = false
      WHERE account_level <= 4
    `);
    console.log('   ✅ تم تحديث الحسابات الحالية لتصبح رئيسية');

    // 2. إنشاء حسابات فرعية للموكلين
    console.log('\n👥 إنشاء حسابات فرعية للموكلين...');

    // التحقق من وجود حساب الموكلين الرئيسي
    const clientMainAccount = await client.query(`
      SELECT id FROM chart_of_accounts WHERE account_code = '1121'
    `);

    if (clientMainAccount.rows.length > 0) {
      const mainAccountId = clientMainAccount.rows[0].id;

      // جلب الموكلين وإنشاء حسابات لهم
      const clients = await client.query(`
        SELECT id, name, phone FROM clients WHERE status = 'active'
      `);

      for (const clientRow of clients.rows) {
        const accountCode = `1121${String(clientRow.id).padStart(3, '0')}`;
        const accountName = `${clientRow.name} - موكل`;

        // التحقق من عدم وجود الحساب
        const existingAccount = await client.query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [accountCode]);

        if (existingAccount.rows.length === 0) {
          await client.query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              account_level, is_main_account, is_sub_account,
              linked_table, account_nature, allow_posting, is_active
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          `, [
            accountCode, accountName, 'أصول', mainAccountId,
            5, false, true, 'clients', 'مدين', true, true
          ]);
          console.log(`   ✅ تم إنشاء حساب: ${accountCode} - ${accountName}`);
        }
      }
    }

    // 3. إنشاء حسابات فرعية للموظفين
    console.log('\n👨‍💼 إنشاء حسابات فرعية للموظفين...');

    const employeeMainAccount = await client.query(`
      SELECT id FROM chart_of_accounts WHERE account_code = '1122'
    `);

    if (employeeMainAccount.rows.length > 0) {
      const mainAccountId = employeeMainAccount.rows[0].id;

      // جلب الموظفين وإنشاء حسابات لهم
      const employees = await client.query(`
        SELECT id, name, phone FROM employees WHERE status = 'active'
      `);

      for (const employeeRow of employees.rows) {
        const accountCode = `1122${String(employeeRow.id).padStart(3, '0')}`;
        const accountName = `${employeeRow.name} - موظف`;

        // التحقق من عدم وجود الحساب
        const existingAccount = await client.query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [accountCode]);

        if (existingAccount.rows.length === 0) {
          await client.query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              account_level, is_main_account, is_sub_account,
              linked_table, account_nature, allow_posting, is_active
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          `, [
            accountCode, accountName, 'أصول', mainAccountId,
            5, false, true, 'employees', 'مدين', true, true
          ]);
          console.log(`   ✅ تم إنشاء حساب: ${accountCode} - ${accountName}`);
        }
      }
    }

    // 4. إنشاء بعض الحسابات الفرعية الأساسية للعمليات
    console.log('\n💰 إنشاء حسابات فرعية أساسية...');

    const basicSubAccounts = [
      {
        code: '********',
        name: 'صندوق المكتب الرئيسي',
        parent_code: '1111',
        type: 'أصول',
        nature: 'مدين'
      },
      {
        code: '********',
        name: 'البنك الأهلي - حساب جاري',
        parent_code: '1112',
        type: 'أصول',
        nature: 'مدين'
      },
      {
        code: '********',
        name: 'أتعاب قضايا مدنية',
        parent_code: '411',
        type: 'إيرادات',
        nature: 'دائن'
      },
      {
        code: '********',
        name: 'أتعاب قضايا تجارية',
        parent_code: '411',
        type: 'إيرادات',
        nature: 'دائن'
      },
      {
        code: '********',
        name: 'مصروفات محاكم مدنية',
        parent_code: '511',
        type: 'مصروفات',
        nature: 'مدين'
      },
      {
        code: '********',
        name: 'مصروفات محاكم تجارية',
        parent_code: '511',
        type: 'مصروفات',
        nature: 'مدين'
      }
    ];

    for (const account of basicSubAccounts) {
      // البحث عن الحساب الأب
      const parentAccount = await client.query(`
        SELECT id FROM chart_of_accounts WHERE account_code = $1
      `, [account.parent_code]);

      if (parentAccount.rows.length > 0) {
        const parentId = parentAccount.rows[0].id;

        // التحقق من عدم وجود الحساب
        const existingAccount = await client.query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [account.code]);

        if (existingAccount.rows.length === 0) {
          await client.query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              account_level, is_main_account, is_sub_account,
              account_nature, allow_posting, is_active
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
          `, [
            account.code, account.name, account.type, parentId,
            5, false, true, account.nature, true, true
          ]);
          console.log(`   ✅ تم إنشاء حساب: ${account.code} - ${account.name}`);
        }
      }
    }

    // 5. عرض الإحصائيات النهائية
    console.log('\n📊 الإحصائيات النهائية:');

    const finalStats = await client.query(`
      SELECT
        COUNT(*) as total_accounts,
        COUNT(CASE WHEN is_main_account = true THEN 1 END) as main_accounts,
        COUNT(CASE WHEN is_sub_account = true THEN 1 END) as sub_accounts,
        COUNT(CASE WHEN allow_posting = true THEN 1 END) as posting_accounts,
        COUNT(CASE WHEN linked_table = 'clients' THEN 1 END) as client_accounts,
        COUNT(CASE WHEN linked_table = 'employees' THEN 1 END) as employee_accounts
      FROM chart_of_accounts
    `);

    const stats = finalStats.rows[0];
    console.log(`   📋 إجمالي الحسابات: ${stats.total_accounts}`);
    console.log(`   🏢 الحسابات الرئيسية: ${stats.main_accounts}`);
    console.log(`   📝 الحسابات الفرعية: ${stats.sub_accounts}`);
    console.log(`   ✅ حسابات قابلة للترحيل: ${stats.posting_accounts}`);
    console.log(`   👥 حسابات الموكلين: ${stats.client_accounts}`);
    console.log(`   👨‍💼 حسابات الموظفين: ${stats.employee_accounts}`);

    // 6. عرض عينة من الحسابات الفرعية
    console.log('\n📋 عينة من الحسابات الفرعية الجديدة:');
    const subAccounts = await client.query(`
      SELECT account_code, account_name, linked_table
      FROM chart_of_accounts
      WHERE is_sub_account = true AND allow_posting = true
      ORDER BY account_code
      LIMIT 10
    `);

    subAccounts.rows.forEach(acc => {
      const type = acc.linked_table ? `(${acc.linked_table})` : '';
      console.log(`   ${acc.account_code}: ${acc.account_name} ${type}`);
    });

    console.log('\n✅ تم تحديث نظام الحسابات الهرمي بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في التحديث:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
if (require.main === module) {
  updateAccountsHierarchy()
    .then(() => {
      console.log('🎉 تم إنجاز التحديث بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في التحديث:', error);
      process.exit(1);
    });
}

module.exports = { updateAccountsHierarchy };
