@font-face {
  font-family: '<PERSON><PERSON><PERSON>';
  src: url('/fonts/<PERSON>-Art-bold.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON><PERSON>';
  src: url('/fonts/<PERSON>-Art-bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* تطبيق الخط على جميع العناصر */
* {
  font-family: '<PERSON>-<PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تطبيق الخط العريض على العناوين */
h1, h2, h3, h4, h5, h6,
.font-bold,
.font-semibold,
.font-medium,
th,
.sidebar-title,
.menu-title {
  font-family: 'Khalid-Art', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 700 !important;
}

/* تطبيق الخط على القوائم */
.sidebar,
.menu-item,
.nav-item,
.dropdown-item {
  font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تطبيق الخط على النماذج */
input,
textarea,
select,
button,
.form-control,
.btn {
  font-family: 'Khalid-Art', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تطبيق الخط على الجداول */
table,
td,
th,
.table {
  font-family: 'Khalid-Art', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تطبيق الخط على النوافذ المنبثقة */
.modal,
.dialog,
.popup,
.card {
  font-family: 'Khalid-Art', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}
