'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Users, Clock, TrendingUp, Calendar, Award, BarChart3 } from 'lucide-react'

export default function EmployeeReportsPage() {
  const reports = [
    {
      id: 'attendance-report',
      title: 'تقرير الحضور والانصراف',
      description: 'تتبع حضور الموظفين وساعات العمل',
      icon: Clock,
      color: 'bg-blue-500',
      status: 'قريباً'
    },
    {
      id: 'performance-report',
      title: 'تقرير الأداء',
      description: 'تقييم أداء الموظفين في القضايا',
      icon: TrendingUp,
      color: 'bg-green-500',
      status: 'قريباً'
    },
    {
      id: 'salary-report',
      title: 'تقرير الرواتب',
      description: 'ملخص رواتب ومكافآت الموظفين',
      icon: BarChart3,
      color: 'bg-purple-500',
      status: 'قريباً'
    },
    {
      id: 'monthly-summary',
      title: 'الملخص الشهري',
      description: 'تقرير شامل عن نشاط الموظفين شهرياً',
      icon: Calendar,
      color: 'bg-orange-500',
      status: 'قريباً'
    },
    {
      id: 'achievements-report',
      title: 'تقرير الإنجازات',
      description: 'إنجازات ومكافآت الموظفين',
      icon: Award,
      color: 'bg-red-500',
      status: 'قريباً'
    },
    {
      id: 'team-analysis',
      title: 'تحليل الفريق',
      description: 'تحليل أداء الفرق والأقسام',
      icon: Users,
      color: 'bg-indigo-500',
      status: 'قريباً'
    }
  ]

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <Users className="h-8 w-8 text-rose-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">تقارير الموظفين</h1>
            <p className="text-gray-600">تقارير شاملة عن الموظفين والأداء</p>
          </div>
        </div>

        {/* التقارير المتاحة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reports.map((report) => (
            <Card key={report.id} className="transition-all hover:shadow-lg">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className={`p-3 rounded-lg ${report.color} text-white`}>
                    <report.icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{report.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">{report.description}</p>
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    disabled
                    className="opacity-50"
                  >
                    {report.status}
                  </Button>
                  <span className="text-xs text-gray-500">قيد التطوير</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* معلومات */}
        <Card>
          <CardHeader>
            <CardTitle>معلومات التطوير</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">👥 تقارير الموظفين</h3>
              <p className="text-sm text-blue-700">
                تقارير الموظفين ستتضمن تتبع الحضور، تقييم الأداء، الرواتب، والإنجازات. 
                هذه الميزات قيد التطوير وستكون متاحة قريباً.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
