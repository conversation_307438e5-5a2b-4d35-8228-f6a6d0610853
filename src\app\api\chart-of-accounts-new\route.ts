import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب دليل الحسابات مع التسلسل الهرمي
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const level = searchParams.get('level') // 1, 2, 3, 4, all
    const parentId = searchParams.get('parentId')
    const includeBalances = searchParams.get('includeBalances') === 'true'
    const onlyTransactional = searchParams.get('onlyTransactional') === 'true'

    let whereClause = 'WHERE is_active = true'
    let params: any[] = []

    if (level && level !== 'all') {
      whereClause += ` AND account_level = $${params.length + 1}`
      params.push(parseInt(level))
    }

    if (parentId) {
      const levelNum = parseInt(level || '2')
      const parentColumn = `level_${levelNum - 1}_id`
      whereClause += ` AND ${parentColumn} = $${params.length + 1}`
      params.push(parseInt(parentId))
    }

    if (onlyTransactional) {
      whereClause += ' AND allow_transactions = true'
    }

    const accountsResult = await query(`
      SELECT 
        id,
        account_code,
        account_name,
        account_level,
        account_type,
        account_subtype,
        normal_balance,
        level_1_id,
        level_2_id,
        level_3_id,
        level_4_id,
        ${includeBalances ? 'opening_balance, current_balance,' : ''}
        is_active,
        allow_transactions,
        description,
        created_at,
        (
          SELECT COUNT(*) 
          FROM chart_of_accounts_new child 
          WHERE CASE 
            WHEN chart_of_accounts_new.account_level = 1 THEN child.level_1_id = chart_of_accounts_new.id
            WHEN chart_of_accounts_new.account_level = 2 THEN child.level_2_id = chart_of_accounts_new.id
            WHEN chart_of_accounts_new.account_level = 3 THEN child.level_3_id = chart_of_accounts_new.id
            ELSE 0
          END
        ) as children_count
      FROM chart_of_accounts_new
      ${whereClause}
      ORDER BY account_code
    `, params)

    // إضافة المسار الكامل لكل حساب
    const accountsWithPath = await Promise.all(
      accountsResult.rows.map(async (account) => {
        const pathResult = await query(
          'SELECT get_account_path($1) as full_path',
          [account.id]
        )
        return {
          ...account,
          full_path: pathResult.rows[0]?.full_path || account.account_name
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: accountsWithPath
    })
  } catch (error) {
    console.error('Error fetching chart of accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب دليل الحسابات' },
      { status: 500 }
    )
  }
}

// POST - إضافة حساب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      account_code,
      account_name,
      account_name_en,
      account_level,
      account_type,
      account_subtype,
      normal_balance,
      level_1_id,
      level_2_id,
      level_3_id,
      opening_balance = 0,
      description,
      notes
    } = body

    // التحقق من صحة البيانات
    if (!account_code || !account_name || !account_level || !account_type || !normal_balance) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم الحساب
    const existingAccount = await query(
      'SELECT id FROM chart_of_accounts_new WHERE account_code = $1',
      [account_code]
    )

    if (existingAccount.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم الحساب موجود مسبقاً' },
        { status: 400 }
      )
    }

    // تحديد ما إذا كان الحساب يسمح بالمعاملات (فقط المرتبة الرابعة)
    const allowTransactions = account_level === 4

    const result = await query(`
      INSERT INTO chart_of_accounts_new (
        account_code, account_name, account_name_en, account_level,
        account_type, account_subtype, normal_balance,
        level_1_id, level_2_id, level_3_id,
        opening_balance, current_balance, allow_transactions,
        description, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $11, $12, $13, $14)
      RETURNING *
    `, [
      account_code, account_name, account_name_en, account_level,
      account_type, account_subtype, normal_balance,
      level_1_id, level_2_id, level_3_id,
      opening_balance, allowTransactions,
      description, notes
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error creating account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الحساب' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حساب
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      account_name,
      account_name_en,
      account_subtype,
      description,
      notes,
      is_active
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE chart_of_accounts_new 
      SET 
        account_name = COALESCE($2, account_name),
        account_name_en = COALESCE($3, account_name_en),
        account_subtype = COALESCE($4, account_subtype),
        description = COALESCE($5, description),
        notes = COALESCE($6, notes),
        is_active = COALESCE($7, is_active),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id, account_name, account_name_en, account_subtype, description, notes, is_active])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error updating account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الحساب' },
      { status: 500 }
    )
  }
}
