-- تحديث يدوي لقاعدة البيانات
-- نسخ هذا الكود وتشغيله في pgAdmin أو أي أداة PostgreSQL

-- ===================================
-- 1. تحديث جدول النسب المالية
-- ===================================

-- حذف الأعمدة غير المطلوبة (إذا كانت موجودة)
DO $$ 
BEGIN
    -- حذف الأعمدة القديمة إذا كانت موجودة
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'management_percentage') THEN
        ALTER TABLE lineages DROP COLUMN management_percentage;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'court_percentage') THEN
        ALTER TABLE lineages DROP COLUMN court_percentage;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'commission_percentage') THEN
        ALTER TABLE lineages DROP COLUMN commission_percentage;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'other_percentage') THEN
        ALTER TABLE lineages DROP COLUMN other_percentage;
    END IF;
    
    -- إعادة تسمية group_name إلى name إذا كان موجوداً
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'group_name') THEN
        ALTER TABLE lineages RENAME COLUMN group_name TO name;
    END IF;
    
    -- إضافة admin_percentage إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'admin_percentage') THEN
        ALTER TABLE lineages ADD COLUMN admin_percentage DECIMAL(5,2) DEFAULT 0;
    END IF;
END $$;

-- إنشاء الجدول إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS lineages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    admin_percentage DECIMAL(5,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);

-- حذف البيانات القديمة وإدراج البيانات الجديدة
TRUNCATE TABLE lineages RESTART IDENTITY CASCADE;

INSERT INTO lineages (name, admin_percentage, created_date) VALUES
('نسب القضايا المدنية', 15.0, '2024-01-01'),
('نسب القضايا التجارية', 20.0, '2024-01-02'),
('نسب القضايا الجنائية', 10.0, '2024-01-03'),
('نسب القضايا العمالية', 25.0, '2024-01-04'),
('نسب القضايا العقارية', 18.0, '2024-01-05'),
('نسب القضايا الإدارية', 12.0, '2024-01-06'),
('نسب القضايا الأسرية', 8.0, '2024-01-07'),
('نسب القضايا الضريبية', 22.0, '2024-01-08');

-- ===================================
-- 2. إنشاء جدول الخدمات
-- ===================================

CREATE TABLE IF NOT EXISTS services (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_date DATE DEFAULT CURRENT_DATE
);

-- حذف البيانات القديمة وإدراج البيانات الجديدة
TRUNCATE TABLE services RESTART IDENTITY CASCADE;

INSERT INTO services (name, created_date) VALUES
('اعداد', '2024-01-01'),
('جلسة', '2024-01-01'),
('متابعة', '2024-01-01'),
('اشراف', '2024-01-01'),
('مصروفات قضائية', '2024-01-01'),
('استشارات قانونية', '2024-01-01'),
('صياغة عقود', '2024-01-01'),
('تمثيل قانوني', '2024-01-01');

-- ===================================
-- 3. إنشاء جدول توزيع القضايا
-- ===================================

CREATE TABLE IF NOT EXISTS case_distribution (
    id SERIAL PRIMARY KEY,
    issue_id INTEGER,
    lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
    admin_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);

-- ===================================
-- 4. إنشاء جدول تفاصيل توزيع الخدمات
-- ===================================

CREATE TABLE IF NOT EXISTS service_distributions (
    id SERIAL PRIMARY KEY,
    case_distribution_id INTEGER REFERENCES case_distribution(id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
    percentage DECIMAL(5,2) DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    lawyer_id INTEGER,
    created_date DATE DEFAULT CURRENT_DATE
);

-- ===================================
-- 5. إنشاء الفهارس
-- ===================================

CREATE INDEX IF NOT EXISTS idx_lineages_name ON lineages(name);
CREATE INDEX IF NOT EXISTS idx_services_name ON services(name);
CREATE INDEX IF NOT EXISTS idx_case_distribution_issue_id ON case_distribution(issue_id);
CREATE INDEX IF NOT EXISTS idx_case_distribution_lineage_id ON case_distribution(lineage_id);
CREATE INDEX IF NOT EXISTS idx_service_distributions_case_id ON service_distributions(case_distribution_id);
CREATE INDEX IF NOT EXISTS idx_service_distributions_service_id ON service_distributions(service_id);

-- ===================================
-- 6. التحقق من النتائج
-- ===================================

-- عرض هيكل جدول النسب المالية
SELECT 'جدول النسب المالية (lineages):' as info;
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'lineages' ORDER BY ordinal_position;

-- عرض بيانات النسب المالية
SELECT 'بيانات النسب المالية:' as info;
SELECT * FROM lineages ORDER BY id;

-- عرض هيكل جدول الخدمات
SELECT 'جدول الخدمات (services):' as info;
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'services' ORDER BY ordinal_position;

-- عرض بيانات الخدمات
SELECT 'بيانات الخدمات:' as info;
SELECT * FROM services ORDER BY id;

-- عرض ملخص الجداول
SELECT 'ملخص الجداول:' as info;
SELECT 
    'lineages' as table_name,
    COUNT(*) as record_count
FROM lineages
UNION ALL
SELECT 
    'services' as table_name,
    COUNT(*) as record_count
FROM services
UNION ALL
SELECT 
    'case_distribution' as table_name,
    COUNT(*) as record_count
FROM case_distribution
UNION ALL
SELECT 
    'service_distributions' as table_name,
    COUNT(*) as record_count
FROM service_distributions;

-- رسالة النجاح
SELECT '✅ تم تحديث قاعدة البيانات بنجاح!' as result;
