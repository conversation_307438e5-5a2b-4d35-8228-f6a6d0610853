@echo off
echo ========================================
echo    تشغيل خادم النظام القانوني
echo ========================================
echo.

cd /d "%~dp0"

echo جاري التحقق من Node.js...
node --version
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت
    pause
    exit /b 1
)

echo.
echo جاري التحقق من npm...
npm --version
if %errorlevel% neq 0 (
    echo خطأ: npm غير متاح
    pause
    exit /b 1
)

echo.
echo جاري تثبيت التبعيات...
npm install
if %errorlevel% neq 0 (
    echo خطأ: فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo.
echo ========================================
echo    بدء تشغيل الخادم على المنفذ 7443
echo ========================================
echo.
echo للإيقاف: اضغط Ctrl+C
echo الرابط: http://localhost:7443
echo.

npm run dev -- -p 7443

echo.
echo تم إيقاف الخادم
pause
