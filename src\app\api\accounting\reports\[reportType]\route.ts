import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - إنشاء التقارير المحاسبية
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ reportType: string }> }
) {
  try {
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const { reportType } = await params

    let reportData: any = {
      accounts: [],
      transactions: [],
      summary: {
        totalAssets: 0,
        totalLiabilities: 0,
        totalEquity: 0,
        totalRevenue: 0,
        totalExpenses: 0,
        netIncome: 0
      }
    }

    switch (reportType) {
      case 'trial-balance':
        reportData = await generateTrialBalance(dateFrom, dateTo)
        break

      case 'general-ledger':
        reportData = await generateGeneralLedger(dateFrom, dateTo)
        break

      case 'income-statement':
        reportData = await generateIncomeStatement(dateFrom, dateTo)
        break

      case 'balance-sheet':
        reportData = await generateBalanceSheet(dateFrom, dateTo)
        break

      case 'cash-flow':
        reportData = await generateCashFlow(dateFrom, dateTo)
        break

      case 'vouchers-summary':
        reportData = await generateVouchersSummary(dateFrom, dateTo)
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'نوع التقرير غير مدعوم'
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      ...reportData,
      reportType,
      dateFrom,
      dateTo,
      generatedAt: new Date().toISOString(),
      message: 'تم إنشاء التقرير بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء التقرير:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء التقرير',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// ميزان المراجعة
async function generateTrialBalance(dateFrom?: string | null, dateTo?: string | null) {
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  // جلب أرصدة الحسابات
  const accountsResult = await query(`
    WITH account_balances AS (
      -- أرصدة افتتاحية
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,
        ca.opening_balance,
        COALESCE(
          CASE
            WHEN ca.account_nature = 'مدين' THEN ca.opening_balance
            ELSE 0
          END, 0
        ) as opening_debit,
        COALESCE(
          CASE
            WHEN ca.account_nature = 'دائن' THEN ca.opening_balance
            ELSE 0
          END, 0
        ) as opening_credit
      FROM chart_of_accounts ca
      WHERE ca.allow_transactions = true AND ca.is_active = true

      UNION ALL



      -- حركات من القيود اليومية
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,
        0 as opening_balance,
        COALESCE(jed.debit_amount, 0) as opening_debit,
        COALESCE(jed.credit_amount, 0) as opening_credit
      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date <= $1
        AND je.status = 'approved'
      WHERE ca.allow_transactions = true AND ca.is_active = true
        AND jed.id IS NOT NULL
    )
    SELECT
      id,
      account_code,
      account_name,
      account_type,
      account_nature,
      SUM(opening_debit) as total_debit,
      SUM(opening_credit) as total_credit,
      CASE
        WHEN SUM(opening_debit) > SUM(opening_credit)
        THEN SUM(opening_debit) - SUM(opening_credit)
        ELSE 0
      END as debit_balance,
      CASE
        WHEN SUM(opening_credit) > SUM(opening_debit)
        THEN SUM(opening_credit) - SUM(opening_debit)
        ELSE 0
      END as credit_balance
    FROM account_balances
    GROUP BY id, account_code, account_name, account_type, account_nature
    HAVING SUM(opening_debit) != 0 OR SUM(opening_credit) != 0
    ORDER BY account_code
  `, [endDate])

  return {
    accounts: accountsResult.rows,
    summary: {
      totalDebit: accountsResult.rows.reduce((sum, acc) => sum + (acc.debit_balance || 0), 0),
      totalCredit: accountsResult.rows.reduce((sum, acc) => sum + (acc.credit_balance || 0), 0)
    }
  }
}

// قائمة الدخل
async function generateIncomeStatement(dateFrom?: string | null, dateTo?: string | null) {
  const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  // جلب الإيرادات والمصروفات
  const accountsResult = await query(`
    WITH income_accounts AS (
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,

        -- من القيود اليومية (تشمل السندات والقيود اليدوية)
        COALESCE(SUM(
          CASE
            WHEN ca.account_type IN ('إيرادات', 'revenue')
            THEN jed.credit_amount - jed.debit_amount
            WHEN ca.account_type IN ('مصروفات', 'expenses')
            THEN jed.debit_amount - jed.credit_amount
            ELSE 0
          END
        ), 0) as journal_amount

      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date BETWEEN $1 AND $2
        AND je.status = 'approved'
      WHERE ca.account_type IN ('إيرادات', 'revenue', 'مصروفات', 'expenses')
        AND ca.is_active = true
      GROUP BY ca.id, ca.account_code, ca.account_name, ca.account_type, ca.account_nature
    )
    SELECT
      *,
      CASE
        WHEN account_type IN ('إيرادات', 'revenue')
        THEN journal_amount
        ELSE 0
      END as total_revenue,
      CASE
        WHEN account_type IN ('مصروفات', 'expenses')
        THEN journal_amount
        ELSE 0
      END as total_expenses
    FROM income_accounts
    WHERE journal_amount != 0
    ORDER BY account_code
  `, [startDate, endDate])

  const accounts = accountsResult.rows
  const totalRevenue = accounts.reduce((sum, acc) => sum + (acc.total_revenue || 0), 0)
  const totalExpenses = accounts.reduce((sum, acc) => sum + (acc.total_expenses || 0), 0)
  const netIncome = totalRevenue - totalExpenses

  return {
    accounts,
    summary: {
      totalRevenue,
      totalExpenses,
      netIncome
    }
  }
}

// الميزانية العمومية
async function generateBalanceSheet(dateFrom?: string | null, dateTo?: string | null) {
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  const accountsResult = await query(`
    WITH balance_sheet_accounts AS (
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,
        ca.opening_balance,

        -- حساب الرصيد النهائي
        ca.opening_balance +
        COALESCE(SUM(
          CASE
            WHEN ca.account_nature = 'مدين'
            THEN COALESCE(jed.debit_amount, 0) - COALESCE(jed.credit_amount, 0)
            ELSE COALESCE(jed.credit_amount, 0) - COALESCE(jed.debit_amount, 0)
          END
        ), 0) +
        0 as final_balance

      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date <= $1
        AND je.status = 'approved'
      WHERE ca.account_type IN ('أصول', 'assets', 'خصوم', 'liabilities', 'حقوق ملكية', 'equity')
        AND ca.is_active = true
      GROUP BY ca.id, ca.account_code, ca.account_name, ca.account_type, ca.account_nature, ca.opening_balance
    )
    SELECT
      *,
      CASE
        WHEN account_type IN ('أصول', 'assets') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as asset_balance,
      CASE
        WHEN account_type IN ('خصوم', 'liabilities') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as liability_balance,
      CASE
        WHEN account_type IN ('حقوق ملكية', 'equity') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as equity_balance
    FROM balance_sheet_accounts
    WHERE final_balance != 0
    ORDER BY account_code
  `, [endDate])

  const accounts = accountsResult.rows
  const totalAssets = accounts.reduce((sum, acc) => sum + (acc.asset_balance || 0), 0)
  const totalLiabilities = accounts.reduce((sum, acc) => sum + (acc.liability_balance || 0), 0)
  const totalEquity = accounts.reduce((sum, acc) => sum + (acc.equity_balance || 0), 0)

  return {
    accounts,
    summary: {
      totalAssets,
      totalLiabilities,
      totalEquity
    }
  }
}

// دفتر الأستاذ العام
async function generateGeneralLedger(dateFrom?: string | null, dateTo?: string | null) {
  const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  const transactionsResult = await query(`
    SELECT
      CASE
        WHEN je.voucher_type = 'payment' THEN 'سند صرف'
        WHEN je.voucher_type = 'receipt' THEN 'سند قبض'
        WHEN je.voucher_type = 'journal' THEN 'قيد يومي'
        ELSE 'معاملة'
      END as transaction_type,
      je.entry_number as reference,
      je.entry_date as transaction_date,
      COALESCE(jed.description, je.description) as description,
      ca.account_code,
      ca.account_name,
      jed.debit_amount,
      jed.credit_amount,
      CASE
        WHEN je.voucher_type = 'payment' THEN je.beneficiary_name
        WHEN je.voucher_type = 'receipt' THEN je.payer_name
        ELSE ''
      END as party_name,
      je.voucher_type,
      pm.method_name as payment_method,
      c.currency_code
    FROM journal_entry_details jed
    JOIN journal_entries je ON jed.journal_entry_id = je.id
    JOIN chart_of_accounts ca ON jed.account_id = ca.id
    LEFT JOIN payment_methods pm ON je.payment_method_id = pm.id
    LEFT JOIN currencies c ON je.currency_id = c.id
    WHERE je.entry_date BETWEEN $1 AND $2
      AND je.status IN ('draft', 'approved')
      AND ca.is_active = true
    ORDER BY ca.account_code, je.entry_date, je.entry_number, jed.line_number
  `, [startDate, endDate])

  // تجميع المعاملات حسب الحساب
  const accountsMap = new Map()

  transactionsResult.rows.forEach(row => {
    const accountKey = `${row.account_code}-${row.account_name}`
    if (!accountsMap.has(accountKey)) {
      accountsMap.set(accountKey, {
        account_code: row.account_code,
        account_name: row.account_name,
        transactions: [],
        total_debit: 0,
        total_credit: 0,
        balance: 0
      })
    }

    const account = accountsMap.get(accountKey)
    account.transactions.push(row)
    account.total_debit += parseFloat(row.debit_amount || 0)
    account.total_credit += parseFloat(row.credit_amount || 0)
    account.balance = account.total_debit - account.total_credit
  })

  return {
    transactions: transactionsResult.rows,
    accounts: Array.from(accountsMap.values()),
    summary: {
      totalTransactions: transactionsResult.rows.length,
      totalAccounts: accountsMap.size
    }
  }
}

// ملخص السندات
async function generateVouchersSummary(dateFrom?: string | null, dateTo?: string | null) {
  const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  const summaryResult = await query(`
    SELECT
      voucher_type,
      COUNT(*) as total_count,
      SUM(amount) as total_amount,
      AVG(amount) as average_amount,
      MIN(amount) as min_amount,
      MAX(amount) as max_amount
    FROM journal_entries
    WHERE entry_date BETWEEN $1 AND $2
      AND voucher_type IN ('payment', 'receipt')
      AND status IN ('draft', 'approved')
    GROUP BY voucher_type
    ORDER BY voucher_type
  `, [startDate, endDate])

  // جلب تفاصيل السندات مع الحسابات المدينة والدائنة
  const detailsResult = await query(`
    SELECT
      je.voucher_type,
      je.entry_number,
      je.entry_date,
      je.amount,
      je.description,
      CASE
        WHEN je.voucher_type = 'payment' THEN je.beneficiary_name
        WHEN je.voucher_type = 'receipt' THEN je.payer_name
        ELSE 'غير محدد'
      END as party_name,
      pm.method_name as payment_method,
      c.currency_code,
      c.symbol as currency_symbol,
      -- الحساب المدين
      debit_acc.account_code as debit_account_code,
      debit_acc.account_name as debit_account_name,
      -- الحساب الدائن
      credit_acc.account_code as credit_account_code,
      credit_acc.account_name as credit_account_name
    FROM journal_entries je
    LEFT JOIN payment_methods pm ON je.payment_method_id = pm.id
    LEFT JOIN currencies c ON je.currency_id = c.id
    -- ربط الحساب المدين
    LEFT JOIN journal_entry_details jed_debit ON je.id = jed_debit.journal_entry_id
      AND jed_debit.debit_amount > 0 AND jed_debit.line_number = 1
    LEFT JOIN chart_of_accounts debit_acc ON jed_debit.account_id = debit_acc.id
    -- ربط الحساب الدائن
    LEFT JOIN journal_entry_details jed_credit ON je.id = jed_credit.journal_entry_id
      AND jed_credit.credit_amount > 0 AND jed_credit.line_number = 2
    LEFT JOIN chart_of_accounts credit_acc ON jed_credit.account_id = credit_acc.id
    WHERE je.entry_date BETWEEN $1 AND $2
      AND je.voucher_type IN ('payment', 'receipt')
      AND je.status IN ('draft', 'approved')
    ORDER BY je.entry_date DESC, je.entry_number DESC
    LIMIT 50
  `, [startDate, endDate])

  const paymentSummary = summaryResult.rows.find(r => r.voucher_type === 'payment')
  const receiptSummary = summaryResult.rows.find(r => r.voucher_type === 'receipt')

  return {
    accounts: summaryResult.rows,
    transactions: detailsResult.rows,
    summary: {
      totalPayments: parseFloat(paymentSummary?.total_amount || 0),
      totalReceipts: parseFloat(receiptSummary?.total_amount || 0),
      paymentCount: parseInt(paymentSummary?.total_count || 0),
      receiptCount: parseInt(receiptSummary?.total_count || 0),
      netAmount: parseFloat(receiptSummary?.total_amount || 0) - parseFloat(paymentSummary?.total_amount || 0)
    }
  }
}

// التدفقات النقدية
async function generateCashFlow(dateFrom?: string | null, dateTo?: string | null) {
  // TODO: تنفيذ تقرير التدفقات النقدية
  return {
    accounts: [],
    summary: {}
  }
}
