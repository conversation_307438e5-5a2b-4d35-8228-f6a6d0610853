'use client'

interface PrintFooterProps {
  showSignatures?: boolean
  showDate?: boolean
  customSignatures?: string[]
  className?: string
}

export function PrintFooter({ 
  showSignatures = true, 
  showDate = true,
  customSignatures = [],
  className = ''
}: PrintFooterProps) {
  const defaultSignatures = [
    'المحاسب',
    'المستلم / الدافع', 
    'المدير المالي'
  ]

  const signatures = customSignatures.length > 0 ? customSignatures : defaultSignatures

  return (
    <div className={`print-footer ${className}`}>
      <style jsx>{`
        .print-footer {
          width: 100%;
          margin-top: 40px;
          padding-top: 30px;
          border-top: 2px solid #e5e7eb;
          page-break-inside: avoid;
        }

        .signatures-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          margin-bottom: 20px;
          min-height: 80px;
        }

        .signature-box {
          flex: 1;
          text-align: center;
          margin: 0 20px;
          border-bottom: 2px solid #374151;
          padding-bottom: 5px;
          min-height: 60px;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
        }

        .signature-title {
          font-size: 14px;
          font-weight: bold;
          color: #374151;
          margin-top: 10px;
        }

        .signature-line {
          height: 40px;
          border-bottom: 1px solid #9ca3af;
          margin-bottom: 5px;
        }

        .footer-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #6b7280;
          border-top: 1px solid #e5e7eb;
          padding-top: 15px;
        }

        .print-date {
          font-weight: 500;
        }

        .system-info {
          font-style: italic;
        }

        @media print {
          .print-footer {
            margin-top: 30px;
            padding-top: 20px;
          }
          
          .signature-box {
            min-height: 50px;
            margin: 0 15px;
          }
          
          .signature-title {
            font-size: 12px;
          }
          
          .footer-info {
            font-size: 10px;
            padding-top: 10px;
          }
        }
      `}</style>

      {/* التوقيعات */}
      {showSignatures && (
        <div className="signatures-container">
          {signatures.map((signature, index) => (
            <div key={index} className="signature-box">
              <div className="signature-line"></div>
              <div className="signature-title">
                {signature}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* معلومات التذييل */}
      <div className="footer-info">
        <div className="system-info">
          تم إنشاء هذا المستند بواسطة نظام إدارة المكاتب القانونية
        </div>
        
        {showDate && (
          <div className="print-date">
            تاريخ الطباعة: {new Date().toLocaleDateString('ar-EG', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        )}
      </div>
    </div>
  )
}
