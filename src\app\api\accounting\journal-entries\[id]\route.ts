import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب قيد واحد مع تفاصيله
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url)
    const includeDetails = searchParams.get('include_details') === 'true'
    const { id: entryId } = await params

    // جلب القيد الرئيسي
    const entryResult = await query(`
      SELECT
        je.*,
        c.currency_code,
        c.symbol as currency_symbol,
        cc.center_name as cost_center_name,
        u.username as created_by_username
      FROM journal_entries je
      LEFT JOIN currencies c ON je.currency_id = c.id
      LEFT JOIN cost_centers cc ON je.cost_center_id = cc.id
      LEFT JOIN users u ON je.created_by_user_id = u.id
      WHERE je.id = $1
    `, [entryId])

    if (entryResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    let entry = entryResult.rows[0]

    // جلب التفاصيل إذا طُلب ذلك
    if (includeDetails) {
      const detailsResult = await query(`
        SELECT
          jed.*,
          ca.account_name,
          ca.account_code,
          c.currency_code,
          pm.method_name as payment_method_name,
          cc.center_name as cost_center_name
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts ca ON jed.account_id = ca.id
        LEFT JOIN currencies c ON jed.currency_id = c.id
        LEFT JOIN payment_methods pm ON jed.payment_method_id = pm.id
        LEFT JOIN cost_centers cc ON jed.cost_center_id = cc.id
        WHERE jed.journal_entry_id = $1
        ORDER BY jed.line_number
      `, [entryId])

      entry.details = detailsResult.rows
    }

    return NextResponse.json({
      success: true,
      entry,
      message: 'تم جلب القيد بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب القيد:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب القيد',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث قيد
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json()
    const { id: entryId } = await params
    const {
      entry_date,
      description,
      total_debit,
      total_credit,
      currency_id,
      cost_center_id,
      case_id,
      case_number,
      reference_number,
      status,
      details = []
    } = body

    // التحقق من وجود القيد
    const existingEntry = await query(
      'SELECT id, status FROM journal_entries WHERE id = $1',
      [entryId]
    )

    if (existingEntry.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    // منع تعديل القيود المعتمدة
    if (existingEntry.rows[0].status === 'approved') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن تعديل القيد المعتمد'
      }, { status: 400 })
    }

    // التحقق من توازن القيد
    if (Math.abs(total_debit - total_credit) > 0.01) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير متوازن'
      }, { status: 400 })
    }

    // بدء المعاملة
    await query('BEGIN')

    try {
      // تحديث القيد الرئيسي
      const result = await query(`
        UPDATE journal_entries
        SET
          entry_date = $2,
          description = $3,
          total_debit = $4,
          total_credit = $5,
          currency_id = $6,
          cost_center_id = $7,
          case_id = $8,
          case_number = $9,
          reference_number = $10,
          status = COALESCE($11, status),
          updated_date = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `, [
        entryId, entry_date, description, total_debit, total_credit,
        currency_id, cost_center_id, case_id, case_number, reference_number, status
      ])

      // حذف التفاصيل القديمة وإدراج الجديدة
      if (details.length > 0) {
        await query('DELETE FROM journal_entry_details WHERE journal_entry_id = $1', [entryId])

        for (const detail of details) {
          await query(`
            INSERT INTO journal_entry_details (
              journal_entry_id, line_number, account_id, debit_amount, credit_amount,
              currency_id, exchange_rate, cost_center_id, payment_method_id,
              description, reference_number, case_id
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          `, [
            entryId, detail.line_number, detail.account_id, detail.debit_amount || 0, detail.credit_amount || 0,
            detail.currency_id || currency_id, detail.exchange_rate || 1, detail.cost_center_id,
            detail.payment_method_id, detail.description, detail.reference_number,
            // في التحديث: حفظ case_id من التفصيل أو من القيد الرئيسي
            detail.case_id || (case_id && case_id !== '0' ? parseInt(case_id) : null)
          ])
        }
      }

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        entry: result.rows[0],
        message: 'تم تحديث القيد اليومي بنجاح'
      })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('خطأ في تحديث القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف قيد
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: entryId } = await params

    // التحقق من وجود القيد وحالته
    const existingEntry = await query(
      'SELECT id, status FROM journal_entries WHERE id = $1',
      [entryId]
    )

    if (existingEntry.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    // منع حذف القيود المعتمدة
    if (existingEntry.rows[0].status === 'approved') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف القيد المعتمد'
      }, { status: 400 })
    }

    // بدء المعاملة
    await query('BEGIN')

    try {
      // حذف التفاصيل أولاً
      await query('DELETE FROM journal_entry_details WHERE journal_entry_id = $1', [entryId])

      // حذف القيد الرئيسي
      await query('DELETE FROM journal_entries WHERE id = $1', [entryId])

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حذف القيد اليومي بنجاح'
      })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('خطأ في حذف القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
