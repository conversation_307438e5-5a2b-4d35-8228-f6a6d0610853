'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ChevronDown, Search, Settings } from 'lucide-react'

interface Service {
  id: number
  name: string
  description: string
  category: string
}

interface ServiceSelectProps {
  value: string
  onChange: (serviceName: string) => void
  label?: string
  placeholder?: string
  required?: boolean
}

export function ServiceSelect({ value, onChange, label = "الخدمة", placeholder = "اختر الخدمة", required = false }: ServiceSelectProps) {
  const [services, setServices] = useState<Service[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const fetchServices = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/services')
      const result = await response.json()
      
      if (result.success) {
        setServices(result.data)
      }
    } catch (error) {
      console.error('Error fetching services:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchServices()
  }, [])

  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (service: Service) => {
    onChange(service.name)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    onChange('')
    setIsOpen(false)
    setSearchTerm('')
  }

  return (
    <div className="relative">
      {label && (
        <Label className="text-xs font-medium text-gray-700 mb-1 block">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}

      <div className="relative">
        <div
          className="w-full px-2 py-1 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between text-xs h-6"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            <Settings className="h-3 w-3 mr-1 text-gray-400" />
            <span className={value ? 'text-gray-900' : 'text-gray-500'}>
              {value || placeholder}
            </span>
          </div>
          <ChevronDown className={`h-3 w-3 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-[60] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-3 w-3" />
                <Input
                  type="text"
                  placeholder="البحث في الخدمات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-8 text-xs h-6"
                />
              </div>
            </div>

            {/* قائمة الخدمات */}
            <div className="max-h-32 overflow-y-auto">
              {isLoading ? (
                <div className="p-2 text-center text-gray-500 text-xs">جاري التحميل...</div>
              ) : filteredServices.length > 0 ? (
                <>
                  {value && (
                    <div
                      className="p-2 hover:bg-gray-100 cursor-pointer border-b text-red-600"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-xs">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredServices.map((service) => (
                    <div
                      key={service.id}
                      className="p-2 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                      onClick={() => handleSelect(service)}
                    >
                      <div className="font-medium text-gray-900 text-xs">{service.name}</div>
                      {service.description && (
                        <div className="text-xs text-gray-500 mt-1">{service.description}</div>
                      )}
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-2 text-center text-gray-500 text-xs">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد خدمات'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
