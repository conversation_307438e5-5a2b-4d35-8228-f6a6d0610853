import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب الإشعارات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const recipientType = searchParams.get('recipientType')
    const recipientId = searchParams.get('recipientId')
    const isRead = searchParams.get('isRead')
    const limit = searchParams.get('limit') || '20'

    if (!recipientType || !recipientId) {
      return NextResponse.json(
        { success: false, error: 'نوع المستلم ومعرف المستلم مطلوبان' },
        { status: 400 }
      )
    }

    let whereClause = 'WHERE recipient_type = $1 AND recipient_id = $2'
    let params: any[] = [recipientType, recipientId]
    let paramIndex = 3

    if (isRead !== null) {
      whereClause += ` AND is_read = $${paramIndex}`
      params.push(isRead === 'true')
      paramIndex++
    }

    const result = await query(`
      SELECT 
        n.*,
        CASE 
          WHEN n.sender_type = 'user' THEN u.username
          WHEN n.sender_type = 'client' THEN c.name
        END as sender_name
      FROM notifications n
      LEFT JOIN users u ON n.sender_type = 'user' AND n.sender_id = u.id
      LEFT JOIN clients c ON n.sender_type = 'client' AND n.sender_id = c.id
      ${whereClause}
      ORDER BY n.created_at DESC
      LIMIT $${paramIndex}
    `, [...params, parseInt(limit)])

    return NextResponse.json({
      success: true,
      data: result.rows
    })

  } catch (error) {
    console.error('Error fetching notifications:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الإشعارات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء إشعار جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      recipientType,
      recipientId,
      senderType,
      senderId,
      notificationType = 'message',
      title,
      content,
      relatedId
    } = body

    if (!recipientType || !recipientId || !senderType || !senderId || !title) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة ناقصة' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO notifications (
        recipient_type, 
        recipient_id, 
        sender_type, 
        sender_id, 
        notification_type, 
        title, 
        content, 
        related_id
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      recipientType,
      recipientId,
      senderType,
      senderId,
      notificationType,
      title,
      content,
      relatedId
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إنشاء الإشعار بنجاح'
    })

  } catch (error) {
    console.error('Error creating notification:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الإشعار' },
      { status: 500 }
    )
  }
}

// PUT - تحديث إشعار (تحديد كمقروء عادة)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, isRead, recipientType, recipientId } = body

    if (id) {
      // تحديث إشعار واحد
      const result = await query(`
        UPDATE notifications 
        SET is_read = $2
        WHERE id = $1
        RETURNING *
      `, [id, isRead])

      if (result.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'الإشعار غير موجود' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        data: result.rows[0],
        message: 'تم تحديث الإشعار بنجاح'
      })

    } else if (recipientType && recipientId) {
      // تحديد جميع الإشعارات كمقروءة
      await query(`
        UPDATE notifications 
        SET is_read = true
        WHERE recipient_type = $1 AND recipient_id = $2 AND is_read = false
      `, [recipientType, recipientId])

      return NextResponse.json({
        success: true,
        message: 'تم تحديث جميع الإشعارات كمقروءة'
      })

    } else {
      return NextResponse.json(
        { success: false, error: 'معرف الإشعار أو بيانات المستلم مطلوبة' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Error updating notification:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الإشعار' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إشعار
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const recipientType = searchParams.get('recipientType')
    const recipientId = searchParams.get('recipientId')

    if (id) {
      // حذف إشعار واحد
      const result = await query(`
        DELETE FROM notifications 
        WHERE id = $1
        RETURNING *
      `, [id])

      if (result.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'الإشعار غير موجود' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'تم حذف الإشعار بنجاح'
      })

    } else if (recipientType && recipientId) {
      // حذف جميع الإشعارات المقروءة
      await query(`
        DELETE FROM notifications 
        WHERE recipient_type = $1 AND recipient_id = $2 AND is_read = true
      `, [recipientType, recipientId])

      return NextResponse.json({
        success: true,
        message: 'تم حذف جميع الإشعارات المقروءة'
      })

    } else {
      return NextResponse.json(
        { success: false, error: 'معرف الإشعار أو بيانات المستلم مطلوبة' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Error deleting notification:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الإشعار' },
      { status: 500 }
    )
  }
}
