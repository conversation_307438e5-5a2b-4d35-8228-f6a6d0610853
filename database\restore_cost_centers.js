const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function restoreCostCenters() {
  try {
    console.log('🔧 فحص وإصلاح جدول مراكز التكلفة...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. فحص وجود جدول cost_centers
    console.log('\n📋 فحص وجود جدول cost_centers...');
    
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'cost_centers'
      );
    `);
    
    if (tableExists.rows[0].exists) {
      console.log('✅ جدول cost_centers موجود');
      
      // فحص البيانات الموجودة
      const dataCount = await client.query('SELECT COUNT(*) as count FROM cost_centers');
      console.log(`📊 عدد مراكز التكلفة الموجودة: ${dataCount.rows[0].count}`);
      
      if (parseInt(dataCount.rows[0].count) > 0) {
        const sampleData = await client.query(`
          SELECT id, center_code, center_name, is_active 
          FROM cost_centers 
          ORDER BY id 
          LIMIT 5
        `);
        
        console.log('📋 عينة من مراكز التكلفة:');
        sampleData.rows.forEach(center => {
          console.log(`   - ${center.center_code}: ${center.center_name} ${center.is_active ? '(نشط)' : '(غير نشط)'}`);
        });
        
        console.log('\n✅ جدول مراكز التكلفة يعمل بشكل صحيح');
        return;
      }
    } else {
      console.log('❌ جدول cost_centers غير موجود - سيتم إنشاؤه...');
    }

    // 2. إنشاء جدول cost_centers
    console.log('\n🏗️ إنشاء جدول cost_centers...');
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS cost_centers (
        id SERIAL PRIMARY KEY,
        center_code VARCHAR(20) UNIQUE NOT NULL,
        center_name VARCHAR(255) NOT NULL,
        parent_id INTEGER REFERENCES cost_centers(id) ON DELETE SET NULL,
        center_level INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        description TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ تم إنشاء جدول cost_centers');

    // 3. إضافة فهارس للأداء
    console.log('\n📊 إضافة فهارس للأداء...');
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_cost_centers_code 
      ON cost_centers(center_code)
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_cost_centers_parent 
      ON cost_centers(parent_id)
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_cost_centers_active 
      ON cost_centers(is_active)
    `);
    
    console.log('✅ تم إضافة الفهارس');

    // 4. إدراج البيانات الأساسية
    console.log('\n📝 إدراج مراكز التكلفة الأساسية...');
    
    const basicCostCenters = [
      {
        code: 'CC001',
        name: 'الإدارة العامة',
        description: 'مركز تكلفة الإدارة العامة والخدمات الإدارية'
      },
      {
        code: 'CC002', 
        name: 'القسم القانوني',
        description: 'مركز تكلفة الخدمات القانونية والقضايا'
      },
      {
        code: 'CC003',
        name: 'المحاسبة والمالية', 
        description: 'مركز تكلفة الخدمات المحاسبية والمالية'
      },
      {
        code: 'CC004',
        name: 'الموارد البشرية',
        description: 'مركز تكلفة إدارة الموارد البشرية'
      },
      {
        code: 'CC005',
        name: 'التسويق والعلاقات العامة',
        description: 'مركز تكلفة أنشطة التسويق والعلاقات العامة'
      },
      {
        code: 'CC006',
        name: 'تقنية المعلومات',
        description: 'مركز تكلفة خدمات تقنية المعلومات والأنظمة'
      }
    ];

    for (const center of basicCostCenters) {
      try {
        await client.query(`
          INSERT INTO cost_centers (center_code, center_name, description, center_level, is_active)
          VALUES ($1, $2, $3, 1, true)
          ON CONFLICT (center_code) DO NOTHING
        `, [center.code, center.name, center.description]);
        
        console.log(`   ✅ تم إدراج: ${center.code} - ${center.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${center.code}: ${error.message}`);
      }
    }

    // 5. إضافة مراكز فرعية للقسم القانوني
    console.log('\n📋 إضافة مراكز فرعية للقسم القانوني...');
    
    // الحصول على معرف القسم القانوني
    const legalDeptResult = await client.query(`
      SELECT id FROM cost_centers WHERE center_code = 'CC002'
    `);
    
    if (legalDeptResult.rows.length > 0) {
      const legalDeptId = legalDeptResult.rows[0].id;
      
      const legalSubCenters = [
        {
          code: 'CC002-01',
          name: 'القضايا المدنية',
          description: 'مركز تكلفة القضايا المدنية'
        },
        {
          code: 'CC002-02',
          name: 'القضايا التجارية', 
          description: 'مركز تكلفة القضايا التجارية'
        },
        {
          code: 'CC002-03',
          name: 'القضايا الجنائية',
          description: 'مركز تكلفة القضايا الجنائية'
        },
        {
          code: 'CC002-04',
          name: 'قضايا الأحوال الشخصية',
          description: 'مركز تكلفة قضايا الأحوال الشخصية'
        },
        {
          code: 'CC002-05',
          name: 'القضايا العمالية',
          description: 'مركز تكلفة القضايا العمالية'
        }
      ];

      for (const subCenter of legalSubCenters) {
        try {
          await client.query(`
            INSERT INTO cost_centers (center_code, center_name, description, parent_id, center_level, is_active)
            VALUES ($1, $2, $3, $4, 2, true)
            ON CONFLICT (center_code) DO NOTHING
          `, [subCenter.code, subCenter.name, subCenter.description, legalDeptId]);
          
          console.log(`   ✅ تم إدراج: ${subCenter.code} - ${subCenter.name}`);
        } catch (error) {
          console.log(`   ⚠️ خطأ في إدراج ${subCenter.code}: ${error.message}`);
        }
      }
    }

    // 6. فحص النتائج النهائية
    console.log('\n📊 فحص النتائج النهائية...');
    
    const finalCount = await client.query('SELECT COUNT(*) as count FROM cost_centers');
    console.log(`📈 إجمالي مراكز التكلفة: ${finalCount.rows[0].count}`);

    const hierarchyResult = await client.query(`
      SELECT 
        cc.center_code,
        cc.center_name,
        cc.center_level,
        cc.is_active,
        parent.center_name as parent_name
      FROM cost_centers cc
      LEFT JOIN cost_centers parent ON cc.parent_id = parent.id
      ORDER BY cc.center_level, cc.center_code
    `);

    console.log('\n🏗️ هيكل مراكز التكلفة:');
    hierarchyResult.rows.forEach(center => {
      const indent = '  '.repeat(center.center_level - 1);
      const status = center.is_active ? '✅' : '❌';
      const parent = center.parent_name ? ` (تحت: ${center.parent_name})` : '';
      console.log(`${indent}${status} ${center.center_code}: ${center.center_name}${parent}`);
    });

    // 7. إنشاء API routes إذا لم تكن موجودة
    console.log('\n🔗 التحقق من API routes...');
    
    console.log('📋 ملاحظة: تأكد من وجود الملفات التالية:');
    console.log('   - src/app/api/cost-centers/route.ts');
    console.log('   - src/app/settings/cost-centers/page.tsx');

    console.log('\n🎉 تم إصلاح جدول مراكز التكلفة بنجاح!');
    console.log('\n📋 الفوائد:');
    console.log('✅ تصنيف دقيق للتكاليف حسب الأقسام');
    console.log('✅ تقارير مالية مفصلة حسب مركز التكلفة');
    console.log('✅ مراقبة أداء كل قسم بشكل منفصل');
    console.log('✅ تحليل ربحية الأقسام المختلفة');

  } catch (error) {
    console.error('❌ خطأ في إصلاح جدول مراكز التكلفة:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
restoreCostCenters();
