import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الحسابات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const level = searchParams.get('level')
    const includeLinked = searchParams.get('include_linked') === 'true'
    const onlyTransactional = searchParams.get('only_transactional') === 'true'

    let sql = `
      SELECT
        id,
        account_code,
        account_name,
        account_name_en,
        level_1_code,
        level_2_code,
        level_3_code,
        level_4_code,
        account_level,
        parent_id,
        account_type,
        account_nature,
        is_active,
        allow_transactions,
        linked_table,
        auto_create_sub_accounts,
        opening_balance,
        current_balance,
        description,
        created_date
      FROM chart_of_accounts
      WHERE is_active = true
    `

    const params: any[] = []
    let paramIndex = 1

    // تصفية حسب المستوى
    if (level && level !== 'all') {
      sql += ` AND account_level = $${paramIndex}`
      params.push(parseInt(level))
      paramIndex++
    }

    // تصفية للحسابات التي تقبل معاملات فقط
    if (onlyTransactional) {
      sql += ` AND allow_transactions = true`
    }

    sql += ` ORDER BY account_code`

    const result = await query(sql, params)
    let accounts = result.rows

    // إضافة الحسابات المرتبطة (العملاء والموظفين)
    if (includeLinked) {
      // البحث عن حساب تحكم العملاء
      const clientControlAccount = await query(`
        SELECT id FROM chart_of_accounts
        WHERE linked_table = 'clients' AND auto_create_sub_accounts = true
        LIMIT 1
      `)

      // البحث عن حساب تحكم الموظفين
      const employeeControlAccount = await query(`
        SELECT id FROM chart_of_accounts
        WHERE linked_table = 'employees' AND auto_create_sub_accounts = true
        LIMIT 1
      `)

      // جلب العملاء
      const clientsResult = await query(`
        SELECT
          c.id,
          c.name,
          'C' || LPAD(c.id::text, 6, '0') as account_code,
          c.name as account_name,
          c.name || ' (عميل)' as account_name_en,
          'أصول' as account_type,
          'مدين' as account_nature,
          'clients' as linked_table,
          4 as account_level,
          $1 as parent_id,
          true as allow_transactions,
          0 as opening_balance,
          0 as current_balance,
          'حساب العميل: ' || c.name as description,
          true as is_active
        FROM clients c
        WHERE c.status = 'active'
        ORDER BY c.name
      `, [clientControlAccount.rows.length > 0 ? clientControlAccount.rows[0].id : null])

      // جلب الموظفين
      const employeesResult = await query(`
        SELECT
          e.id,
          e.name,
          'E' || LPAD(e.id::text, 6, '0') as account_code,
          e.name as account_name,
          e.name || ' (موظف)' as account_name_en,
          'خصوم' as account_type,
          'دائن' as account_nature,
          'employees' as linked_table,
          4 as account_level,
          $1 as parent_id,
          true as allow_transactions,
          0 as opening_balance,
          0 as current_balance,
          'حساب الموظف: ' || e.name as description,
          true as is_active
        FROM employees e
        WHERE e.status = 'نشط'
        ORDER BY e.name
      `, [employeeControlAccount.rows.length > 0 ? employeeControlAccount.rows[0].id : null])

      // دمج النتائج
      accounts = [
        ...accounts,
        ...clientsResult.rows.map(client => ({
          ...client,
          is_linked_record: true,
          original_table: 'clients',
          external_id: client.id,
          id: `client_${client.id}` // معرف مؤقت للعرض
        })),
        ...employeesResult.rows.map(employee => ({
          ...employee,
          is_linked_record: true,
          original_table: 'employees',
          external_id: employee.id,
          id: `employee_${employee.id}` // معرف مؤقت للعرض
        }))
      ]
    }

    return NextResponse.json({
      success: true,
      accounts,
      total: accounts.length,
      message: 'تم جلب الحسابات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الحسابات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الحسابات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة حساب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      account_code,
      account_name,
      account_name_en,
      account_level,
      parent_id,
      account_type,
      account_nature = 'مدين',
      linked_table,
      auto_create_sub_accounts = false,
      opening_balance = 0,
      description
    } = body

    // التحقق من صحة البيانات
    if (!account_code || !account_name || !account_level || !account_type) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار رمز الحساب
    const existingAccount = await query(
      'SELECT id FROM chart_of_accounts WHERE account_code = $1',
      [account_code]
    )

    if (existingAccount.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رمز الحساب موجود مسبقاً'
      }, { status: 400 })
    }

    // تحديد أكواد المستويات
    let level_1_code = null, level_2_code = null, level_3_code = null, level_4_code = null

    if (account_level === 1) {
      level_1_code = account_code
    } else if (account_level === 2) {
      level_1_code = account_code.substring(0, 2)
      level_2_code = account_code
    } else if (account_level === 3) {
      level_1_code = account_code.substring(0, 2)
      level_2_code = account_code.substring(0, 4)
      level_3_code = account_code
    } else if (account_level === 4) {
      level_1_code = account_code.substring(0, 2)
      level_2_code = account_code.substring(0, 4)
      level_3_code = account_code.substring(0, 6)
      level_4_code = account_code
    }

    // تحديد إمكانية المعاملات (فقط المستوى 4)
    const allow_transactions = account_level === 4

    // إدراج الحساب الجديد
    const result = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_name_en,
        level_1_code, level_2_code, level_3_code, level_4_code,
        account_level, parent_id, account_type, account_nature,
        allow_transactions, linked_table, auto_create_sub_accounts,
        opening_balance, current_balance, description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING *
    `, [
      account_code, account_name, account_name_en,
      level_1_code, level_2_code, level_3_code, level_4_code,
      account_level, parent_id, account_type, account_nature,
      allow_transactions, linked_table, auto_create_sub_accounts,
      opening_balance, opening_balance, description
    ])

    return NextResponse.json({
      success: true,
      account: result.rows[0],
      message: 'تم إضافة الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث حساب
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      account_name,
      account_name_en,
      account_type,
      account_nature,
      linked_table,
      auto_create_sub_accounts,
      description,
      is_active = true
    } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الحساب مطلوب'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE chart_of_accounts
      SET
        account_name = $2,
        account_name_en = $3,
        account_type = $4,
        account_nature = $5,
        linked_table = $6,
        auto_create_sub_accounts = $7,
        description = $8,
        is_active = $9,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      id, account_name, account_name_en, account_type, account_nature,
      linked_table, auto_create_sub_accounts, description, is_active
    ])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      account: result.rows[0],
      message: 'تم تحديث الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
