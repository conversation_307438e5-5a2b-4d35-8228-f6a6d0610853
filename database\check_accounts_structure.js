const { Client } = require('pg');

const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function checkStructure() {
  try {
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // فحص هيكل الجدول
    const structure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'chart_of_accounts'
      ORDER BY ordinal_position
    `);
    
    console.log('\n📋 هيكل جدول chart_of_accounts:');
    structure.rows.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`);
    });
    
    // عرض البيانات الحالية
    const accounts = await client.query(`
      SELECT account_code, account_name, account_type, parent_id, account_level, is_main_account
      FROM chart_of_accounts 
      ORDER BY account_code
      LIMIT 15
    `);
    
    console.log('\n📊 عينة من الحسابات الحالية:');
    accounts.rows.forEach(acc => {
      console.log(`   ${acc.account_code}: ${acc.account_name} (مستوى: ${acc.account_level || 1}, رئيسي: ${acc.is_main_account || false})`);
    });
    
    // إحصائيات
    const stats = await client.query(`
      SELECT 
        COUNT(*) as total_accounts,
        COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as main_accounts,
        COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as sub_accounts
      FROM chart_of_accounts
    `);
    
    console.log('\n📈 إحصائيات الحسابات:');
    console.log(`   إجمالي الحسابات: ${stats.rows[0].total_accounts}`);
    console.log(`   الحسابات الرئيسية: ${stats.rows[0].main_accounts}`);
    console.log(`   الحسابات الفرعية: ${stats.rows[0].sub_accounts}`);
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
    console.log('\n🔌 تم قطع الاتصال');
  }
}

checkStructure();
