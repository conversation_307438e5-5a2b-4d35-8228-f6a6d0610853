import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع القضايا مع الربط
export async function GET() {
  try {
    const result = await query(`
      SELECT
        i.*,
        c.name as client_name,
        ct.name as court_name,
        it.name as issue_type
      FROM issues i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN courts ct ON i.court_id = ct.id
      LEFT JOIN issue_types it ON i.issue_type_id = it.id
      ORDER BY i.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching issues:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات القضايا' },
      { status: 500 }
    )
  }
}

// POST - إضافة قضية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      case_number, title, description, client_name, court_name,
      issue_type, status = 'pending', amount, next_hearing, notes,
      contract_method = 'بالجلسة'
    } = body

    // التحقق من البيانات المطلوبة
    if (!case_number || !title || !client_name) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية والعنوان واسم الموكل مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم القضية
    const existingIssue = await query(
      'SELECT id FROM issues WHERE case_number = $1',
      [case_number]
    )

    if (existingIssue.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إدراج القضية الجديدة
    const result = await query(`
      INSERT INTO issues (
        case_number, title, description, client_name, court_name,
        issue_type, status, amount, next_hearing, notes, contract_method
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      case_number, title, description, client_name, court_name,
      issue_type, status, amount, next_hearing, notes, contract_method
    ])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث قضية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, case_number, title, description, client_name, court_name,
      issue_type, status, amount, next_hearing, notes
    } = body

    if (!id || !case_number || !title || !client_name) {
      return NextResponse.json(
        { success: false, error: 'المعرف ورقم القضية والعنوان واسم الموكل مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود القضية
    const existingIssue = await query(
      'SELECT id FROM issues WHERE id = $1',
      [id]
    )

    if (existingIssue.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    // تحديث بيانات القضية
    const result = await query(`
      UPDATE issues
      SET case_number = $1, title = $2, description = $3, client_name = $4,
          court_name = $5, issue_type = $6, status = $7, amount = $8,
          next_hearing = $9, notes = $10, updated_at = CURRENT_TIMESTAMP
      WHERE id = $11
      RETURNING *
    `, [
      case_number, title, description, client_name, court_name,
      issue_type, status, amount, next_hearing, notes, id
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات القضية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف قضية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف القضية مطلوب' },
        { status: 400 }
      )
    }

    // حذف القضية
    const result = await query(
      'DELETE FROM issues WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف القضية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف القضية' },
      { status: 500 }
    )
  }
}
