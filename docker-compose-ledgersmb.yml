version: '3.8'

services:
  # PostgreSQL Database for LedgerSMB
  ledgersmb-db:
    image: postgres:15
    container_name: ledgersmb-postgres
    environment:
      POSTGRES_DB: ledgersmb
      POSTGRES_USER: ledgersmb
      POSTGRES_PASSWORD: ledgersmb123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"
    volumes:
      - ledgersmb_data:/var/lib/postgresql/data
    networks:
      - ledgersmb-network
    restart: unless-stopped

  # LedgerSMB Application
  ledgersmb:
    image: ledgersmb/ledgersmb:latest
    container_name: ledgersmb-app
    environment:
      - POSTGRES_HOST=ledgersmb-db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=ledgersmb
      - POSTGRES_USER=ledgersmb
      - POSTGRES_PASSWORD=ledgersmb123
      - LSMB_ADMIN_USERNAME=admin
      - LSMB_ADMIN_PASSWORD=admin123
      - LSMB_COMPANY_NAME=Legal System
    ports:
      - "5762:5762"
    depends_on:
      - ledgersmb-db
    networks:
      - ledgersmb-network
    restart: unless-stopped
    volumes:
      - ledgersmb_config:/opt/ledgersmb/conf
      - ledgersmb_logs:/opt/ledgersmb/logs

volumes:
  ledgersmb_data:
  ledgersmb_config:
  ledgersmb_logs:

networks:
  ledgersmb-network:
    driver: bridge
