'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Clock,
  User,
  FileText
} from 'lucide-react'

interface Follow {
  id: number
  case_number: string
  case_title: string
  client_name: string
  follow_type: 'hearing' | 'document' | 'payment' | 'meeting'
  description: string
  due_date: string
  status: 'pending' | 'completed' | 'overdue'
  priority: 'low' | 'medium' | 'high'
  created_date: string
}

export default function FollowsPage() {
  const [follows, setFollows] = useState<Follow[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingFollow, setEditingFollow] = useState<Follow | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchFollows = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/follows')
      const result = await response.json()

      if (result.success) {
        setFollows(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات المتابعات')
        setFollows([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setFollows([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchFollows()
  }, [])

  const filteredFollows = follows.filter(follow =>
    (follow.case_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (follow.case_title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (follow.client_name || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلقة'
      case 'completed': return 'مكتملة'
      case 'overdue': return 'متأخرة'
      default: return 'غير محدد'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high': return 'عالية'
      case 'medium': return 'متوسطة'
      case 'low': return 'منخفضة'
      default: return 'غير محدد'
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'hearing': return 'جلسة محاكمة'
      case 'document': return 'مستندات'
      case 'payment': return 'دفعة مالية'
      case 'meeting': return 'اجتماع'
      default: return 'غير محدد'
    }
  }

  const handleDelete = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه المتابعة؟')) {
      setFollows(follows.filter(follow => follow.id !== id))
    }
  }

  const handleEdit = (follow: Follow) => {
    setEditingFollow(follow)
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingFollow(null)
    setIsModalOpen(true)
  }

  const stats = {
    total: follows.length,
    pending: follows.filter(f => f.status === 'pending').length,
    completed: follows.filter(f => f.status === 'completed').length,
    overdue: follows.filter(f => f.status === 'overdue').length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Calendar className="h-8 w-8 mr-3 text-blue-600" />
              إدارة المتابعات
            </h1>
            <p className="text-gray-600 mt-1">متابعة المهام والمواعيد المتعلقة بالقضايا</p>
          </div>

          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة متابعة جديدة
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي المتابعات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.pending}</div>
                  <div className="text-sm text-gray-600">معلقة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.completed}</div>
                  <div className="text-sm text-gray-600">مكتملة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Clock className="h-6 w-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.overdue}</div>
                  <div className="text-sm text-gray-600">متأخرة</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في المتابعات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              قائمة المتابعات ({filteredFollows.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3 font-semibold">رقم القضية</th>
                    <th className="text-right p-3 font-semibold">عنوان القضية</th>
                    <th className="text-right p-3 font-semibold">الموكل</th>
                    <th className="text-right p-3 font-semibold">نوع المتابعة</th>
                    <th className="text-right p-3 font-semibold">الوصف</th>
                    <th className="text-center p-3 font-semibold">تاريخ الاستحقاق</th>
                    <th className="text-center p-3 font-semibold">الأولوية</th>
                    <th className="text-center p-3 font-semibold">الحالة</th>
                    <th className="text-center p-3 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredFollows.map((follow) => (
                    <tr key={follow.id} className="border-b hover:bg-gray-50">
                      <td className="p-3 font-medium text-blue-600">{follow.case_number}</td>
                      <td className="p-3">{follow.case_title}</td>
                      <td className="p-3 flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-400" />
                        {follow.client_name}
                      </td>
                      <td className="p-3">
                        <Badge variant="outline">{getTypeText(follow.follow_type)}</Badge>
                      </td>
                      <td className="p-3">{follow.description}</td>
                      <td className="text-center p-3">
                        <div className="flex items-center justify-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                          {follow.due_date}
                        </div>
                      </td>
                      <td className="text-center p-3">
                        <Badge className={getPriorityColor(follow.priority)}>
                          {getPriorityText(follow.priority)}
                        </Badge>
                      </td>
                      <td className="text-center p-3">
                        <Badge className={getStatusColor(follow.status)}>
                          {getStatusText(follow.status)}
                        </Badge>
                      </td>
                      <td className="text-center p-3">
                        <div className="flex justify-center space-x-2 space-x-reverse">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleEdit(follow)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(follow.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {editingFollow ? 'تعديل المتابعة' : 'إضافة متابعة جديدة'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-gray-600">سيتم إضافة نموذج التعديل هنا</p>
              <div className="flex justify-end mt-4">
                <Button onClick={() => setIsModalOpen(false)}>إغلاق</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
