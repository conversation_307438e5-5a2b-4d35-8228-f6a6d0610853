import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const {
      voucher_number,
      voucher_date,
      cost_center_id,
      debit_account_id,
      debit_amount,
      currency_id,
      description,
      credit_lines
    } = data

    // التحقق من البيانات الأساسية
    if (!voucher_number || !voucher_date || !cost_center_id || !debit_account_id || !debit_amount) {
      return NextResponse.json(
        { success: false, error: 'البيانات الأساسية مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من المعاملات الدائنة
    if (!credit_lines || credit_lines.length === 0) {
      return NextResponse.json(
        { success: false, error: 'يجب إضافة معاملة دائنة واحدة على الأقل' },
        { status: 400 }
      )
    }

    // التحقق من التوازن
    const totalCredit = credit_lines.reduce((sum: number, line: any) => sum + (line.amount || 0), 0)
    if (Math.abs(debit_amount - totalCredit) > 0.01) {
      return NextResponse.json(
        { success: false, error: `المبالغ غير متوازنة - مدين: ${debit_amount}, دائن: ${totalCredit}` },
        { status: 400 }
      )
    }

    // بدء المعاملة
    await query('BEGIN')

    try {
      // 1. إدراج السند الرئيسي
      const voucherResult = await query(`
        INSERT INTO vouchers_master (
          voucher_number, voucher_date, voucher_type, cost_center_id,
          total_amount, description, status, created_by
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id
      `, [
        voucher_number,
        voucher_date,
        'سند قبض',
        cost_center_id,
        debit_amount,
        description,
        'مسودة',
        1
      ])

      const voucherId = voucherResult.rows[0].id

      // 2. إدراج المعاملة المدينة
      await query(`
        INSERT INTO financial_transactions (
          voucher_id, debit_amount, credit_amount,
          debit_account_id, credit_account_id,
          transaction_date, cost_center_id, line_number,
          currency_id, exchange_rate
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        voucherId,
        debit_amount,  // المبلغ المدين
        0,             // المبلغ الدائن = 0
        debit_account_id, // الحساب المدين
        null,          // لا يوجد حساب دائن
        voucher_date,
        cost_center_id,
        1,             // رقم السطر
        currency_id,
        1.0000         // سعر الصرف
      ])

      // 3. إدراج المعاملات الدائنة
      let lineNumber = 2
      for (const line of credit_lines) {
        if (line.account_id && line.amount > 0) {
          await query(`
            INSERT INTO financial_transactions (
              voucher_id, debit_amount, credit_amount,
              debit_account_id, credit_account_id,
              transaction_date, cost_center_id, line_number,
              currency_id, exchange_rate
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
          `, [
            voucherId,
            0,                // المبلغ المدين = 0
            line.amount,      // المبلغ الدائن
            null,             // لا يوجد حساب مدين
            line.account_id,  // الحساب الدائن
            voucher_date,
            cost_center_id,
            lineNumber++,
            line.currency_id || currency_id,
            1.0000
          ])
        }
      }

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حفظ سند القبض بنجاح',
        voucher_id: voucherId
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error saving simple receipt:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في حفظ سند القبض',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}
