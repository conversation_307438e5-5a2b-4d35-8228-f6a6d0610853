'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  FileText,
  Save,
  ArrowRight,
  User,
  Building,
  Calendar,
  DollarSign,
  Scale,
  ArrowLeft
} from 'lucide-react'

export default function NewIssuePage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    case_number: '',
    title: '',
    description: '',
    client_id: '',
    client_name: '',
    court_name: '',
    issue_type: '',
    status: 'pending',
    amount: '',
    notes: '',
    contract_method: 'بالجلسة',
    contract_date: new Date().toISOString().split('T')[0]
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (result.success) {
        alert('تم إنشاء القضية بنجاح!')
        router.push('/issues')
      } else {
        alert(result.error || 'فشل في إنشاء القضية')
      }
    } catch (error) {
      console.error('Error creating issue:', error)
      alert('حدث خطأ أثناء إنشاء القضية')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <FileText className="h-8 w-8 mr-3 text-blue-600" />
              إضافة قضية جديدة
            </h1>
            <p className="text-gray-600 mt-1">إنشاء قضية قانونية جديدة في النظام</p>
          </div>

          <Link href="/issues">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة للقائمة
            </Button>
          </Link>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* معلومات القضية الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Scale className="h-5 w-5 mr-2" />
                معلومات القضية الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="case_number">رقم القضية *</Label>
                  <Input
                    id="case_number"
                    name="case_number"
                    value={formData.case_number}
                    onChange={handleInputChange}
                    placeholder="مثال: 2024-001"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="issue_type">نوع القضية *</Label>
                  <select
                    id="issue_type"
                    name="issue_type"
                    value={formData.issue_type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">اختر نوع القضية</option>
                    <option value="تجاري">تجاري</option>
                    <option value="مدني">مدني</option>
                    <option value="جنائي">جنائي</option>
                    <option value="عمالي">عمالي</option>
                    <option value="عقاري">عقاري</option>
                    <option value="أحوال شخصية">أحوال شخصية</option>
                    <option value="إداري">إداري</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="title">عنوان القضية *</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="أدخل عنوان القضية"
                  required
                />
              </div>

              <div>
                <Label htmlFor="description">وصف القضية</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="أدخل وصف تفصيلي للقضية"
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* معلومات الموكل والمحكمة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                معلومات الموكل والمحكمة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="client_name">اسم الموكل *</Label>
                  <Input
                    id="client_name"
                    name="client_name"
                    value={formData.client_name}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم الموكل"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="court_name">المحكمة</Label>
                  <Input
                    id="court_name"
                    name="court_name"
                    value={formData.court_name}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم المحكمة"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* المعلومات المالية والتعاقد */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="h-5 w-5 mr-2" />
                المعلومات المالية والتعاقد
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="amount">قيمة القضية (ريال)</Label>
                  <Input
                    id="amount"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    placeholder="أدخل قيمة القضية"
                    type="number"
                    min="0"
                    step="1"
                  />
                </div>
                <div>
                  <Label htmlFor="contract_method">طريقة التعاقد</Label>
                  <select
                    id="contract_method"
                    name="contract_method"
                    value={formData.contract_method}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="بالجلسة">بالجلسة</option>
                    <option value="بالعقد">بالعقد</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="contract_date">تاريخ التعاقد</Label>
                  <Input
                    id="contract_date"
                    name="contract_date"
                    value={formData.contract_date}
                    onChange={handleInputChange}
                    type="date"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ملاحظات إضافية */}
          <Card>
            <CardHeader>
              <CardTitle>ملاحظات إضافية</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="أدخل أي ملاحظات إضافية"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* أزرار الحفظ */}
          <div className="flex justify-end space-x-4 space-x-reverse">
            <Link href="/issues">
              <Button type="button" variant="outline">
                إلغاء
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                'جاري الحفظ...'
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ القضية
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </MainLayout>
  )
}
