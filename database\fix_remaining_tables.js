// إصلاح الجداول المتبقية وإكمال النسخ
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function fixRemainingTables() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إصلاح جدول الموكلين
    console.log('🔄 جاري إصلاح جدول الموكلين...');
    
    // التحقق من الأعمدة الموجودة
    const clientsColumns = await client.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'clients'
    `);
    
    const existingColumns = clientsColumns.rows.map(row => row.column_name);
    console.log('📋 أعمدة جدول الموكلين الحالية:', existingColumns);

    // إضافة الأعمدة المفقودة
    if (!existingColumns.includes('client_type')) {
      await client.query('ALTER TABLE clients ADD COLUMN client_type VARCHAR(100)');
      console.log('✅ تم إضافة عمود client_type');
    }

    if (!existingColumns.includes('id_number')) {
      await client.query('ALTER TABLE clients ADD COLUMN id_number VARCHAR(50)');
      console.log('✅ تم إضافة عمود id_number');
    }

    // نسخ بيانات الموكلين
    await client.query('TRUNCATE TABLE clients RESTART IDENTITY CASCADE');
    const clientsData = [
      { name: 'أحمد محمد سالم', phone: '777111222', email: '<EMAIL>', address: 'صنعاء - شارع الستين', id_number: '12345678901', client_type: 'فرد' },
      { name: 'شركة النور للتجارة', phone: '777222333', email: '<EMAIL>', address: 'عدن - كريتر', id_number: '98765432109', client_type: 'شركة' },
      { name: 'فاطمة علي أحمد', phone: '777333444', email: '<EMAIL>', address: 'تعز - شارع جمال', id_number: '11122233344', client_type: 'فرد' },
      { name: 'مؤسسة الأمل', phone: '777444555', email: '<EMAIL>', address: 'الحديدة - الكورنيش', id_number: '55566677788', client_type: 'مؤسسة' }
    ];

    for (const client_data of clientsData) {
      await client.query(`
        INSERT INTO clients (name, phone, email, address, id_number, client_type)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [client_data.name, client_data.phone, client_data.email, client_data.address, client_data.id_number, client_data.client_type]);
    }
    console.log(`✅ تم نسخ ${clientsData.length} موكل`);

    // نسخ بيانات القضايا
    console.log('🔄 جاري نسخ بيانات القضايا...');
    await client.query('TRUNCATE TABLE issues RESTART IDENTITY CASCADE');
    const issuesData = [
      { title: 'قضية تجارية - شركة الأمل', case_number: 'C2024001', client_id: 2, issue_type_id: 1, court_id: 1, case_amount: 500000, status: 'جارية', start_date: '2024-01-15' },
      { title: 'قضية عقارية - النزاع العقاري', case_number: 'C2024002', client_id: 1, issue_type_id: 1, court_id: 2, case_amount: 750000, status: 'جارية', start_date: '2024-02-10' },
      { title: 'قضية أحوال شخصية - طلاق', case_number: 'C2024003', client_id: 3, issue_type_id: 3, court_id: 4, case_amount: 50000, status: 'مكتملة', start_date: '2024-01-20' },
      { title: 'قضية عمالية - حقوق عامل', case_number: 'C2024004', client_id: 4, issue_type_id: 4, court_id: 3, case_amount: 100000, status: 'جارية', start_date: '2024-03-05' }
    ];

    for (const issue of issuesData) {
      await client.query(`
        INSERT INTO issues (title, case_number, client_id, issue_type_id, court_id, case_amount, status, start_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [issue.title, issue.case_number, issue.client_id, issue.issue_type_id, issue.court_id, issue.case_amount, issue.status, issue.start_date]);
    }
    console.log(`✅ تم نسخ ${issuesData.length} قضية`);

    // نسخ بيانات المستخدمين
    console.log('🔄 جاري نسخ بيانات المستخدمين...');
    await client.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE');
    const usersData = [
      { username: 'admin', email: '<EMAIL>', full_name: 'مدير النظام', role: 'admin', employee_id: 1, is_active: true },
      { username: 'lawyer1', email: '<EMAIL>', full_name: 'ماجد أحمد علي', role: 'lawyer', employee_id: 1, is_active: true },
      { username: 'lawyer2', email: '<EMAIL>', full_name: 'يحيى علي محمد', role: 'lawyer', employee_id: 2, is_active: true },
      { username: 'assistant', email: '<EMAIL>', full_name: 'أحمد صالح حسن', role: 'assistant', employee_id: 3, is_active: true }
    ];

    for (const user of usersData) {
      await client.query(`
        INSERT INTO users (username, email, full_name, role, employee_id, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [user.username, user.email, user.full_name, user.role, user.employee_id, user.is_active]);
    }
    console.log(`✅ تم نسخ ${usersData.length} مستخدم`);

    // نسخ بيانات الشركة
    console.log('🔄 جاري نسخ بيانات الشركة...');
    await client.query('TRUNCATE TABLE companies RESTART IDENTITY CASCADE');
    const companiesData = [
      { name: 'مكتب المحاماة والاستشارات القانونية', address: 'صنعاء - شارع الزبيري', phone: '01-123456', email: '<EMAIL>', website: 'www.legal.com', license_number: 'LAW2020001', established_date: '2020-01-01' }
    ];

    for (const company of companiesData) {
      await client.query(`
        INSERT INTO companies (name, address, phone, email, website, license_number, established_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [company.name, company.address, company.phone, company.email, company.website, company.license_number, company.established_date]);
    }
    console.log(`✅ تم نسخ ${companiesData.length} شركة`);

    // إنشاء جداول إضافية مطلوبة
    console.log('🔄 جاري إنشاء الجداول الإضافية...');

    // جدول المتابعات
    await client.query(`
      CREATE TABLE IF NOT EXISTS follows (
        id SERIAL PRIMARY KEY,
        issue_id INTEGER REFERENCES issues(id),
        follow_date DATE NOT NULL,
        follow_type VARCHAR(100),
        description TEXT,
        next_follow_date DATE,
        employee_id INTEGER REFERENCES employees(id),
        status VARCHAR(100),
        created_date DATE DEFAULT CURRENT_DATE
      )
    `);

    // جدول الحركات
    await client.query(`
      CREATE TABLE IF NOT EXISTS movements (
        id SERIAL PRIMARY KEY,
        issue_id INTEGER REFERENCES issues(id),
        movement_date DATE NOT NULL,
        movement_type VARCHAR(100),
        description TEXT,
        court_id INTEGER REFERENCES courts(id),
        employee_id INTEGER REFERENCES employees(id),
        created_date DATE DEFAULT CURRENT_DATE
      )
    `);

    // جدول القيود اليومية
    await client.query(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id SERIAL PRIMARY KEY,
        entry_number VARCHAR(50) UNIQUE,
        entry_date DATE NOT NULL,
        description TEXT,
        total_debit DECIMAL(15,2),
        total_credit DECIMAL(15,2),
        reference_number VARCHAR(100),
        created_date DATE DEFAULT CURRENT_DATE
      )
    `);

    console.log('✅ تم إنشاء الجداول الإضافية');

    // التحقق من النتائج النهائية
    console.log('🔄 جاري التحقق من النتائج النهائية...');
    
    const finalResults = await Promise.all([
      client.query('SELECT COUNT(*) FROM governorates'),
      client.query('SELECT COUNT(*) FROM branches'),
      client.query('SELECT COUNT(*) FROM issue_types'),
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues'),
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM companies'),
      client.query('SELECT COUNT(*) FROM lineages'),
      client.query('SELECT COUNT(*) FROM services'),
      client.query('SELECT COUNT(*) FROM opening_balances')
    ]);

    console.log('📋 ملخص جميع البيانات في قاعدة البيانات:');
    console.log(`   ✅ المحافظات: ${finalResults[0].rows[0].count} سجل`);
    console.log(`   ✅ الفروع: ${finalResults[1].rows[0].count} سجل`);
    console.log(`   ✅ أنواع القضايا: ${finalResults[2].rows[0].count} سجل`);
    console.log(`   ✅ الموظفين: ${finalResults[3].rows[0].count} سجل`);
    console.log(`   ✅ الموكلين: ${finalResults[4].rows[0].count} سجل`);
    console.log(`   ✅ القضايا: ${finalResults[5].rows[0].count} سجل`);
    console.log(`   ✅ المستخدمين: ${finalResults[6].rows[0].count} سجل`);
    console.log(`   ✅ بيانات الشركة: ${finalResults[7].rows[0].count} سجل`);
    console.log(`   ✅ النسب المالية: ${finalResults[8].rows[0].count} سجل`);
    console.log(`   ✅ الخدمات: ${finalResults[9].rows[0].count} سجل`);
    console.log(`   ✅ الأرصدة الافتتاحية: ${finalResults[10].rows[0].count} سجل`);

    console.log('🎉 تم إصلاح وإكمال جميع الجداول بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح الجداول:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإصلاح
fixRemainingTables();
