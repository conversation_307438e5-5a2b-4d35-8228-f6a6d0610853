const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function fixForeignKeys() {
  try {
    console.log('🔧 إصلاح المفاتيح الخارجية...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. حذف المفاتيح الخارجية الموجودة (إن وجدت)
    console.log('🔄 جاري حذف المفاتيح الخارجية القديمة...');
    
    try {
      await client.query(`
        ALTER TABLE clients 
        DROP CONSTRAINT IF EXISTS clients_account_id_fkey
      `);
      console.log('   ✅ تم حذف المفتاح الخارجي القديم للعملاء');
    } catch (error) {
      console.log('   ⚠️ لم يتم العثور على مفتاح خارجي قديم للعملاء');
    }

    try {
      await client.query(`
        ALTER TABLE employees 
        DROP CONSTRAINT IF EXISTS employees_account_id_fkey
      `);
      console.log('   ✅ تم حذف المفتاح الخارجي القديم للموظفين');
    } catch (error) {
      console.log('   ⚠️ لم يتم العثور على مفتاح خارجي قديم للموظفين');
    }

    // 2. إنشاء المفاتيح الخارجية الجديدة
    console.log('🔄 جاري إنشاء المفاتيح الخارجية الجديدة...');
    
    // مفتاح خارجي للعملاء
    await client.query(`
      ALTER TABLE clients 
      ADD CONSTRAINT fk_clients_account_linking_settings
      FOREIGN KEY (account_id) 
      REFERENCES account_linking_settings(id) 
      ON DELETE SET NULL 
      ON UPDATE CASCADE
    `);
    console.log('   ✅ تم إنشاء المفتاح الخارجي للعملاء');

    // مفتاح خارجي للموظفين
    await client.query(`
      ALTER TABLE employees 
      ADD CONSTRAINT fk_employees_account_linking_settings
      FOREIGN KEY (account_id) 
      REFERENCES account_linking_settings(id) 
      ON DELETE SET NULL 
      ON UPDATE CASCADE
    `);
    console.log('   ✅ تم إنشاء المفتاح الخارجي للموظفين');

    // 3. التحقق من المفاتيح الخارجية الجديدة
    console.log('🔍 التحقق من المفاتيح الخارجية الجديدة...');
    
    const foreignKeys = await client.query(`
      SELECT 
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        tc.constraint_name,
        rc.delete_rule,
        rc.update_rule
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      JOIN information_schema.referential_constraints AS rc
        ON tc.constraint_name = rc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND (tc.table_name = 'clients' OR tc.table_name = 'employees')
        AND kcu.column_name = 'account_id'
      ORDER BY tc.table_name
    `);
    
    if (foreignKeys.rows.length > 0) {
      console.log('   ✅ المفاتيح الخارجية المُنشأة:');
      foreignKeys.rows.forEach(fk => {
        console.log(`      ${fk.table_name}.${fk.column_name} → ${fk.foreign_table_name}.${fk.foreign_column_name}`);
        console.log(`         القيد: ${fk.constraint_name}`);
        console.log(`         عند الحذف: ${fk.delete_rule}, عند التحديث: ${fk.update_rule}`);
      });
    } else {
      console.log('   ❌ لم يتم العثور على مفاتيح خارجية');
    }

    // 4. اختبار سلامة البيانات
    console.log('🧪 اختبار سلامة البيانات...');
    
    // التحقق من أن جميع account_id في العملاء موجودة في account_linking_settings
    const invalidClients = await client.query(`
      SELECT c.id, c.name, c.account_id
      FROM clients c
      WHERE c.account_id IS NOT NULL 
        AND NOT EXISTS (
          SELECT 1 FROM account_linking_settings als 
          WHERE als.id = c.account_id
        )
    `);
    
    if (invalidClients.rows.length === 0) {
      console.log('   ✅ جميع العملاء مربوطين بإعدادات صحيحة');
    } else {
      console.log('   ❌ عملاء مربوطين بإعدادات غير موجودة:');
      invalidClients.rows.forEach(client => {
        console.log(`      ${client.id}: ${client.name} (account_id: ${client.account_id})`);
      });
    }

    // التحقق من أن جميع account_id في الموظفين موجودة في account_linking_settings
    const invalidEmployees = await client.query(`
      SELECT e.id, e.name, e.account_id
      FROM employees e
      WHERE e.account_id IS NOT NULL 
        AND NOT EXISTS (
          SELECT 1 FROM account_linking_settings als 
          WHERE als.id = e.account_id
        )
    `);
    
    if (invalidEmployees.rows.length === 0) {
      console.log('   ✅ جميع الموظفين مربوطين بإعدادات صحيحة');
    } else {
      console.log('   ❌ موظفين مربوطين بإعدادات غير موجودة:');
      invalidEmployees.rows.forEach(employee => {
        console.log(`      ${employee.id}: ${employee.name} (account_id: ${employee.account_id})`);
      });
    }

    // 5. إنشاء فهارس إضافية للأداء
    console.log('🔄 إنشاء فهارس إضافية للأداء...');
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_clients_account_id_not_null 
      ON clients(account_id) 
      WHERE account_id IS NOT NULL
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_employees_account_id_not_null 
      ON employees(account_id) 
      WHERE account_id IS NOT NULL
    `);
    
    console.log('   ✅ تم إنشاء الفهارس الإضافية');

    // 6. عرض ملخص العلاقات
    console.log('\n📊 ملخص العلاقات النهائي:');
    
    const relationshipSummary = await client.query(`
      SELECT 
        'account_linking_settings' as parent_table,
        'clients' as child_table,
        COUNT(c.account_id) as linked_records
      FROM account_linking_settings als
      LEFT JOIN clients c ON als.id = c.account_id
      WHERE als.table_name = 'clients'
      GROUP BY als.id
      
      UNION ALL
      
      SELECT 
        'account_linking_settings' as parent_table,
        'employees' as child_table,
        COUNT(e.account_id) as linked_records
      FROM account_linking_settings als
      LEFT JOIN employees e ON als.id = e.account_id
      WHERE als.table_name = 'employees'
      GROUP BY als.id
    `);
    
    relationshipSummary.rows.forEach(rel => {
      console.log(`   ${rel.parent_table} ← ${rel.child_table}: ${rel.linked_records} سجل مربوط`);
    });

    console.log('\n✅ تم إصلاح المفاتيح الخارجية بنجاح!');
    console.log('\n🎯 نوع العلاقة المُنشأة:');
    console.log('   📋 account_linking_settings (الجدول الأب)');
    console.log('   ├── clients (جدول فرعي) - علاقة One-to-Many');
    console.log('   └── employees (جدول فرعي) - علاقة One-to-Many');
    console.log('\n   كل سجل في account_linking_settings يمكن أن يرتبط بعدة عملاء/موظفين');
    console.log('   كل عميل/موظف يرتبط بسجل واحد فقط في account_linking_settings');

  } catch (error) {
    console.error('❌ خطأ في إصلاح المفاتيح الخارجية:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  fixForeignKeys()
    .then(() => {
      console.log('🎉 تم إنجاز إصلاح المفاتيح الخارجية بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في إصلاح المفاتيح الخارجية:', error);
      process.exit(1);
    });
}

module.exports = { fixForeignKeys };
