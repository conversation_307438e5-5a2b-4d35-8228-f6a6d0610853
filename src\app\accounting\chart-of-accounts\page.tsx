'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BookOpen, Plus, Edit, Trash2, Search, ChevronRight, ChevronDown, Users, Building } from 'lucide-react'

interface Account {
  id: number | string
  account_code: string
  account_name: string
  account_name_en?: string
  account_level: number
  account_type: string
  account_nature: string
  parent_id?: number
  allow_transactions: boolean
  linked_table?: string
  auto_create_sub_accounts: boolean
  opening_balance: number
  current_balance: number
  is_active: boolean
  description?: string
  is_linked_record?: boolean
  original_table?: string
  external_id?: number
  children?: Account[]
}

export default function ChartOfAccountsPage() {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [filteredAccounts, setFilteredAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLevel, setSelectedLevel] = useState<string>('all')
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set())
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingAccount, setEditingAccount] = useState<Account | null>(null)
  const [parentAccount, setParentAccount] = useState<Account | null>(null)

  // بيانات النموذج
  const [formData, setFormData] = useState({
    account_code: '',
    account_name: '',
    account_name_en: '',
    account_type: '',
    account_nature: 'مدين',
    linked_table: 'none',
    auto_create_sub_accounts: false,
    description: ''
  })

  // جلب الحسابات من API
  useEffect(() => {
    fetchAccounts()
  }, [])

  const fetchAccounts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/accounting/chart-of-accounts?include_linked=true')
      if (response.ok) {
        const data = await response.json()
        const hierarchicalAccounts = buildAccountHierarchy(data.accounts || [])
        setAccounts(hierarchicalAccounts)
        setFilteredAccounts(hierarchicalAccounts)
      }
    } catch (error) {
      console.error('خطأ في جلب الحسابات:', error)
    } finally {
      setLoading(false)
    }
  }

  // حفظ الحساب
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = editingAccount
        ? `/api/accounting/chart-of-accounts/${editingAccount.id}`
        : '/api/accounting/chart-of-accounts'

      const method = editingAccount ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          linked_table: formData.linked_table === 'none' ? null : formData.linked_table,
          parent_id: parentAccount?.id || null,
          account_level: parentAccount ? parentAccount.account_level + 1 : 1,
          auto_create_sub_accounts: formData.auto_create_sub_accounts
        }),
      })

      if (response.ok) {
        await fetchAccounts()
        setShowAddDialog(false)
        setEditingAccount(null)
        setParentAccount(null)
        resetForm()
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حفظ الحساب:', error)
      alert('حدث خطأ أثناء حفظ الحساب')
    }
  }

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      account_code: '',
      account_name: '',
      account_name_en: '',
      account_type: '',
      account_nature: 'مدين',
      linked_table: 'none',
      auto_create_sub_accounts: false,
      description: ''
    })
  }

  // تعديل حساب
  const handleEdit = (account: Account) => {
    setEditingAccount(account)
    setFormData({
      account_code: account.account_code,
      account_name: account.account_name,
      account_name_en: account.account_name_en || '',
      account_type: account.account_type,
      account_nature: account.account_nature,
      linked_table: account.linked_table || 'none',
      auto_create_sub_accounts: account.auto_create_sub_accounts || false,
      description: account.description || ''
    })
    setShowAddDialog(true)
  }

  // إضافة حساب فرعي
  const handleAddSubAccount = (parent: Account) => {
    if (parent.account_level >= 4) {
      alert('لا يمكن إضافة حسابات فرعية للمستوى الرابع')
      return
    }

    setParentAccount(parent)
    setEditingAccount(null)
    resetForm()

    // تعيين القيم الافتراضية بناءً على الحساب الأب
    setFormData(prev => ({
      ...prev,
      account_type: parent.account_type,
      account_nature: parent.account_nature
    }))

    setShowAddDialog(true)
  }

  // حذف حساب
  const handleDelete = async (account: Account) => {
    if (!confirm(`هل أنت متأكد من حذف الحساب "${account.account_name}"؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء.`)) {
      return
    }

    try {
      const response = await fetch(`/api/accounting/chart-of-accounts/${account.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchAccounts()
        alert('تم حذف الحساب بنجاح')
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حذف الحساب:', error)
      alert('حدث خطأ أثناء حذف الحساب')
    }
  }

  // بناء الهيكل الهرمي للحسابات
  const buildAccountHierarchy = (flatAccounts: Account[]): Account[] => {
    const accountMap = new Map<number, Account>()
    const rootAccounts: Account[] = []

    // إنشاء خريطة للحسابات
    flatAccounts.forEach(account => {
      accountMap.set(account.id, { ...account, children: [] })
    })

    // بناء الهيكل الهرمي
    flatAccounts.forEach(account => {
      const accountWithChildren = accountMap.get(account.id)!
      if (account.parent_id && accountMap.has(account.parent_id)) {
        const parent = accountMap.get(account.parent_id)!
        parent.children!.push(accountWithChildren)
      } else {
        rootAccounts.push(accountWithChildren)
      }
    })

    return rootAccounts
  }

  // تصفية الحسابات
  useEffect(() => {
    let filtered = accounts

    if (searchTerm) {
      filtered = filterAccountsBySearch(accounts, searchTerm)
    }

    if (selectedLevel !== 'all') {
      filtered = filterAccountsByLevel(filtered, parseInt(selectedLevel))
    }

    setFilteredAccounts(filtered)
  }, [accounts, searchTerm, selectedLevel])

  const filterAccountsBySearch = (accountList: Account[], term: string): Account[] => {
    const result: Account[] = []

    accountList.forEach(account => {
      const matchesSearch =
        account.account_name.toLowerCase().includes(term.toLowerCase()) ||
        account.account_code.includes(term) ||
        (account.account_name_en && account.account_name_en.toLowerCase().includes(term.toLowerCase()))

      const filteredChildren = account.children ? filterAccountsBySearch(account.children, term) : []

      if (matchesSearch || filteredChildren.length > 0) {
        result.push({
          ...account,
          children: filteredChildren
        })
      }
    })

    return result
  }

  const filterAccountsByLevel = (accountList: Account[], level: number): Account[] => {
    const result: Account[] = []

    accountList.forEach(account => {
      if (account.account_level === level) {
        result.push(account)
      } else if (account.children) {
        const filteredChildren = filterAccountsByLevel(account.children, level)
        if (filteredChildren.length > 0) {
          result.push({
            ...account,
            children: filteredChildren
          })
        }
      }
    })

    return result
  }

  // تبديل حالة التوسع
  const toggleExpanded = (accountId: number) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(accountId)) {
      newExpanded.delete(accountId)
    } else {
      newExpanded.add(accountId)
    }
    setExpandedNodes(newExpanded)
  }

  // عرض الحساب الواحد
  const renderAccount = (account: Account, depth: number = 0) => {
    const hasChildren = account.children && account.children.length > 0
    const isExpanded = expandedNodes.has(account.id)
    const indentStyle = { marginLeft: `${depth * 24}px` }

    return (
      <div key={account.id} className="border-b border-gray-100">
        <div className="flex items-center justify-between p-3 hover:bg-gray-50" style={indentStyle}>
          <div className="flex items-center space-x-3 space-x-reverse">
            {hasChildren && (
              <button
                onClick={() => toggleExpanded(account.id)}
                className="p-1 hover:bg-gray-200 rounded"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </button>
            )}
            {!hasChildren && <div className="w-6" />}

            <div className="flex flex-col">
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="font-mono text-sm text-blue-600">{account.account_code}</span>
                <span className="font-medium">{account.account_name}</span>
                {account.linked_table && (
                  <Badge variant="outline" className="text-xs">
                    {account.linked_table === 'clients' ? (
                      <><Users className="h-3 w-3 ml-1" /> عملاء</>
                    ) : account.linked_table === 'employees' ? (
                      <><Building className="h-3 w-3 ml-1" /> موظفين</>
                    ) : (
                      account.linked_table
                    )}
                  </Badge>
                )}
                {account.is_linked_record && (
                  <Badge variant="secondary" className="text-xs">
                    {account.original_table === 'clients' ? (
                      <><Users className="h-3 w-3 ml-1" /> عميل</>
                    ) : (
                      <><Building className="h-3 w-3 ml-1" /> موظف</>
                    )}
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                <span>المستوى {account.account_level}</span>
                <span>{account.account_type}</span>
                <span>{account.account_nature}</span>
                {account.allow_transactions && (
                  <Badge variant="secondary" className="text-xs">يقبل معاملات</Badge>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            {account.current_balance !== 0 && (
              <span className={`text-sm font-medium ${
                account.current_balance > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {account.current_balance.toLocaleString()} ر.ي
              </span>
            )}

            <div className="flex space-x-1 space-x-reverse">
              {!account.is_linked_record && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEdit(account)}
                    title="تعديل الحساب"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(account)}
                    title="حذف الحساب"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </>
              )}
              {account.account_level < 4 && !account.is_linked_record && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAddSubAccount(account)}
                  title="إضافة حساب فرعي"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div>
            {account.children!.map(child => renderAccount(child, depth + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <BookOpen className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">دليل الحسابات</h1>
              <p className="text-gray-600">إدارة الهيكل المحاسبي للشركة (4 مستويات)</p>
            </div>
          </div>

          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 ml-2" />
            إضافة حساب جديد
          </Button>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="flex space-x-4 space-x-reverse">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الحسابات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="تصفية حسب المستوى" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المستويات</SelectItem>
                  <SelectItem value="1">المستوى الأول</SelectItem>
                  <SelectItem value="2">المستوى الثاني</SelectItem>
                  <SelectItem value="3">المستوى الثالث</SelectItem>
                  <SelectItem value="4">المستوى الرابع</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الحسابات */}
        <Card>
          <CardHeader>
            <CardTitle>الحسابات المحاسبية</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل الحسابات...</p>
              </div>
            ) : filteredAccounts.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد حسابات مطابقة للبحث</p>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto">
                {filteredAccounts.map(account => renderAccount(account))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل الحساب */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingAccount ? 'تعديل الحساب' : parentAccount ? `إضافة حساب فرعي تحت: ${parentAccount.account_name}` : 'إضافة حساب جديد'}
              </DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="account_code">رمز الحساب</Label>
                  <Input
                    id="account_code"
                    value={formData.account_code}
                    onChange={(e) => setFormData({...formData, account_code: e.target.value})}
                    required
                    placeholder="مثال: 0101"
                  />
                </div>

                <div>
                  <Label htmlFor="account_name">اسم الحساب</Label>
                  <Input
                    id="account_name"
                    value={formData.account_name}
                    onChange={(e) => setFormData({...formData, account_name: e.target.value})}
                    required
                    placeholder="اسم الحساب بالعربية"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="account_name_en">اسم الحساب بالإنجليزية (اختياري)</Label>
                <Input
                  id="account_name_en"
                  value={formData.account_name_en}
                  onChange={(e) => setFormData({...formData, account_name_en: e.target.value})}
                  placeholder="Account Name in English"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="account_type">نوع الحساب</Label>
                  <Select value={formData.account_type} onValueChange={(value) => setFormData({...formData, account_type: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الحساب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="أصول">أصول</SelectItem>
                      <SelectItem value="خصوم">خصوم</SelectItem>
                      <SelectItem value="حقوق ملكية">حقوق ملكية</SelectItem>
                      <SelectItem value="إيرادات">إيرادات</SelectItem>
                      <SelectItem value="مصروفات">مصروفات</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="account_nature">طبيعة الحساب</Label>
                  <Select value={formData.account_nature} onValueChange={(value) => setFormData({...formData, account_nature: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر طبيعة الحساب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="مدين">مدين</SelectItem>
                      <SelectItem value="دائن">دائن</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="linked_table">ربط بجدول خارجي (اختياري)</Label>
                  <Select value={formData.linked_table} onValueChange={(value) => setFormData({...formData, linked_table: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الجدول المرتبط" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">بدون ربط</SelectItem>
                      <SelectItem value="clients">العملاء</SelectItem>
                      <SelectItem value="employees">الموظفين</SelectItem>
                      <SelectItem value="cases">القضايا</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse pt-6">
                  <input
                    type="checkbox"
                    id="auto_create_sub_accounts"
                    checked={formData.auto_create_sub_accounts}
                    onChange={(e) => setFormData({...formData, auto_create_sub_accounts: e.target.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="auto_create_sub_accounts">إنشاء حسابات فرعية تلقائياً</Label>
                </div>
              </div>

              <div>
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="وصف الحساب ووظيفته"
                />
              </div>

              {parentAccount && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>الحساب الأب:</strong> {parentAccount.account_code} - {parentAccount.account_name}
                  </p>
                  <p className="text-sm text-blue-600">
                    المستوى الجديد: {parentAccount.account_level + 1}
                  </p>
                </div>
              )}

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddDialog(false)
                  setEditingAccount(null)
                  setParentAccount(null)
                  resetForm()
                }}>
                  إلغاء
                </Button>
                <Button type="submit">
                  {editingAccount ? 'تحديث' : 'حفظ'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
