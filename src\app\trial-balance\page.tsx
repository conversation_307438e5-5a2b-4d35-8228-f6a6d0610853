'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Calculator,
  Search,
  Download,
  Printer,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

interface TrialBalanceItem {
  account_code: string
  account_name: string
  debit_balance: number
  credit_balance: number
  account_type: string
}

export default function TrialBalancePage() {
  const [trialBalance, setTrialBalance] = useState<TrialBalanceItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchTrialBalance = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      // محاكاة بيانات ميزان المراجعة
      const sampleData: TrialBalanceItem[] = [
        {
          account_code: '1001',
          account_name: 'النقدية في الصندوق',
          debit_balance: 50000,
          credit_balance: 0,
          account_type: 'أصول'
        },
        {
          account_code: '1002',
          account_name: 'البنك - الحساب الجاري',
          debit_balance: 150000,
          credit_balance: 0,
          account_type: 'أصول'
        },
        {
          account_code: '1101',
          account_name: 'العملاء',
          debit_balance: 75000,
          credit_balance: 0,
          account_type: 'أصول'
        },
        {
          account_code: '2001',
          account_name: 'الموردون',
          debit_balance: 0,
          credit_balance: 45000,
          account_type: 'خصوم'
        },
        {
          account_code: '3001',
          account_name: 'رأس المال',
          debit_balance: 0,
          credit_balance: 200000,
          account_type: 'حقوق ملكية'
        },
        {
          account_code: '4001',
          account_name: 'إيرادات الخدمات القانونية',
          debit_balance: 0,
          credit_balance: 120000,
          account_type: 'إيرادات'
        },
        {
          account_code: '5001',
          account_name: 'مصروفات الرواتب',
          debit_balance: 80000,
          credit_balance: 0,
          account_type: 'مصروفات'
        },
        {
          account_code: '5002',
          account_name: 'مصروفات الإيجار',
          debit_balance: 10000,
          credit_balance: 0,
          account_type: 'مصروفات'
        }
      ]

      setTrialBalance(sampleData)
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setTrialBalance([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTrialBalance()
  }, [selectedDate])

  const filteredItems = trialBalance.filter(item =>
    (item.account_code || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.account_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.account_type || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalDebits = filteredItems.reduce((sum, item) => sum + (item.debit_balance || 0), 0)
  const totalCredits = filteredItems.reduce((sum, item) => sum + (item.credit_balance || 0), 0)
  const isBalanced = totalDebits === totalCredits

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'أصول': return 'bg-blue-100 text-blue-800'
      case 'خصوم': return 'bg-red-100 text-red-800'
      case 'حقوق ملكية': return 'bg-green-100 text-green-800'
      case 'إيرادات': return 'bg-purple-100 text-purple-800'
      case 'مصروفات': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Calculator className="h-8 w-8 mr-3 text-blue-600" />
              ميزان المراجعة
            </h1>
            <p className="text-gray-600 mt-1">عرض أرصدة الحسابات والتأكد من توازن الميزان</p>
          </div>

          <div className="flex space-x-3 space-x-reverse">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              تصدير
            </Button>
            <Button variant="outline">
              <Printer className="h-4 w-4 mr-2" />
              طباعة
            </Button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-green-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي المدين</dt>
                    <dd className="text-lg font-medium text-gray-900">{totalDebits.toLocaleString()} ريال</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingDown className="h-8 w-8 text-red-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي الدائن</dt>
                    <dd className="text-lg font-medium text-gray-900">{totalCredits.toLocaleString()} ريال</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Calculator className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">الفرق</dt>
                    <dd className="text-lg font-medium text-gray-900">{Math.abs(totalDebits - totalCredits).toLocaleString()} ريال</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`h-8 w-8 rounded-full flex items-center justify-center ${isBalanced ? 'bg-green-100' : 'bg-red-100'}`}>
                    {isBalanced ? '✓' : '✗'}
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">حالة الميزان</dt>
                    <dd className={`text-lg font-medium ${isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                      {isBalanced ? 'متوازن' : 'غير متوازن'}
                    </dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الحسابات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <div className="relative">
                <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* عرض رسالة الخطأ */}
        {dbError && (
          <Card>
            <CardContent className="p-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">خطأ في الاتصال بقاعدة البيانات</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={fetchTrialBalance} variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                ميزان المراجعة - {selectedDate} ({filteredItems.length} حساب)
              </div>
              <Badge className={isBalanced ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {isBalanced ? 'متوازن' : 'غير متوازن'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!dbError && !isLoading && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">رمز الحساب</th>
                      <th className="text-right p-3 font-semibold">اسم الحساب</th>
                      <th className="text-center p-3 font-semibold">نوع الحساب</th>
                      <th className="text-center p-3 font-semibold">المدين</th>
                      <th className="text-center p-3 font-semibold">الدائن</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredItems.map((item, index) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{item.account_code}</td>
                        <td className="p-3">{item.account_name}</td>
                        <td className="text-center p-3">
                          <Badge className={getAccountTypeColor(item.account_type)}>
                            {item.account_type}
                          </Badge>
                        </td>
                        <td className="text-center p-3">
                          {item.debit_balance > 0 && (
                            <div className="flex items-center justify-center">
                              <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                              {item.debit_balance.toLocaleString()}
                            </div>
                          )}
                        </td>
                        <td className="text-center p-3">
                          {item.credit_balance > 0 && (
                            <div className="flex items-center justify-center">
                              <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                              {item.credit_balance.toLocaleString()}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                    {/* صف الإجماليات */}
                    <tr className="border-t-2 border-gray-300 bg-gray-50 font-bold">
                      <td className="p-3" colSpan={3}>الإجماليات</td>
                      <td className="text-center p-3">
                        <div className="flex items-center justify-center">
                          <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                          {totalDebits.toLocaleString()}
                        </div>
                      </td>
                      <td className="text-center p-3">
                        <div className="flex items-center justify-center">
                          <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                          {totalCredits.toLocaleString()}
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            )}

            {!dbError && !isLoading && trialBalance.length === 0 && (
              <div className="text-center py-8">
                <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات</h3>
                <p className="text-gray-600">لم يتم العثور على أي حسابات للتاريخ المحدد</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
