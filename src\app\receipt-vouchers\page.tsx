'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { VoucherForm } from '@/components/accounting/voucher-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Receipt,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  TrendingUp,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'

interface ReceiptVoucher {
  id: number
  voucher_number: string
  voucher_date: string
  description: string
  total_amount: number
  reference: string
  status: string
  created_by: string
  created_at: string
  entries: any[]
}

export default function ReceiptVouchersPage() {
  const [vouchers, setVouchers] = useState<ReceiptVoucher[]>([])
  const [filteredVouchers, setFilteredVouchers] = useState<ReceiptVoucher[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingVoucher, setEditingVoucher] = useState<ReceiptVoucher | null>(null)
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add')

  // جلب البيانات
  useEffect(() => {
    fetchVouchers()
  }, [])

  // تطبيق الفلاتر
  useEffect(() => {
    let filtered = vouchers

    if (searchTerm) {
      filtered = filtered.filter(voucher =>
        voucher.voucher_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        voucher.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        voucher.reference.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(voucher => voucher.status === statusFilter)
    }

    setFilteredVouchers(filtered)
  }, [vouchers, searchTerm, statusFilter])

  const fetchVouchers = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/receipt-vouchers')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setVouchers(result.data)
        }
      }
    } catch (error) {
      console.error('Error fetching receipt vouchers:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddNew = () => {
    setEditingVoucher(null)
    setFormMode('add')
    setIsFormOpen(true)
  }

  const handleEdit = (voucher: ReceiptVoucher) => {
    setEditingVoucher(voucher)
    setFormMode('edit')
    setIsFormOpen(true)
  }

  const handleView = (voucher: ReceiptVoucher) => {
    setEditingVoucher(voucher)
    setFormMode('view')
    setIsFormOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا السند؟')) {
      try {
        const response = await fetch(`/api/receipt-vouchers/${id}`, {
          method: 'DELETE'
        })
        if (response.ok) {
          fetchVouchers()
        }
      } catch (error) {
        console.error('Error deleting voucher:', error)
      }
    }
  }

  const handleSaveVoucher = async (voucherData: any) => {
    try {
      const url = editingVoucher 
        ? `/api/receipt-vouchers/${editingVoucher.id}`
        : '/api/receipt-vouchers'
      
      const method = editingVoucher ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(voucherData)
      })

      if (response.ok) {
        fetchVouchers()
        setIsFormOpen(false)
      }
    } catch (error) {
      console.error('Error saving voucher:', error)
      throw error
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'معتمد'
      case 'pending': return 'في الانتظار'
      case 'rejected': return 'مرفوض'
      default: return 'غير محدد'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />
      case 'pending': return <Clock className="h-4 w-4" />
      case 'rejected': return <XCircle className="h-4 w-4" />
      default: return null
    }
  }

  // حساب الإحصائيات
  const stats = {
    total: vouchers.length,
    approved: vouchers.filter(v => v.status === 'approved').length,
    pending: vouchers.filter(v => v.status === 'pending').length,
    totalAmount: vouchers.reduce((sum, v) => sum + (v.total_amount || 0), 0)
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Receipt className="h-8 w-8 mr-3 text-green-600" />
              سندات القبض
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة سندات القبض والإيرادات</p>
          </div>
          
          <Button onClick={handleAddNew} className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة سند قبض جديد
          </Button>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Receipt className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي السندات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.approved}</div>
                  <div className="text-sm text-gray-600">سندات معتمدة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.pending}</div>
                  <div className="text-sm text-gray-600">في الانتظار</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {stats.totalAmount.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">إجمالي المبالغ</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في سندات القبض..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="pending">في الانتظار</option>
                  <option value="approved">معتمد</option>
                  <option value="rejected">مرفوض</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول السندات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Receipt className="h-5 w-5 mr-2" />
              قائمة سندات القبض ({filteredVouchers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
              </div>
            ) : filteredVouchers.length === 0 ? (
              <div className="text-center py-8">
                <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد سندات قبض</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">رقم السند</th>
                      <th className="text-center p-3 font-semibold">التاريخ</th>
                      <th className="text-right p-3 font-semibold">الوصف</th>
                      <th className="text-center p-3 font-semibold">المبلغ</th>
                      <th className="text-right p-3 font-semibold">المرجع</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-right p-3 font-semibold">المنشئ</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredVouchers.map((voucher) => (
                      <tr key={voucher.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{voucher.voucher_number}</td>
                        <td className="text-center p-3">
                          <div className="flex items-center justify-center">
                            <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                            {voucher.voucher_date}
                          </div>
                        </td>
                        <td className="p-3">{voucher.description}</td>
                        <td className="text-center p-3">
                          <div className="flex items-center justify-center">
                            <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                            {(voucher.total_amount || 0).toLocaleString()}
                          </div>
                        </td>
                        <td className="p-3">{voucher.reference}</td>
                        <td className="text-center p-3">
                          <Badge className={getStatusColor(voucher.status)}>
                            <div className="flex items-center">
                              {getStatusIcon(voucher.status)}
                              <span className="mr-1">{getStatusText(voucher.status)}</span>
                            </div>
                          </Badge>
                        </td>
                        <td className="p-3">{voucher.created_by}</td>
                        <td className="text-center p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline" onClick={() => handleView(voucher)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(voucher)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(voucher.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج السند */}
        <VoucherForm
          voucherType="receipt"
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onSave={handleSaveVoucher}
          editingData={editingVoucher}
          mode={formMode}
        />
      </div>
    </MainLayout>
  )
}
