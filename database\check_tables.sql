-- التحقق من هيكل الجداول المحدثة
-- Database: mohammi

-- ===================================
-- 1. التحقق من جدول النسب المالية (lineages)
-- ===================================

SELECT 'جدول النسب المالية (lineages)' as table_info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'lineages' 
ORDER BY ordinal_position;

SELECT 'بيانات جدول النسب المالية:' as data_info;
SELECT * FROM lineages ORDER BY id;

-- ===================================
-- 2. التحقق من جدول الخدمات (services)
-- ===================================

SELECT 'جدول الخدمات (services)' as table_info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'services' 
ORDER BY ordinal_position;

SELECT 'بيانات جدول الخدمات:' as data_info;
SELECT * FROM services ORDER BY id;

-- ===================================
-- 3. التحقق من جدول توزيع القضايا (case_distribution)
-- ===================================

SELECT 'جدول توزيع القضايا (case_distribution)' as table_info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'case_distribution' 
ORDER BY ordinal_position;

SELECT 'عدد سجلات توزيع القضايا:' as data_info;
SELECT COUNT(*) as total_records FROM case_distribution;

-- ===================================
-- 4. التحقق من جدول تفاصيل توزيع الخدمات (service_distributions)
-- ===================================

SELECT 'جدول تفاصيل توزيع الخدمات (service_distributions)' as table_info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'service_distributions' 
ORDER BY ordinal_position;

SELECT 'عدد سجلات تفاصيل توزيع الخدمات:' as data_info;
SELECT COUNT(*) as total_records FROM service_distributions;

-- ===================================
-- 5. التحقق من الفهارس
-- ===================================

SELECT 'الفهارس المنشأة:' as index_info;
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('lineages', 'services', 'case_distribution', 'service_distributions')
ORDER BY tablename, indexname;

-- ===================================
-- 6. التحقق من العلاقات (Foreign Keys)
-- ===================================

SELECT 'العلاقات بين الجداول:' as fk_info;
SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('case_distribution', 'service_distributions')
ORDER BY tc.table_name;

-- ===================================
-- 7. ملخص الجداول
-- ===================================

SELECT 'ملخص عام للجداول:' as summary_info;
SELECT 
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count,
    CASE 
        WHEN table_name = 'lineages' THEN (SELECT COUNT(*) FROM lineages)
        WHEN table_name = 'services' THEN (SELECT COUNT(*) FROM services)
        WHEN table_name = 'case_distribution' THEN (SELECT COUNT(*) FROM case_distribution)
        WHEN table_name = 'service_distributions' THEN (SELECT COUNT(*) FROM service_distributions)
        ELSE 0
    END as record_count
FROM information_schema.tables t
WHERE table_name IN ('lineages', 'services', 'case_distribution', 'service_distributions')
    AND table_schema = 'public'
ORDER BY table_name;
