'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ChevronDown, Search, Percent } from 'lucide-react'

interface Lineage {
  id: number
  name: string
  admin_percentage: number
  created_date?: string
}

interface LineageSelectProps {
  value: string
  onChange: (lineageId: string, lineageData: Lineage | null) => void
  label?: string
  placeholder?: string
  required?: boolean
}

export function LineageSelect({ value, onChange, label = "مجموعة النسب المالية", placeholder = "اختر مجموعة النسب", required = false }: LineageSelectProps) {
  const [lineages, setLineages] = useState<Lineage[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLineage, setSelectedLineage] = useState<Lineage | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const fetchLineages = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/percentages')
      const result = await response.json()

      if (result.success) {
        setLineages(result.data)
      }
    } catch (error) {
      console.error('Error fetching lineages:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchLineages()
  }, [])

  useEffect(() => {
    if (value && lineages.length > 0) {
      const lineage = lineages.find(l => l.id.toString() === value)
      setSelectedLineage(lineage || null)
    }
  }, [value, lineages])

  const filteredLineages = lineages.filter(lineage =>
    lineage.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (lineage: Lineage) => {
    setSelectedLineage(lineage)
    onChange(lineage.id.toString(), lineage)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedLineage(null)
    onChange('', null)
    setSearchTerm('')
  }

  return (
    <div className="relative">
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            <Percent className="h-4 w-4 mr-2 text-gray-400" />
            <span className={selectedLineage ? 'text-gray-900' : 'text-gray-500'}>
              {selectedLineage ? selectedLineage.name : placeholder}
            </span>
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في مجموعات النسب..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة النسب المالية */}
            <div className="max-h-48 overflow-y-auto">
              {isLoading ? (
                <div className="p-3 text-center text-gray-500">جاري التحميل...</div>
              ) : filteredLineages.length > 0 ? (
                <>
                  {selectedLineage && (
                    <div
                      className="p-2 hover:bg-gray-100 cursor-pointer border-b text-red-600"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredLineages.map((lineage) => (
                    <div
                      key={lineage.id}
                      className="p-3 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                      onClick={() => handleSelect(lineage)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="font-medium text-gray-900">{lineage.name}</div>
                        <div className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm font-medium">
                          {lineage.admin_percentage}%
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-3 text-center text-gray-500">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد مجموعات نسب'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>



      {/* حقل مخفي للقيمة */}
      <input type="hidden" value={value} name="lineage_id" />
    </div>
  )
}
