'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Bell, Search, User, LogOut } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SmartSearch } from './smart-search'

export function Header() {
  const router = useRouter()
  const [userSession, setUserSession] = useState<any>(null)

  // جلب بيانات المستخدم من localStorage
  useEffect(() => {
    const session = localStorage.getItem('userSession')
    if (session) {
      setUserSession(JSON.parse(session))
    }
  }, [])

  // تسجيل الخروج
  const handleLogout = async () => {
    try {
      await fetch('/api/auth/users', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userSession?.token}`
        }
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('userSession')
      router.push('/login')
    }
  }
  return (
    <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <SmartSearch />
        </div>

        <div className="flex items-center space-x-4 space-x-reverse">
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
          </Button>

          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                {userSession?.name || 'المستخدم'}
              </div>
              <div className="text-xs text-gray-500">
                {userSession?.username || 'غير محدد'}
              </div>
            </div>
            <Button variant="ghost" size="icon">
              <User className="h-5 w-5" />
            </Button>

            {userSession && (
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLogout}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                title="تسجيل الخروج"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
