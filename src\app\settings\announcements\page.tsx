'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Megaphone,
  Save,
  RotateCcw,
  Eye,
  EyeOff
} from 'lucide-react'

interface Announcement {
  id: number
  announcement_1: string
  announcement_2: string
  announcement_3: string
  announcement_4: string
  is_active: boolean
  created_date: string
  updated_at: string
}

export default function AnnouncementsPage() {
  const [announcement, setAnnouncement] = useState<Announcement | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    announcement_1: '',
    announcement_2: '',
    announcement_3: '',
    announcement_4: '',
    is_active: true
  })

  // جلب الإعلانات
  const fetchAnnouncements = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/settings/announcements')
      const data = await response.json()
      if (data.success && data.data) {
        setAnnouncement(data.data)
        setFormData({
          announcement_1: data.data.announcement_1 || '',
          announcement_2: data.data.announcement_2 || '',
          announcement_3: data.data.announcement_3 || '',
          announcement_4: data.data.announcement_4 || '',
          is_active: data.data.is_active !== undefined ? data.data.is_active : true
        })
      }
    } catch (error) {
      console.error('Error fetching announcements:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAnnouncements()
  }, [])

  // حفظ الإعلانات
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      const method = announcement ? 'PUT' : 'POST'
      const body = announcement 
        ? { id: announcement.id, ...formData }
        : formData

      const response = await fetch('/api/settings/announcements', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      const data = await response.json()
      if (data.success) {
        await fetchAnnouncements()
        alert('تم حفظ الإعلانات بنجاح')
      } else {
        alert(`خطأ: ${data.error}`)
      }
    } catch (error) {
      console.error('Error saving announcements:', error)
      alert('حدث خطأ أثناء حفظ الإعلانات')
    } finally {
      setIsSaving(false)
    }
  }

  // إعادة تعيين النموذج
  const handleReset = () => {
    if (announcement) {
      setFormData({
        announcement_1: announcement.announcement_1 || '',
        announcement_2: announcement.announcement_2 || '',
        announcement_3: announcement.announcement_3 || '',
        announcement_4: announcement.announcement_4 || '',
        is_active: announcement.is_active
      })
    } else {
      setFormData({
        announcement_1: '',
        announcement_2: '',
        announcement_3: '',
        announcement_4: '',
        is_active: true
      })
    }
  }

  // تبديل حالة النشاط
  const toggleActive = () => {
    setFormData({...formData, is_active: !formData.is_active})
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان الرئيسي */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الإعلانات</h1>
            <p className="text-gray-600 mt-1">إدارة الإعلانات المعروضة في الشريط المتحرك</p>
          </div>
          
          {/* حالة الإعلانات */}
          <div className="flex items-center space-x-3 space-x-reverse">
            <Badge className={formData.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
              {formData.is_active ? 'نشط' : 'غير نشط'}
            </Badge>
            <Button
              variant="outline"
              onClick={toggleActive}
              className={formData.is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
            >
              {formData.is_active ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
              {formData.is_active ? 'إخفاء الإعلانات' : 'إظهار الإعلانات'}
            </Button>
          </div>
        </div>

        {/* نموذج الإعلانات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Megaphone className="h-5 w-5 mr-2" />
              نصوص الإعلانات
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">جاري التحميل...</div>
            ) : (
              <form onSubmit={handleSave} className="space-y-6">
                {/* الإعلان الأول */}
                <div className="space-y-2">
                  <Label htmlFor="announcement_1" className="text-sm font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md inline-block">
                    📢 الإعلان الأول
                  </Label>
                  <textarea
                    id="announcement_1"
                    value={formData.announcement_1}
                    onChange={(e) => setFormData({...formData, announcement_1: e.target.value})}
                    placeholder="اكتب نص الإعلان الأول هنا..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                    rows={2}
                  />
                  <p className="text-xs text-gray-500">
                    عدد الأحرف: {formData.announcement_1.length}
                  </p>
                </div>

                {/* الإعلان الثاني */}
                <div className="space-y-2">
                  <Label htmlFor="announcement_2" className="text-sm font-semibold text-green-700 bg-green-50 px-2 py-1 rounded-md inline-block">
                    📢 الإعلان الثاني
                  </Label>
                  <textarea
                    id="announcement_2"
                    value={formData.announcement_2}
                    onChange={(e) => setFormData({...formData, announcement_2: e.target.value})}
                    placeholder="اكتب نص الإعلان الثاني هنا..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-vertical"
                    rows={2}
                  />
                  <p className="text-xs text-gray-500">
                    عدد الأحرف: {formData.announcement_2.length}
                  </p>
                </div>

                {/* الإعلان الثالث */}
                <div className="space-y-2">
                  <Label htmlFor="announcement_3" className="text-sm font-semibold text-orange-700 bg-orange-50 px-2 py-1 rounded-md inline-block">
                    📢 الإعلان الثالث
                  </Label>
                  <textarea
                    id="announcement_3"
                    value={formData.announcement_3}
                    onChange={(e) => setFormData({...formData, announcement_3: e.target.value})}
                    placeholder="اكتب نص الإعلان الثالث هنا..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical"
                    rows={2}
                  />
                  <p className="text-xs text-gray-500">
                    عدد الأحرف: {formData.announcement_3.length}
                  </p>
                </div>

                {/* الإعلان الرابع */}
                <div className="space-y-2">
                  <Label htmlFor="announcement_4" className="text-sm font-semibold text-purple-700 bg-purple-50 px-2 py-1 rounded-md inline-block">
                    📢 الإعلان الرابع
                  </Label>
                  <textarea
                    id="announcement_4"
                    value={formData.announcement_4}
                    onChange={(e) => setFormData({...formData, announcement_4: e.target.value})}
                    placeholder="اكتب نص الإعلان الرابع هنا..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-vertical"
                    rows={2}
                  />
                  <p className="text-xs text-gray-500">
                    عدد الأحرف: {formData.announcement_4.length}
                  </p>
                </div>

                {/* معاينة الإعلانات */}
                {(formData.announcement_1 || formData.announcement_2 || formData.announcement_3 || formData.announcement_4) && (
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <h3 className="text-sm font-medium text-gray-700 mb-3">معاينة الشريط المتحرك:</h3>
                    <div className="bg-blue-600 text-white p-2 rounded overflow-hidden">
                      <div className="animate-marquee whitespace-nowrap">
                        {[formData.announcement_1, formData.announcement_2, formData.announcement_3, formData.announcement_4]
                          .filter(text => text.trim())
                          .join(' • ')}
                      </div>
                    </div>
                  </div>
                )}

                {/* أزرار الإجراءات */}
                <div className="flex space-x-3 space-x-reverse pt-6 border-t">
                  <Button 
                    type="submit" 
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                    disabled={isSaving}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'جاري الحفظ...' : 'حفظ الإعلانات'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleReset}
                    className="flex-1"
                    disabled={isSaving}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    إعادة تعيين
                  </Button>
                </div>

                {/* معلومات إضافية */}
                {announcement && (
                  <div className="text-xs text-gray-500 pt-4 border-t">
                    <p>آخر تحديث: {new Date(announcement.updated_at).toLocaleString('ar-EG')}</p>
                    <p>تاريخ الإنشاء: {new Date(announcement.created_date).toLocaleString('ar-EG')}</p>
                  </div>
                )}
              </form>
            )}
          </CardContent>
        </Card>

        {/* نصائح الاستخدام */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">نصائح الاستخدام</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• يمكنك إضافة حتى 4 إعلانات مختلفة</p>
            <p>• الإعلانات الفارغة لن تظهر في الشريط المتحرك</p>
            <p>• استخدم نصوص قصيرة ومفيدة للحصول على أفضل تأثير</p>
            <p>• يمكنك إيقاف/تشغيل جميع الإعلانات من الزر أعلى الصفحة</p>
            <p>• الإعلانات ستظهر في الشريط المتحرك أعلى جميع صفحات النظام</p>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
