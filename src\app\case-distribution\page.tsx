'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { IssueSelect } from '@/components/ui/issue-select'
import { LineageSelect } from '@/components/ui/lineage-select'
import { EmployeeSelect } from '@/components/ui/employee-select'
import {
  Share2,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  Calculator,
  DollarSign,
  Users,
  FileText
} from 'lucide-react'

interface ServiceDistribution {
  service_id: number
  service_name: string
  percentage: number
  amount: number
  lawyer_id: number
  lawyer_name: string
}

interface CaseDistribution {
  id: number
  issue_id: number
  issue_title: string
  case_number: string
  case_amount: number
  lineage_id: number
  lineage_name: string
  admin_percentage: number
  commission_percentage: number
  admin_amount: number
  commission_amount: number
  remaining_amount: number
  service_distributions: ServiceDistribution[]
  created_date: string
}

export default function CaseDistributionPage() {
  const [distributions, setDistributions] = useState<CaseDistribution[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingDistribution, setEditingDistribution] = useState<CaseDistribution | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    issue_id: '',
    issue_data: null as any,
    lineage_id: '',
    lineage_data: null as any,
    service_distributions: [] as ServiceDistribution[]
  })

  const fetchDistributions = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/case-distribution')
      const result = await response.json()

      if (result.success) {
        setDistributions(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات توزيع القضايا')
        setDistributions([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setDistributions([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDistributions()
  }, [])

  const handleAddNew = () => {
    setEditingDistribution(null)
    setFormData({
      issue_id: '',
      issue_data: null,
      lineage_id: '',
      lineage_data: null,
      service_distributions: []
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleIssueChange = (issueId: string, issueData: any) => {
    setFormData({
      ...formData,
      issue_id: issueId,
      issue_data: issueData
    })
  }

  const handleLineageChange = (lineageId: string, lineageData: any) => {
    if (lineageData && formData.issue_data) {
      // حساب نسبة الإدارة ومبلغها
      const adminAmount = (formData.issue_data.amount * lineageData.admin_percentage) / 100
      const commissionAmount = (formData.issue_data.amount * (lineageData.commission_percentage || 0)) / 100
      const remainingAmount = formData.issue_data.amount - adminAmount - commissionAmount

      // إنشاء توزيع الخدمات بنسب افتراضية (يمكن تعديلها لاحقاً)
      const defaultServicePercentages = {
        'اعداد': 30,
        'جلسة': 25,
        'متابعة': 25,
        'اشراف': 20
      }

      const serviceDistributions = [
        {
          service_id: 1,
          service_name: 'اعداد',
          percentage: defaultServicePercentages['اعداد'],
          amount: (remainingAmount * defaultServicePercentages['اعداد']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        },
        {
          service_id: 2,
          service_name: 'جلسة',
          percentage: defaultServicePercentages['جلسة'],
          amount: (remainingAmount * defaultServicePercentages['جلسة']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        },
        {
          service_id: 3,
          service_name: 'متابعة',
          percentage: defaultServicePercentages['متابعة'],
          amount: (remainingAmount * defaultServicePercentages['متابعة']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        },
        {
          service_id: 4,
          service_name: 'اشراف',
          percentage: defaultServicePercentages['اشراف'],
          amount: (remainingAmount * defaultServicePercentages['اشراف']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        }
      ]

      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        service_distributions: serviceDistributions
      })
    } else {
      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        service_distributions: []
      })
    }
  }

  const handleLawyerChange = (serviceId: number, lawyerId: string, lawyerData: any) => {
    const updatedDistributions = formData.service_distributions.map(dist =>
      dist.service_id === serviceId
        ? { ...dist, lawyer_id: Number(lawyerId), lawyer_name: lawyerData ? lawyerData.name : '' }
        : dist
    )

    setFormData({
      ...formData,
      service_distributions: updatedDistributions
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/case-distribution', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة توزيع القضية بنجاح')
          fetchDistributions()
        } else {
          alert(result.error || 'فشل في إضافة توزيع القضية')
          return
        }
      }

      setIsModalOpen(false)
      setEditingDistribution(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const stats = {
    total: distributions.length,
    totalAmount: distributions.reduce((sum, d) => sum + d.case_amount, 0),
    totalAdminAmount: distributions.reduce((sum, d) => sum + d.admin_amount, 0)
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Share2 className="h-8 w-8 mr-3 text-green-600" />
              توزيع القضايا
            </h1>
            <p className="text-gray-600 mt-1">إدارة وتوزيع مبالغ القضايا على الخدمات والمحامين</p>
          </div>

          <Button onClick={handleAddNew} className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة توزيع جديد
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي التوزيعات</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي المبالغ</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAmount.toLocaleString()} ريال</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calculator className="h-8 w-8 text-purple-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">مبالغ الإدارة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAdminAmount.toLocaleString()} ريال</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* النموذج المحسن */}
        {isModalOpen && modalType === 'add' && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">📋 إضافة توزيع قضية جديد</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* القسم الأول: اختيار القضية والنسب المالية */}
                <Card>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-12 gap-4 mb-6">
                      {/* اختيار القضية - 50% */}
                      <div className="col-span-6">
                        <IssueSelect
                          value={formData.issue_id}
                          onChange={handleIssueChange}
                          label="القضية"
                          placeholder="اختر القضية"
                          required
                        />
                      </div>

                      {/* مبلغ القضية - 30% */}
                      {formData.issue_data && (
                        <div className="col-span-3">
                          <Label className="text-sm font-medium text-gray-600">مبلغ القضية</Label>
                          <div className="mt-1 p-3 bg-blue-50 border-2 border-blue-200 rounded-lg text-blue-800 font-bold text-lg text-center">
                            💰 {formData.issue_data.amount?.toLocaleString() || 0} ريال
                          </div>
                        </div>
                      )}

                      {/* النسب المالية - 20% */}
                      <div className="col-span-3">
                        <LineageSelect
                          value={formData.lineage_id}
                          onChange={handleLineageChange}
                          label="النسب المالية"
                          placeholder="اختر النسب"
                          required
                        />
                      </div>
                    </div>

                    {/* عرض تفاصيل النسب والحسابات */}
                    {formData.lineage_data && formData.issue_data && (
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          {/* نسبة الإدارة */}
                          <div className="p-4 bg-purple-50 border-2 border-purple-200 rounded-lg">
                            <Label className="text-purple-800 font-bold text-base">نسبة الإدارة</Label>
                            <div className="flex items-center space-x-2 space-x-reverse mt-2">
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                value={formData.lineage_data.admin_percentage}
                                onChange={(e) => {
                                  const newData = {...formData.lineage_data, admin_percentage: Number(e.target.value)}
                                  setFormData({...formData, lineage_data: newData})
                                }}
                                className="w-24 text-center font-bold"
                              />
                              <span className="text-purple-600 font-bold text-lg">%</span>
                              <span className="text-sm text-gray-600 font-medium">
                                = {((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100).toLocaleString()} ريال
                              </span>
                            </div>
                          </div>

                          {/* نسبة العمولة */}
                          <div className="p-4 bg-green-50 border-2 border-green-200 rounded-lg">
                            <Label className="text-green-800 font-bold text-base">العمولة</Label>
                            <div className="flex items-center space-x-2 space-x-reverse mt-2">
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                value={formData.lineage_data.commission_percentage || 0}
                                onChange={(e) => {
                                  const newData = {...formData.lineage_data, commission_percentage: Number(e.target.value)}
                                  setFormData({...formData, lineage_data: newData})
                                }}
                                className="w-24 text-center font-bold"
                              />
                              <span className="text-green-600 font-bold text-lg">%</span>
                              <span className="text-sm text-gray-600 font-medium">
                                = {((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100).toLocaleString()} ريال
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* ملخص النسب */}
                        <div className="p-4 bg-blue-50 border-2 border-blue-200 rounded-lg">
                          <div className="text-center">
                            <div className="text-sm text-gray-700 mb-2">
                              <span className="font-medium">نسبة الإدارة {formData.lineage_data.admin_percentage}%:</span>
                              <strong className="mx-2 text-purple-600">{((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100).toLocaleString()} ريال</strong>
                              <span className="font-medium">- العمولة {formData.lineage_data.commission_percentage || 0}%:</span>
                              <strong className="mx-2 text-green-600">{((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100).toLocaleString()} ريال</strong>
                            </div>
                            <div className="font-bold text-blue-800 text-lg">
                              = إجمالي النسبة {(formData.lineage_data.admin_percentage + (formData.lineage_data.commission_percentage || 0)).toFixed(1)}%
                              مبلغ: {(((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100) + ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)).toLocaleString()} ريال
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* توزيع الخدمات على المحامين */}
                {formData.lineage_data && formData.issue_data && (
                  <Card>
                    <CardContent className="p-6">
                      <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="text-sm text-gray-600">
                          النسبة القابلة للتوزيع: <strong className="text-green-700">{(100 - formData.lineage_data.admin_percentage - (formData.lineage_data.commission_percentage || 0)).toFixed(1)}%</strong>
                          - المبلغ المتاح: <strong className="text-green-700">{(formData.issue_data.amount - ((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100) - ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)).toLocaleString()} ريال</strong>
                        </div>
                      </div>
                      {formData.service_distributions.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border-2 border-gray-300 rounded-lg overflow-hidden">
                            <thead>
                              <tr className="bg-gradient-to-r from-green-100 to-blue-100">
                                <th className="border-2 border-gray-300 p-4 text-right font-bold text-gray-700 bg-gray-50">🔧 الخدمة</th>
                                <th className="border-2 border-gray-300 p-4 text-center font-bold text-gray-700 bg-blue-50">📊 النسبة (%)</th>
                                <th className="border-2 border-gray-300 p-4 text-center font-bold text-gray-700 bg-green-50">💰 المبلغ (ريال)</th>
                                <th className="border-2 border-gray-300 p-4 text-right font-bold text-gray-700 bg-purple-50">👨‍💼 المحامي المكلف</th>
                              </tr>
                            </thead>
                            <tbody>
                              {formData.service_distributions.map((service, index) => (
                                <tr key={service.service_id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                  {/* الخدمة */}
                                  <td className="border-2 border-gray-300 p-4">
                                    <div className="font-medium text-gray-800 bg-white p-2 rounded border">
                                      {service.service_name}
                                    </div>
                                  </td>

                                  {/* النسبة - قابلة للتحرير */}
                                  <td className="border-2 border-gray-300 p-4 text-center">
                                    <Input
                                      type="number"
                                      min="0"
                                      max="100"
                                      step="0.1"
                                      value={service.percentage}
                                      onChange={(e) => {
                                        const newDistributions = [...formData.service_distributions]
                                        newDistributions[index].percentage = Number(e.target.value)
                                        // حساب المبلغ بناءً على النسبة
                                        if (formData.issue_data && formData.lineage_data) {
                                          const availableAmount = formData.issue_data.amount -
                                            ((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100) -
                                            ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)
                                          newDistributions[index].amount = (availableAmount * Number(e.target.value)) / 100
                                        }
                                        setFormData({...formData, service_distributions: newDistributions})
                                      }}
                                      className="text-center font-bold text-blue-600 bg-blue-50 border-2 border-blue-200"
                                    />
                                  </td>

                                  {/* المبلغ - قابل للتحرير */}
                                  <td className="border-2 border-gray-300 p-4 text-center">
                                    <Input
                                      type="number"
                                      min="0"
                                      step="0.01"
                                      value={service.amount}
                                      onChange={(e) => {
                                        const newDistributions = [...formData.service_distributions]
                                        newDistributions[index].amount = Number(e.target.value)
                                        // حساب النسبة بناءً على المبلغ
                                        if (formData.issue_data && formData.lineage_data) {
                                          const availableAmount = formData.issue_data.amount -
                                            ((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100) -
                                            ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)
                                          newDistributions[index].percentage = (Number(e.target.value) / availableAmount) * 100
                                        }
                                        setFormData({...formData, service_distributions: newDistributions})
                                      }}
                                      className="text-center font-bold text-green-600 bg-green-50 border-2 border-green-200"
                                    />
                                  </td>

                                  {/* المحامي */}
                                  <td className="border-2 border-gray-300 p-4">
                                    <EmployeeSelect
                                      value={service.lawyer_id.toString()}
                                      onChange={(lawyerId, lawyerData) => handleLawyerChange(service.service_id, lawyerId, lawyerData)}
                                      label=""
                                      placeholder="اختر المحامي"
                                      required
                                    />
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <div className="text-lg mb-2">📋</div>
                          <p>لم يتم إضافة خدمات للتوزيع بعد</p>
                          <p className="text-sm">اختر القضية والنسب المالية أولاً لعرض الخدمات المتاحة</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* أزرار الحفظ والإلغاء */}
                <Card>
                  <CardContent className="p-6">
                    <div className="flex space-x-4 space-x-reverse">
                      <Button
                        type="submit"
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-3 text-lg"
                        disabled={!formData.issue_id || !formData.lineage_id || formData.service_distributions.length === 0}
                      >
                        <Save className="h-5 w-5 mr-2" />
                        💾 حفظ التوزيع
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsModalOpen(false)}
                        className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50 font-bold py-3 text-lg"
                      >
                        <X className="h-5 w-5 mr-2" />
                        ❌ إلغاء
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </form>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
