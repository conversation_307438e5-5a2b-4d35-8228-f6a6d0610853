import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET() {
  try {
    const result = await query(`
      SELECT
        id,
        center_name as name,
        parent_id,
        is_active,
        created_date
      FROM cost_centers
      ORDER BY center_name
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('خطأ في جلب مراكز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب مراكز التكلفة'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      code,
      name,
      parent_id
    } = body

    const result = await query(`
      INSERT INTO cost_centers (
        code, name, parent_id, is_active
      ) VALUES ($1, $2, $3, TRUE)
      RETURNING *
    `, [
      code,
      name,
      parent_id || null
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إنشاء مركز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء مركز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء مركز التكلفة'
    }, { status: 500 })
  }
}
