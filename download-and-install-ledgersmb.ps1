# تحميل وتثبيت LedgerSMB كمشروع مستقل
# LedgerSMB Download and Installation Script

param(
    [string]$InstallPath = "E:\mohammi\ledgersmb-install",
    [int]$Port = 8444
)

Write-Host "🚀 بدء تحميل وتثبيت LedgerSMB..." -ForegroundColor Green
Write-Host "📍 مسار التثبيت: $InstallPath" -ForegroundColor Yellow
Write-Host "🌐 المنفذ: $Port" -ForegroundColor Yellow

# 1. التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ يجب تشغيل هذا السكريبت كمدير" -ForegroundColor Red
    Write-Host "انقر بزر الماوس الأيمن على PowerShell واختر 'تشغيل كمدير'" -ForegroundColor Yellow
    exit 1
}

# 2. إنشاء مجلد التثبيت
Write-Host "📁 إنشاء مجلد التثبيت..." -ForegroundColor Cyan
if (-not (Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
}
Set-Location $InstallPath

# 3. تثبيت Git إذا لم يكن موجوداً
Write-Host "🔧 التحقق من Git..." -ForegroundColor Cyan
try {
    git --version | Out-Null
    Write-Host "✅ Git موجود" -ForegroundColor Green
} catch {
    Write-Host "📦 تثبيت Git..." -ForegroundColor Yellow
    
    # تحميل Git
    $gitUrl = "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-********-64-bit.exe"
    $gitInstaller = "$InstallPath\git-installer.exe"
    
    Write-Host "⬇️ تحميل Git..." -ForegroundColor Cyan
    Invoke-WebRequest -Uri $gitUrl -OutFile $gitInstaller -UseBasicParsing
    
    Write-Host "🔧 تثبيت Git..." -ForegroundColor Cyan
    Start-Process -FilePath $gitInstaller -ArgumentList "/SILENT" -Wait
    
    # إضافة Git إلى PATH
    $env:Path += ";C:\Program Files\Git\bin"
    [Environment]::SetEnvironmentVariable("Path", $env:Path, [EnvironmentVariableTarget]::Machine)
    
    Remove-Item $gitInstaller -Force
    Write-Host "✅ تم تثبيت Git" -ForegroundColor Green
}

# 4. تحميل LedgerSMB من GitHub
Write-Host "⬇️ تحميل LedgerSMB من GitHub..." -ForegroundColor Cyan
$ledgerSMBPath = "$InstallPath\LedgerSMB"

if (Test-Path $ledgerSMBPath) {
    Write-Host "🔄 تحديث LedgerSMB الموجود..." -ForegroundColor Yellow
    Set-Location $ledgerSMBPath
    git pull origin master
} else {
    Write-Host "📥 استنساخ مستودع LedgerSMB..." -ForegroundColor Cyan
    git clone https://github.com/ledgersmb/LedgerSMB.git $ledgerSMBPath
    Set-Location $ledgerSMBPath
}

Write-Host "✅ تم تحميل LedgerSMB بنجاح" -ForegroundColor Green

# 5. تثبيت Strawberry Perl
Write-Host "🍓 تثبيت Strawberry Perl..." -ForegroundColor Cyan
try {
    perl --version | Out-Null
    Write-Host "✅ Perl موجود" -ForegroundColor Green
} catch {
    $perlUrl = "https://strawberryperl.com/download/********/strawberry-perl-********-64bit.msi"
    $perlInstaller = "$InstallPath\strawberry-perl.msi"
    
    Write-Host "⬇️ تحميل Strawberry Perl..." -ForegroundColor Cyan
    Invoke-WebRequest -Uri $perlUrl -OutFile $perlInstaller -UseBasicParsing
    
    Write-Host "🔧 تثبيت Strawberry Perl..." -ForegroundColor Cyan
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$perlInstaller`" /quiet" -Wait
    
    # إضافة Perl إلى PATH
    $env:Path += ";C:\Strawberry\perl\bin;C:\Strawberry\c\bin"
    [Environment]::SetEnvironmentVariable("Path", $env:Path, [EnvironmentVariableTarget]::Machine)
    
    Remove-Item $perlInstaller -Force
    Write-Host "✅ تم تثبيت Strawberry Perl" -ForegroundColor Green
}

# 6. تثبيت PostgreSQL
Write-Host "🐘 تثبيت PostgreSQL..." -ForegroundColor Cyan
try {
    & "C:\Program Files\PostgreSQL\15\bin\psql.exe" --version | Out-Null
    Write-Host "✅ PostgreSQL موجود" -ForegroundColor Green
} catch {
    $pgUrl = "https://get.enterprisedb.com/postgresql/postgresql-15.4-1-windows-x64.exe"
    $pgInstaller = "$InstallPath\postgresql-installer.exe"
    
    Write-Host "⬇️ تحميل PostgreSQL..." -ForegroundColor Cyan
    Invoke-WebRequest -Uri $pgUrl -OutFile $pgInstaller -UseBasicParsing
    
    Write-Host "🔧 تثبيت PostgreSQL..." -ForegroundColor Cyan
    Start-Process -FilePath $pgInstaller -ArgumentList "--mode unattended --superpassword postgres123 --servicename postgresql --servicepassword postgres123" -Wait
    
    # إضافة PostgreSQL إلى PATH
    $env:Path += ";C:\Program Files\PostgreSQL\15\bin"
    [Environment]::SetEnvironmentVariable("Path", $env:Path, [EnvironmentVariableTarget]::Machine)
    
    Remove-Item $pgInstaller -Force
    Write-Host "✅ تم تثبيت PostgreSQL" -ForegroundColor Green
}

# 7. إنشاء قاعدة بيانات LedgerSMB
Write-Host "🗄️ إعداد قاعدة البيانات..." -ForegroundColor Cyan
try {
    # بدء خدمة PostgreSQL
    Start-Service postgresql-x64-15 -ErrorAction SilentlyContinue
    
    # إنشاء قاعدة البيانات
    $env:PGPASSWORD = "postgres123"
    & "C:\Program Files\PostgreSQL\15\bin\createdb.exe" -U postgres -h localhost ledgersmb_standalone
    Write-Host "✅ تم إنشاء قاعدة البيانات" -ForegroundColor Green
} catch {
    Write-Host "⚠️ قاعدة البيانات قد تكون موجودة مسبقاً" -ForegroundColor Yellow
}

# 8. تثبيت تبعيات Perl
Write-Host "📦 تثبيت تبعيات Perl..." -ForegroundColor Cyan
Set-Location $ledgerSMBPath

try {
    # تثبيت cpanm إذا لم يكن موجوداً
    cpanm --version | Out-Null
} catch {
    Write-Host "🔧 تثبيت cpanm..." -ForegroundColor Yellow
    perl -MCPAN -e "install App::cpanminus"
}

# تثبيت التبعيات الأساسية
$dependencies = @(
    "Starman",
    "Plack",
    "DBI",
    "DBD::Pg",
    "Moose",
    "Template",
    "JSON",
    "DateTime",
    "Log::Log4perl",
    "Config::IniFiles"
)

foreach ($dep in $dependencies) {
    Write-Host "📦 تثبيت $dep..." -ForegroundColor Cyan
    cpanm --notest $dep
}

Write-Host "✅ تم تثبيت التبعيات" -ForegroundColor Green

# 9. إنشاء ملف الإعداد
Write-Host "⚙️ إنشاء ملف الإعداد..." -ForegroundColor Cyan
$configContent = @"
[main]
auth = DB
logging = 1

[environment]
PGUSER = postgres
PGPASSWORD = postgres123
PGDATABASE = ledgersmb_standalone
PGHOST = localhost
PGPORT = 5432

[database]
host = localhost
port = 5432
default_db = ledgersmb_standalone
contrib_dir = C:/Program Files/PostgreSQL/15/share/contrib

[paths]
localepath = locale/po
cssdir = UI/css

[server]
port = $Port
host = 0.0.0.0
workers = 2
"@

$configContent | Out-File -FilePath "$ledgerSMBPath\ledgersmb.conf" -Encoding UTF8
Write-Host "✅ تم إنشاء ملف الإعداد" -ForegroundColor Green

# 10. إنشاء سكريبت التشغيل
Write-Host "🚀 إنشاء سكريبت التشغيل..." -ForegroundColor Cyan
$startScript = @"
@echo off
echo Starting LedgerSMB on port $Port...
cd /d "$ledgerSMBPath"
set PGUSER=postgres
set PGPASSWORD=postgres123
set PGDATABASE=ledgersmb_standalone
set PGHOST=localhost
set PGPORT=5432
perl -Ilib starman --port $Port --host 0.0.0.0 --workers 2 bin/ledgersmb-server.psgi
pause
"@

$startScript | Out-File -FilePath "$ledgerSMBPath\start-ledgersmb.bat" -Encoding ASCII

# إنشاء سكريبت PowerShell للتشغيل
$startPSScript = @"
# تشغيل LedgerSMB
Write-Host "🚀 بدء تشغيل LedgerSMB على المنفذ $Port..." -ForegroundColor Green

Set-Location "$ledgerSMBPath"

`$env:PGUSER = "postgres"
`$env:PGPASSWORD = "postgres123"
`$env:PGDATABASE = "ledgersmb_standalone"
`$env:PGHOST = "localhost"
`$env:PGPORT = "5432"

Write-Host "🌐 LedgerSMB متاح على:" -ForegroundColor Cyan
Write-Host "   المحلي: http://localhost:$Port" -ForegroundColor Yellow
Write-Host "   الخارجي: http://[your-ip]:$Port" -ForegroundColor Yellow

perl -Ilib starman --port $Port --host 0.0.0.0 --workers 2 bin/ledgersmb-server.psgi
"@

$startPSScript | Out-File -FilePath "$ledgerSMBPath\start-ledgersmb.ps1" -Encoding UTF8

Write-Host "✅ تم إنشاء سكريبت التشغيل" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 تم الانتهاء من تثبيت LedgerSMB!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 معلومات التثبيت:" -ForegroundColor Cyan
Write-Host "   📍 مسار التثبيت: $ledgerSMBPath" -ForegroundColor White
Write-Host "   🌐 المنفذ: $Port" -ForegroundColor White
Write-Host "   🗄️ قاعدة البيانات: ledgersmb_standalone" -ForegroundColor White
Write-Host "   👤 مستخدم قاعدة البيانات: postgres" -ForegroundColor White
Write-Host "   🔑 كلمة مرور قاعدة البيانات: postgres123" -ForegroundColor White
Write-Host ""
Write-Host "🚀 لتشغيل LedgerSMB:" -ForegroundColor Cyan
Write-Host "   شغل: $ledgerSMBPath\start-ledgersmb.ps1" -ForegroundColor White
Write-Host ""
Write-Host "🌐 الوصول إلى LedgerSMB:" -ForegroundColor Cyan
Write-Host "   المحلي: http://localhost:$Port" -ForegroundColor Yellow
Write-Host "   الخارجي: http://[your-ip]:$Port" -ForegroundColor Yellow
Write-Host ""

Write-Host "✅ تم الانتهاء من جميع العمليات!" -ForegroundColor Green
