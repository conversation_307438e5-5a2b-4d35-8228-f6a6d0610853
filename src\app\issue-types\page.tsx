'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Scale,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

interface IssueType {
  id: number
  name: string
  description: string
  color: string
  cases_count: number
  is_active: boolean
  created_date: string
}

export default function IssueTypesPage() {
  const [issueTypes, setIssueTypes] = useState<IssueType[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchIssueTypes = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/issue-types')
      const result = await response.json()

      if (result.success) {
        setIssueTypes(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات أنواع القضايا')
        setIssueTypes([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setIssueTypes([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchIssueTypes()
  }, [])

  const filteredIssueTypes = issueTypes.filter(type =>
    type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    type.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getColorClass = (color: string) => {
    const colors: { [key: string]: string } = {
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800',
      red: 'bg-red-100 text-red-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      purple: 'bg-purple-100 text-purple-800',
      pink: 'bg-pink-100 text-pink-800'
    }
    return colors[color] || 'bg-gray-100 text-gray-800'
  }

  const handleDelete = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا النوع؟')) {
      setIssueTypes(issueTypes.filter(type => type.id !== id))
    }
  }

  const stats = {
    total: issueTypes.length,
    active: issueTypes.filter(t => t.is_active).length,
    totalCases: issueTypes.reduce((sum, type) => sum + type.cases_count, 0)
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Scale className="h-8 w-8 mr-3 text-blue-600" />
              أنواع القضايا
            </h1>
            <p className="text-gray-600 mt-1">إدارة أنواع القضايا القانونية</p>
          </div>

          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة نوع جديد
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Scale className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">أنواع القضايا</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Scale className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.active}</div>
                  <div className="text-sm text-gray-600">أنواع نشطة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Scale className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.totalCases}</div>
                  <div className="text-sm text-gray-600">إجمالي القضايا</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في أنواع القضايا..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredIssueTypes.map((type) => (
            <Card key={type.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Scale className="h-5 w-5 mr-2 text-blue-600" />
                    <span>{type.name}</span>
                  </div>
                  <Badge className={getColorClass(type.color)}>
                    {type.cases_count} قضية
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{type.description}</p>

                <div className="flex items-center justify-between">
                  <Badge variant={type.is_active ? "default" : "secondary"}>
                    {type.is_active ? 'نشط' : 'غير نشط'}
                  </Badge>

                  <div className="flex space-x-2 space-x-reverse">
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(type.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </MainLayout>
  )
}
