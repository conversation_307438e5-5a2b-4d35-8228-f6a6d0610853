'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { IssueSelect } from '@/components/ui/issue-select'
import { LineageSelect } from '@/components/ui/lineage-select'
import { EmployeeSelect } from '@/components/ui/employee-select'
import { ServiceSelect } from '@/components/ui/service-select'
import {
  Share2,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  Calculator,
  DollarSign,
  Users,
  FileText
} from 'lucide-react'

interface ServiceDistribution {
  service_id: number
  service_name: string
  percentage: number
  amount: number
  lawyer_id: number
  lawyer_name: string
}

interface CaseDistribution {
  id: number
  issue_id: number
  issue_title: string
  case_number: string
  case_amount: number
  lineage_id: number
  lineage_name: string
  admin_percentage: number
  commission_percentage: number
  admin_amount: number
  commission_amount: number
  remaining_amount: number
  service_distributions: ServiceDistribution[]
  created_date: string
}

export default function CaseDistributionPage() {
  const [distributions, setDistributions] = useState<CaseDistribution[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingDistribution, setEditingDistribution] = useState<CaseDistribution | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    issue_id: '',
    issue_data: null as any,
    lineage_id: '',
    lineage_data: null as any,
    service_distributions: [] as ServiceDistribution[]
  })

  const fetchDistributions = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/case-distribution')
      const result = await response.json()

      if (result.success) {
        setDistributions(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات توزيع القضايا')
        setDistributions([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setDistributions([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDistributions()
  }, [])

  const handleAddNew = () => {
    setEditingDistribution(null)
    setFormData({
      issue_id: '',
      issue_data: null,
      lineage_id: '',
      lineage_data: null,
      service_distributions: []
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleIssueChange = (issueId: string, issueData: any) => {
    setFormData({
      ...formData,
      issue_id: issueId,
      issue_data: issueData
    })
  }

  const handleLineageChange = (lineageId: string, lineageData: any) => {
    if (lineageData && formData.issue_data) {
      // حساب نسبة الإدارة ومبلغها
      const adminAmount = (formData.issue_data.amount * lineageData.admin_percentage) / 100
      const commissionAmount = (formData.issue_data.amount * (lineageData.commission_percentage || 0)) / 100
      const remainingAmount = formData.issue_data.amount - adminAmount - commissionAmount

      // بدء بجدول فارغ للخدمات
      const serviceDistributions: ServiceDistribution[] = []

      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        service_distributions: serviceDistributions
      })
    } else {
      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        service_distributions: []
      })
    }
  }

  const handleLawyerChange = (serviceId: number, lawyerId: string, lawyerData: any) => {
    const updatedDistributions = formData.service_distributions.map(dist =>
      dist.service_id === serviceId
        ? { ...dist, lawyer_id: Number(lawyerId), lawyer_name: lawyerData ? lawyerData.name : '' }
        : dist
    )

    setFormData({
      ...formData,
      service_distributions: updatedDistributions
    })
  }

  const addNewService = () => {
    if (!formData.issue_data || !formData.lineage_data) return

    const newServiceId = Math.max(0, ...formData.service_distributions.map(s => s.service_id)) + 1
    const newService: ServiceDistribution = {
      service_id: newServiceId,
      service_name: '',
      percentage: 0,
      amount: 0,
      lawyer_id: 0,
      lawyer_name: ''
    }

    setFormData({
      ...formData,
      service_distributions: [...formData.service_distributions, newService]
    })
  }

  const removeService = (serviceId: number) => {
    const newDistributions = formData.service_distributions.filter(dist => dist.service_id !== serviceId)
    setFormData({...formData, service_distributions: newDistributions})
  }

  const updateServiceName = (serviceId: number, serviceName: string) => {
    const newDistributions = formData.service_distributions.map(dist =>
      dist.service_id === serviceId
        ? { ...dist, service_name: serviceName }
        : dist
    )
    setFormData({...formData, service_distributions: newDistributions})
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/case-distribution', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة توزيع القضية بنجاح')
          fetchDistributions()
        } else {
          alert(result.error || 'فشل في إضافة توزيع القضية')
          return
        }
      }

      setIsModalOpen(false)
      setEditingDistribution(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const stats = {
    total: distributions.length,
    totalAmount: distributions.reduce((sum, d) => sum + d.case_amount, 0),
    totalAdminAmount: distributions.reduce((sum, d) => sum + d.admin_amount, 0)
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Share2 className="h-8 w-8 mr-3 text-green-600" />
              توزيع القضايا
            </h1>
            <p className="text-gray-600 mt-1">إدارة وتوزيع مبالغ القضايا على الخدمات والمحامين</p>
          </div>

          <Button onClick={handleAddNew} className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة توزيع جديد
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي التوزيعات</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي المبالغ</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAmount.toLocaleString()} ريال</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calculator className="h-8 w-8 text-purple-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">مبالغ الإدارة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAdminAmount.toLocaleString()} ريال</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* النموذج المحسن */}
        {isModalOpen && modalType === 'add' && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-7xl h-[95vh] flex flex-col">
              {/* رأس النافذة */}
              <div className="flex items-center justify-between p-4 border-b bg-gray-50">
                <h2 className="text-xl font-bold text-gray-900">📋 إضافة توزيع قضية جديد</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* المحتوى الرئيسي */}
              <div className="flex-1 overflow-visible p-4">

              <form onSubmit={handleSubmit} className="space-y-2">
                {/* القسم الأول: اختيار القضية والنسب المالية */}
                <Card>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-2 gap-6 mb-6">
                      {/* اختيار القضية */}
                      <div>
                        <IssueSelect
                          value={formData.issue_id}
                          onChange={handleIssueChange}
                          label="القضية"
                          placeholder="اختر القضية"
                          required
                        />
                      </div>

                      {/* النسب المالية */}
                      <div>
                        <LineageSelect
                          value={formData.lineage_id}
                          onChange={handleLineageChange}
                          label="النسب المالية"
                          placeholder="اختر النسب"
                          required
                        />
                      </div>
                    </div>

                    {/* عرض تفاصيل النسب والحسابات */}
                    {formData.lineage_data && formData.issue_data && (
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          {/* نسبة الإدارة */}
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Label className="text-purple-800 font-bold text-sm">نسبة الإدارة</Label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              step="0.1"
                              value={formData.lineage_data.admin_percentage || 0}
                              onChange={(e) => {
                                const newData = {...formData.lineage_data, admin_percentage: Number(e.target.value)}
                                setFormData({...formData, lineage_data: newData})
                              }}
                              className="w-16 text-center text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            />
                            <span className="text-purple-600 font-bold">%</span>
                            <span className="text-sm text-gray-600">
                              {((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100).toLocaleString()} ريال
                            </span>
                          </div>

                          {/* نسبة العمولة */}
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Label className="text-green-800 font-bold text-sm">العمولة</Label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              step="0.1"
                              value={formData.lineage_data.commission_percentage || 0}
                              onChange={(e) => {
                                const newData = {...formData.lineage_data, commission_percentage: Number(e.target.value)}
                                setFormData({...formData, lineage_data: newData})
                              }}
                              className="w-16 text-center text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            />
                            <span className="text-green-600 font-bold">%</span>
                            <span className="text-sm text-gray-600">
                              {((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100).toLocaleString()} ريال
                            </span>
                          </div>
                        </div>

                        {/* ملخص النسب - سطر واحد فقط */}
                        <div className="p-2 bg-blue-50 border border-blue-200 rounded text-center">
                          <div className="font-bold text-blue-800 text-sm">
                            = إجمالي النسبة {(Number(formData.lineage_data.admin_percentage || 0) + Number(formData.lineage_data.commission_percentage || 0)).toFixed(1)}%
                            مبلغ: {(((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) + ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)).toLocaleString()} ريال
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* رسالة توضيحية عند عدم اختيار النسب */}
                {(!formData.lineage_data || !formData.issue_data) && (
                  <Card>
                    <CardContent className="p-12">
                      <div className="text-center text-gray-500">
                        <div className="text-4xl mb-4">📊</div>
                        <h3 className="text-lg font-medium mb-2">اختر القضية والنسب المالية</h3>
                        <p className="text-sm">
                          {!formData.issue_data && !formData.lineage_data
                            ? "يرجى اختيار القضية ومجموعة النسب المالية لعرض تفاصيل التوزيع"
                            : !formData.issue_data
                            ? "يرجى اختيار القضية أولاً"
                            : "يرجى اختيار مجموعة النسب المالية لعرض تفاصيل التوزيع"
                          }
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* توزيع الخدمات على المحامين */}
                {formData.lineage_data && formData.issue_data && (
                  <Card className="overflow-visible">
                    <CardContent className="p-4 overflow-visible">
                      <div className="mb-1 p-1 bg-green-50 border border-green-200 rounded text-xs text-gray-600">
                        النسبة القابلة للتوزيع: <strong className="text-green-700">{(100 - Number(formData.lineage_data.admin_percentage || 0) - Number(formData.lineage_data.commission_percentage || 0)).toFixed(1)}%</strong>
                        - المبلغ المتاح: <strong className="text-green-700">{(formData.issue_data.amount - ((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) - ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)).toLocaleString()} ريال</strong>
                      </div>
                      {formData.service_distributions.length > 0 ? (
                        <div className="overflow-visible">
                          <table className="w-full border-collapse border-2 border-gray-300 rounded-lg">
                            <thead>
                              <tr className="bg-gradient-to-r from-green-100 to-blue-100">
                                <th className="border border-gray-300 p-1 text-right font-bold text-gray-700 bg-gray-50 text-xs w-24">الخدمة</th>
                                <th className="border border-gray-300 p-1 text-center font-bold text-gray-700 bg-blue-50 text-xs w-20">النسبة (%)</th>
                                <th className="border border-gray-300 p-1 text-center font-bold text-gray-700 bg-green-50 text-xs w-24">المبلغ (ريال)</th>
                                <th className="border border-gray-300 p-1 text-right font-bold text-gray-700 bg-purple-50 text-xs">المحامي المكلف</th>
                                <th className="border border-gray-300 p-1 text-center font-bold text-gray-700 bg-red-50 text-xs w-12">حذف</th>
                              </tr>
                            </thead>
                            <tbody>
                              {formData.service_distributions.map((service, index) => (
                                <tr key={service.service_id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                  {/* الخدمة */}
                                  <td className="border border-gray-300 p-1 w-24">
                                    <ServiceSelect
                                      value={service.service_name}
                                      onChange={(serviceName) => updateServiceName(service.service_id, serviceName)}
                                      placeholder="اختر الخدمة"
                                    />
                                  </td>

                                  {/* النسبة - قابلة للتحرير */}
                                  <td className="border border-gray-300 p-1 text-center">
                                    <Input
                                      type="number"
                                      min="0"
                                      max="100"
                                      step="0.1"
                                      value={service.percentage}
                                      onChange={(e) => {
                                        const newDistributions = [...formData.service_distributions]
                                        newDistributions[index].percentage = Number(e.target.value)
                                        // حساب المبلغ بناءً على النسبة
                                        if (formData.issue_data && formData.lineage_data) {
                                          const availableAmount = formData.issue_data.amount -
                                            ((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) -
                                            ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)
                                          newDistributions[index].amount = (availableAmount * Number(e.target.value)) / 100
                                        }
                                        setFormData({...formData, service_distributions: newDistributions})
                                      }}
                                      className="text-center font-bold text-blue-600 bg-blue-50 border border-blue-200 text-xs h-6 w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                    />
                                  </td>

                                  {/* المبلغ - قابل للتحرير */}
                                  <td className="border border-gray-300 p-1 text-center">
                                    <Input
                                      type="number"
                                      min="0"
                                      step="0.01"
                                      value={service.amount}
                                      onChange={(e) => {
                                        const newDistributions = [...formData.service_distributions]
                                        newDistributions[index].amount = Number(e.target.value)
                                        // حساب النسبة بناءً على المبلغ
                                        if (formData.issue_data && formData.lineage_data) {
                                          const availableAmount = formData.issue_data.amount -
                                            ((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) -
                                            ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)
                                          newDistributions[index].percentage = (Number(e.target.value) / availableAmount) * 100
                                        }
                                        setFormData({...formData, service_distributions: newDistributions})
                                      }}
                                      className="text-center font-bold text-green-600 bg-green-50 border border-green-200 text-xs h-6 w-16 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                    />
                                  </td>

                                  {/* المحامي */}
                                  <td className="border border-gray-300 p-1">
                                    <EmployeeSelect
                                      value={service.lawyer_id.toString()}
                                      onChange={(lawyerId, lawyerData) => handleLawyerChange(service.service_id, lawyerId, lawyerData)}
                                      label=""
                                      placeholder="اختر المحامي"
                                      required
                                    />
                                  </td>

                                  {/* حذف */}
                                  <td className="border border-gray-300 p-1 text-center">
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => removeService(service.service_id)}
                                      className="h-6 w-6 p-0 text-red-600 hover:text-red-800 hover:bg-red-50"
                                    >
                                      ✕
                                    </Button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <div className="text-lg mb-2">📋</div>
                          <p>لم يتم إضافة خدمات للتوزيع بعد</p>
                          <p className="text-sm">انقر على "إضافة خدمة" لبدء التوزيع</p>
                        </div>
                      )}

                    </CardContent>
                  </Card>
                )}
              </form>
              </div>

              {/* أزرار التحكم في أسفل النافذة */}
              <div className="border-t bg-gray-50 p-4 flex space-x-3 space-x-reverse">
                {/* زر إضافة خدمة */}
                <Button
                  type="button"
                  onClick={addNewService}
                  className="bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-4"
                  disabled={!formData.issue_data || !formData.lineage_data}
                >
                  ➕ إضافة خدمة
                </Button>

                <div className="flex-1"></div>

                {/* أزرار الحفظ والإلغاء */}
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsModalOpen(false)}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50 font-bold py-2 px-6 text-sm"
                >
                  <X className="h-4 w-4 mr-2" />
                  إلغاء
                </Button>
                <Button
                  onClick={(e) => {
                    e.preventDefault()
                    handleSubmit(e as any)
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 text-sm"
                  disabled={!formData.issue_id || !formData.lineage_id || formData.service_distributions.length === 0 || formData.service_distributions.some(s => !s.service_name.trim())}
                >
                  <Save className="h-4 w-4 mr-2" />
                  حفظ التوزيع
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
