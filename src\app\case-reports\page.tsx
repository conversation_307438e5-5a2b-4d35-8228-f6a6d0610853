'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { FileText, Calendar, Users, TrendingUp, BarChart3, PieChart } from 'lucide-react'

export default function CaseReportsPage() {
  const reports = [
    {
      id: 'cases-summary',
      title: 'ملخص القضايا',
      description: 'إحصائيات شاملة عن جميع القضايا',
      icon: BarChart3,
      color: 'bg-blue-500',
      status: 'قريباً'
    },
    {
      id: 'cases-by-status',
      title: 'القضايا حسب الحالة',
      description: 'توزيع القضايا حسب حالة كل قضية',
      icon: PieChart,
      color: 'bg-green-500',
      status: 'قريباً'
    },
    {
      id: 'cases-by-type',
      title: 'القضايا حسب النوع',
      description: 'تصنيف القضايا حسب نوع القضية',
      icon: FileText,
      color: 'bg-purple-500',
      status: 'قريباً'
    },
    {
      id: 'monthly-cases',
      title: 'القضايا الشهرية',
      description: 'تقرير القضايا المفتوحة والمغلقة شهرياً',
      icon: Calendar,
      color: 'bg-orange-500',
      status: 'قريباً'
    },
    {
      id: 'lawyer-performance',
      title: 'أداء المحامين',
      description: 'تقييم أداء المحامين في القضايا',
      icon: Users,
      color: 'bg-red-500',
      status: 'قريباً'
    },
    {
      id: 'financial-cases',
      title: 'التقرير المالي للقضايا',
      description: 'الإيرادات والمصروفات لكل قضية',
      icon: TrendingUp,
      color: 'bg-indigo-500',
      status: 'قريباً'
    }
  ]

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <FileText className="h-8 w-8 text-rose-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">تقارير القضايا</h1>
            <p className="text-gray-600">تقارير شاملة عن القضايا والأداء</p>
          </div>
        </div>

        {/* التقارير المتاحة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reports.map((report) => (
            <Card key={report.id} className="transition-all hover:shadow-lg">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className={`p-3 rounded-lg ${report.color} text-white`}>
                    <report.icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{report.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">{report.description}</p>
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    disabled
                    className="opacity-50"
                  >
                    {report.status}
                  </Button>
                  <span className="text-xs text-gray-500">قيد التطوير</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* معلومات */}
        <Card>
          <CardHeader>
            <CardTitle>معلومات التطوير</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">🚧 قيد التطوير</h3>
              <p className="text-sm text-yellow-700">
                تقارير القضايا قيد التطوير حالياً. ستكون متاحة في الإصدارات القادمة مع ميزات متقدمة للتحليل والإحصائيات.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
