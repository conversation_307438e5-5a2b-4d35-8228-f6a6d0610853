import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST() {
  try {
    console.log('🔧 بدء الإعداد المبسط...')

    // اختبار الاتصال
    await query('SELECT 1')
    console.log('✅ اتصال قاعدة البيانات ناجح')

    // إنشاء جدول بسيط للاختبار
    await query(`
      CREATE TABLE IF NOT EXISTS test_table (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    console.log('✅ تم إنشاء جدول الاختبار')

    // إدراج بيانات تجريبية
    await query(`
      INSERT INTO test_table (name) 
      VALUES ('اختبار 1'), ('اختبار 2') 
      ON CONFLICT DO NOTHING
    `)
    console.log('✅ تم إدراج البيانات التجريبية')

    // جلب البيانات للتأكد
    const result = await query('SELECT COUNT(*) as count FROM test_table')
    const count = parseInt(result.rows[0].count)
    console.log(`✅ عدد السجلات: ${count}`)

    return NextResponse.json({
      success: true,
      message: 'تم الإعداد المبسط بنجاح',
      data: {
        test_records: count,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('خطأ في الإعداد المبسط:', error)
    
    return NextResponse.json({
      success: false,
      error: 'فشل في الإعداد المبسط',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
