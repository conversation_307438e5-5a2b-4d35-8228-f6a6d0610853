'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Settings, Database, CheckCircle, AlertCircle, BookOpen } from 'lucide-react'

export default function SetupIntegratedPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSetup = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/setup-integrated-ledgersmb', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      
      if (data.success) {
        setResult(data)
      } else {
        setError(data.error || 'فشل في الإعداد')
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال')
    } finally {
      setLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Settings className="h-8 w-8 mr-3 text-blue-600" />
              إعداد النظام المحاسبي المدمج
            </h1>
            <p className="text-gray-600 mt-1">دمج LedgerSMB بالكامل مع النظام القانوني</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-6 w-6 mr-2" />
              إعداد قاعدة البيانات المدمجة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-gray-600">
              سيقوم هذا الإعداد بدمج LedgerSMB بالكامل مع النظام القانوني:
            </div>
            <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
              <li>إنشاء جداول دليل الحسابات المتكامل</li>
              <li>إنشاء جداول الشركات والكيانات</li>
              <li>إنشاء جداول المعاملات المالية</li>
              <li>إنشاء جداول الفواتير والمدفوعات</li>
              <li>إنشاء جداول المشاريع وإدخالات الوقت</li>
              <li>ربط النظام القانوني بالنظام المحاسبي</li>
              <li>إدراج دليل حسابات شامل للمكاتب القانونية</li>
            </ul>

            <div className="pt-4">
              <Button 
                onClick={handleSetup} 
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? 'جاري الإعداد...' : 'بدء الإعداد المدمج'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center text-red-800">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span className="font-medium">خطأ في الإعداد:</span>
              </div>
              <div className="mt-2 text-red-700 text-sm">
                {error}
              </div>
            </CardContent>
          </Card>
        )}

        {result && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center text-green-800 mb-4">
                <CheckCircle className="h-5 w-5 mr-2" />
                <span className="font-medium">تم الإعداد بنجاح!</span>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div className="text-center p-3 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{result.data.accounts_count}</div>
                  <div className="text-gray-600">الحسابات</div>
                </div>
                
                <div className="text-center p-3 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{result.data.entity_classes_count}</div>
                  <div className="text-gray-600">فئات الكيانات</div>
                </div>
                
                <div className="text-center p-3 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{result.data.clients_count}</div>
                  <div className="text-gray-600">العملاء</div>
                </div>
                
                <div className="text-center p-3 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{result.data.cases_count}</div>
                  <div className="text-gray-600">القضايا</div>
                </div>
                
                <div className="text-center p-3 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{result.data.employees_count}</div>
                  <div className="text-gray-600">الموظفين</div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                <div className="text-blue-800 text-sm">
                  ✅ تم إعداد النظام المحاسبي المدمج بنجاح. يمكنك الآن استخدام جميع مميزات LedgerSMB مدمجة مع النظام القانوني.
                </div>
                <div className="mt-2 space-x-2 space-x-reverse">
                  <Button asChild className="bg-blue-600 hover:bg-blue-700">
                    <a href="/chart-of-accounts">
                      <BookOpen className="h-4 w-4 mr-2" />
                      دليل الحسابات
                    </a>
                  </Button>
                  <Button asChild variant="outline">
                    <a href="/dashboard">العودة للوحة التحكم</a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* معلومات إضافية */}
        <Card>
          <CardHeader>
            <CardTitle>مميزات النظام المدمج</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium mb-2 text-blue-600">المميزات المحاسبية</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• دليل حسابات شامل للمكاتب القانونية</li>
                  <li>• قيود يومية مزدوجة القيد</li>
                  <li>• فواتير احترافية للأتعاب</li>
                  <li>• إدارة المدفوعات والمقبوضات</li>
                  <li>• تقارير مالية متقدمة</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-2 text-green-600">التكامل مع النظام القانوني</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• ربط القضايا بالمشاريع المحاسبية</li>
                  <li>• تتبع الوقت والتكاليف لكل قضية</li>
                  <li>• فواتير تلقائية للأتعاب القانونية</li>
                  <li>• تقارير ربحية لكل قضية</li>
                  <li>• إدارة مالية متكاملة للعملاء</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
