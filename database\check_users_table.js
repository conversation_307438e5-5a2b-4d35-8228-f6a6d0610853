// التحقق من هيكل جدول المستخدمين
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkUsersTable() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔍 التحقق من هيكل جدول المستخدمين...');
    await client.connect();

    // عرض أعمدة الجدول
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);

    console.log('📋 أعمدة جدول المستخدمين:');
    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

    // عرض عينة من البيانات
    const sampleData = await client.query('SELECT * FROM users LIMIT 3');
    console.log('\n📊 عينة من البيانات:');
    if (sampleData.rows.length > 0) {
      console.log('الأعمدة المتاحة:', Object.keys(sampleData.rows[0]));
      sampleData.rows.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.username || 'غير محدد'} - ${row.password_hash ? 'له كلمة مرور' : 'بدون كلمة مرور'}`);
      });
    } else {
      console.log('   لا توجد بيانات في الجدول');
    }

    // اختبار تسجيل دخول admin
    console.log('\n🔐 اختبار بيانات المستخدم admin:');
    const adminUser = await client.query(`
      SELECT id, username, password_hash, employee_id, status
      FROM users 
      WHERE username = 'admin'
    `);

    if (adminUser.rows.length > 0) {
      const admin = adminUser.rows[0];
      console.log(`   ✅ المستخدم admin موجود:`);
      console.log(`      - ID: ${admin.id}`);
      console.log(`      - Username: ${admin.username}`);
      console.log(`      - Has Password: ${admin.password_hash ? 'نعم' : 'لا'}`);
      console.log(`      - Employee ID: ${admin.employee_id || 'غير محدد'}`);
      console.log(`      - Status: ${admin.status || 'غير محدد'}`);
    } else {
      console.log('   ❌ المستخدم admin غير موجود');
    }

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkUsersTable();
