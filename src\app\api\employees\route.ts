import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الموظفين مع الربط
export async function GET() {
  try {
    const result = await query(`
      SELECT
        e.*
      FROM employees e
      ORDER BY e.id
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching employees:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الموظفين' },
      { status: 500 }
    )
  }
}

// POST - إضافة موظف جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name, position, department_id, branch_id, governorate_id, court_id,
      phone, email, salary, hire_date, status = 'نشط'
    } = body

    if (!name || !position) {
      return NextResponse.json(
        { success: false, error: 'الاسم والمنصب مطلوبان' },
        { status: 400 }
      )
    }

    // إنشاء رقم موظف تلقائي
    const employeeNumberResult = await query('SELECT COUNT(*) + 1 as next_number FROM employees')
    const employee_number = `EMP${employeeNumberResult.rows[0].next_number.toString().padStart(4, '0')}`

    const result = await query(`
      INSERT INTO employees (
        employee_number, name, position, department_id, branch_id,
        governorate_id, court_id, phone, email, salary, hire_date, status, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `, [
      employee_number, name, position,
      department_id || null, branch_id || null, governorate_id || null, court_id || null,
      phone, email, salary || null, hire_date, status, true
    ])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الموظف بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating employee:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الموظف' },
      { status: 500 }
    )
  }
}

// PUT - تحديث موظف
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, name, position, department, phone, email, address,
      id_number, salary, hire_date, status
    } = body

    if (!id || !name || !id_number) {
      return NextResponse.json(
        { success: false, error: 'المعرف والاسم ورقم الهوية مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE employees
      SET name = $1, position = $2, department = $3, phone = $4,
          email = $5, address = $6, id_number = $7, salary = $8,
          hire_date = $9, status = $10, updated_at = CURRENT_TIMESTAMP
      WHERE id = $11
      RETURNING *
    `, [name, position, department, phone, email, address, id_number, salary, hire_date, status, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الموظف بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating employee:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الموظف' },
      { status: 500 }
    )
  }
}

// DELETE - حذف موظف
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الموظف مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM employees WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الموظف غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الموظف بنجاح'
    })
  } catch (error) {
    console.error('Error deleting employee:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الموظف' },
      { status: 500 }
    )
  }
}
