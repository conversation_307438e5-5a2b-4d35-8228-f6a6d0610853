'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

interface PageGuardProps {
  children: React.ReactNode
  requiresAuth?: boolean
  fallbackPath?: string
}

// قائمة الصفحات المكتملة
const COMPLETED_PAGES = [
  '/',
  '/dashboard',
  '/login',
  '/clients',
  '/issues',
  '/case-distribution',
  '/courts',
  '/governorates',
  '/branches',
  '/percentages',
  '/employees',
  '/settings',
  '/settings/navigation-pages',
  '/under-construction',
  '/not-found'
]

// قائمة الصفحات قيد الإنشاء
const UNDER_CONSTRUCTION_PAGES = [
  '/service-distribution',
  '/lineages',
  '/journal-entries-new',
  '/advanced-reports',
  '/case-analytics',
  '/financial-reports',
  '/movements',
  '/follows',
  '/receipt-vouchers',
  '/payment-vouchers',
  '/contracts',
  '/documents',
  '/appointments',
  '/notifications',
  '/backup',
  '/system-logs'
]

export default function PageGuard({ children, requiresAuth = true, fallbackPath = '/under-construction' }: PageGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [isChecking, setIsChecking] = useState(true)
  const [shouldRender, setShouldRender] = useState(false)

  useEffect(() => {
    const checkPageStatus = async () => {
      try {
        // التحقق من حالة الصفحة
        if (COMPLETED_PAGES.includes(pathname)) {
          // صفحة مكتملة
          setShouldRender(true)
        } else if (UNDER_CONSTRUCTION_PAGES.includes(pathname)) {
          // صفحة قيد الإنشاء - توجيه لصفحة قيد الإنشاء
          router.replace('/under-construction')
          return
        } else {
          // صفحة غير معروفة - التحقق من وجودها
          try {
            const response = await fetch(pathname, { method: 'HEAD' })
            if (response.status === 404) {
              router.replace('/not-found')
              return
            }
          } catch (error) {
            // في حالة فشل الطلب، نفترض أن الصفحة قيد الإنشاء
            router.replace('/under-construction')
            return
          }
          
          setShouldRender(true)
        }

        // التحقق من المصادقة إذا كانت مطلوبة
        if (requiresAuth && pathname !== '/login') {
          const userSession = localStorage.getItem('userSession')
          if (!userSession) {
            router.replace('/login')
            return
          }
        }

      } catch (error) {
        console.error('Error checking page status:', error)
        // في حالة حدوث خطأ، نوجه لصفحة قيد الإنشاء
        router.replace('/under-construction')
      } finally {
        setIsChecking(false)
      }
    }

    checkPageStatus()
  }, [pathname, router, requiresAuth])

  // عرض شاشة التحميل أثناء التحقق
  if (isChecking) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">جاري التحميل...</h3>
              <p className="text-gray-600 text-sm">يتم التحقق من حالة الصفحة</p>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    )
  }

  // عرض المحتوى إذا كانت الصفحة صالحة
  if (shouldRender) {
    return <>{children}</>
  }

  // في حالة عدم وجود محتوى للعرض
  return null
}

// Hook للتحقق من حالة الصفحة
export function usePageStatus(pathname: string) {
  const [status, setStatus] = useState<'completed' | 'under-construction' | 'not-found' | 'checking'>('checking')

  useEffect(() => {
    if (COMPLETED_PAGES.includes(pathname)) {
      setStatus('completed')
    } else if (UNDER_CONSTRUCTION_PAGES.includes(pathname)) {
      setStatus('under-construction')
    } else {
      setStatus('not-found')
    }
  }, [pathname])

  return status
}

// مكون للتحقق السريع من حالة الصفحة
export function QuickPageCheck({ pathname }: { pathname: string }) {
  const status = usePageStatus(pathname)
  
  const getStatusInfo = () => {
    switch (status) {
      case 'completed':
        return { color: 'text-green-600', text: '✅ مكتملة' }
      case 'under-construction':
        return { color: 'text-yellow-600', text: '🚧 قيد الإنشاء' }
      case 'not-found':
        return { color: 'text-red-600', text: '❌ غير موجودة' }
      default:
        return { color: 'text-gray-600', text: '⏳ جاري التحقق...' }
    }
  }

  const { color, text } = getStatusInfo()

  return (
    <span className={`text-xs ${color} font-medium`}>
      {text}
    </span>
  )
}
