'use client'

import { useState, useEffect } from 'react'
import { X, Megaphone, Sparkles, Bell, Star, Zap } from 'lucide-react'

interface Announcement {
  id: number
  announcement_1: string
  announcement_2: string
  announcement_3: string
  announcement_4: string
  is_active: boolean
}

export function AnnouncementBanner() {
  const [announcement, setAnnouncement] = useState<Announcement | null>(null)
  const [isVisible, setIsVisible] = useState(true)
  const [isLoading, setIsLoading] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(0)

  // جلب الإعلانات
  const fetchAnnouncements = async () => {
    try {
      const response = await fetch('/api/settings/announcements')
      const data = await response.json()
      if (data.success && data.data && data.data.is_active) {
        setAnnouncement(data.data)
      }
    } catch (error) {
      console.error('Error fetching announcements:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAnnouncements()
  }, [])

  // جمع الإعلانات غير الفارغة
  const announcements = announcement ? [
    announcement.announcement_1,
    announcement.announcement_2,
    announcement.announcement_3,
    announcement.announcement_4
  ].filter(text => text && text.trim() !== '') : []

  // تدوير الإعلانات تلقائياً
  useEffect(() => {
    if (announcements.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % announcements.length)
      }, 4000) // تغيير كل 4 ثواني

      return () => clearInterval(interval)
    }
  }, [announcements.length])

  // إخفاء الشريط
  const handleClose = () => {
    setIsVisible(false)
  }

  // إذا كان يتم التحميل أو لا توجد إعلانات أو الشريط مخفي
  if (isLoading || !announcement || !isVisible || announcements.length === 0) {
    return null
  }

  return (
    <div className="relative overflow-hidden" style={{ height: '60px' }}>
      {/* الخلفية المتدرجة المتحركة */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-blue-600 via-indigo-600 to-purple-600 bg-[length:300%_100%] animate-gradient-x"></div>

      {/* طبقة التأثيرات */}
      <div className="absolute inset-0">
        {/* نجوم متحركة */}
        <div className="absolute top-2 left-10 w-1 h-1 bg-yellow-300 rounded-full animate-ping"></div>
        <div className="absolute top-4 right-20 w-1 h-1 bg-white rounded-full animate-pulse"></div>
        <div className="absolute bottom-3 left-32 w-1 h-1 bg-yellow-200 rounded-full animate-bounce"></div>
        <div className="absolute top-3 right-40 w-1 h-1 bg-blue-200 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>

        {/* موجات ضوئية */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-10 transform -skew-x-12 animate-shimmer"></div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="relative z-10 py-3 px-6 h-full">
        <div className="max-w-7xl mx-auto flex items-center justify-between h-full">
          {/* أيقونة الإعلان */}
          <div className="flex-shrink-0 mr-4">
            <div className="relative">
              <div className="absolute inset-0 bg-yellow-400 rounded-full animate-ping opacity-75"></div>
              <div className="relative bg-yellow-500 p-2 rounded-full shadow-lg">
                <Megaphone className="h-5 w-5 text-white animate-pulse" />
              </div>
            </div>
          </div>

          {/* محتوى الإعلان */}
          <div className="flex-1 mx-4 overflow-hidden">
            <div className="relative h-8 flex items-center">
              {announcements.map((text, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 flex items-center transition-all duration-1000 ease-in-out transform ${
                    index === currentIndex
                      ? 'opacity-100 translate-y-0'
                      : 'opacity-0 translate-y-4'
                  }`}
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    {/* أيقونة ديناميكية */}
                    <div className="flex-shrink-0">
                      {index === 0 && <Star className="h-4 w-4 text-yellow-300 animate-spin" />}
                      {index === 1 && <Sparkles className="h-4 w-4 text-blue-200 animate-bounce" />}
                      {index === 2 && <Bell className="h-4 w-4 text-green-300 animate-pulse" />}
                      {index === 3 && <Zap className="h-4 w-4 text-yellow-400 animate-ping" />}
                    </div>

                    {/* النص */}
                    <p className="text-white font-medium text-lg leading-relaxed tracking-wide drop-shadow-lg">
                      {text}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* مؤشرات التنقل */}
          {announcements.length > 1 && (
            <div className="flex-shrink-0 mx-4">
              <div className="flex space-x-2 space-x-reverse">
                {announcements.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? 'bg-white scale-125 shadow-lg'
                        : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}

          {/* زر الإغلاق */}
          <div className="flex-shrink-0">
            <button
              onClick={handleClose}
              className="group p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all duration-300 transform hover:scale-110"
              title="إغلاق الإعلان"
            >
              <X className="h-4 w-4 text-white group-hover:rotate-90 transition-transform duration-300" />
            </button>
          </div>
        </div>
      </div>

      {/* CSS للتأثيرات المخصصة */}
      <style jsx>{`
        @keyframes gradient-x {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }

        @keyframes shimmer {
          0% { transform: translateX(-100%) skewX(-12deg); }
          100% { transform: translateX(200%) skewX(-12deg); }
        }

        .animate-gradient-x {
          animation: gradient-x 8s ease infinite;
        }

        .animate-shimmer {
          animation: shimmer 3s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}
