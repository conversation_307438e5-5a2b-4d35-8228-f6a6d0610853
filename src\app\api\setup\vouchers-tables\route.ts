import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST() {
  try {
    // إنشاء جدول السندات الرئيسي
    await query(`
      CREATE TABLE IF NOT EXISTS vouchers_master (
          id SERIAL PRIMARY KEY,
          voucher_number VARCHAR(50) UNIQUE NOT NULL,
          reference_number VARCHAR(100),
          voucher_date DATE NOT NULL,
          voucher_type VARCHAR(50) NOT NULL,
          cost_center_id INTEGER,
          total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
          description TEXT,
          status VARCHAR(20) DEFAULT 'مسودة',
          created_by VARCHAR(100) DEFAULT 'النظام',
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء جدول المعاملات المالية
    await query(`
      CREATE TABLE IF NOT EXISTS financial_transactions (
          id SERIAL PRIMARY KEY,
          voucher_id INTEGER REFERENCES vouchers_master(id) ON DELETE CASCADE,
          account_id INTEGER NOT NULL,
          account_type VARCHAR(20) DEFAULT 'chart',
          debit_amount DECIMAL(15,2) DEFAULT 0,
          credit_amount DECIMAL(15,2) DEFAULT 0,
          description TEXT,
          transaction_date DATE NOT NULL,
          currency VARCHAR(20) DEFAULT 'ريال سعودي',
          exchange_rate DECIMAL(10,4) DEFAULT 1,
          created_by VARCHAR(100) DEFAULT 'النظام',
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء الفهارس
    await query(`CREATE INDEX IF NOT EXISTS idx_vouchers_master_voucher_number ON vouchers_master(voucher_number)`)
    await query(`CREATE INDEX IF NOT EXISTS idx_vouchers_master_voucher_type ON vouchers_master(voucher_type)`)
    await query(`CREATE INDEX IF NOT EXISTS idx_vouchers_master_voucher_date ON vouchers_master(voucher_date)`)
    await query(`CREATE INDEX IF NOT EXISTS idx_vouchers_master_cost_center ON vouchers_master(cost_center_id)`)

    await query(`CREATE INDEX IF NOT EXISTS idx_financial_transactions_voucher_id ON financial_transactions(voucher_id)`)
    await query(`CREATE INDEX IF NOT EXISTS idx_financial_transactions_account_id ON financial_transactions(account_id)`)
    await query(`CREATE INDEX IF NOT EXISTS idx_financial_transactions_date ON financial_transactions(transaction_date)`)

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء جداول السندات بنجاح'
    })

  } catch (error) {
    console.error('Error creating vouchers tables:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء جداول السندات' },
      { status: 500 }
    )
  }
}
