# تثبيت LedgerSMB على Windows
# LedgerSMB Installation Script for Windows

Write-Host "🚀 بدء تثبيت LedgerSMB على Windows..." -ForegroundColor Green

# 1. التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ يجب تشغيل هذا السكريبت كمدير" -ForegroundColor Red
    Write-Host "انقر بزر الماوس الأيمن على PowerShell واختر 'تشغيل كمدير'" -ForegroundColor Yellow
    exit 1
}

# 2. تثبيت Chocolatey إذا لم يكن موجوداً
Write-Host "📦 التحقق من Chocolatey..." -ForegroundColor Yellow
if (-not (Test-Path "$env:ProgramData\chocolatey\choco.exe")) {
    Write-Host "تثبيت Chocolatey..." -ForegroundColor Cyan
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    $env:Path += ";$env:ProgramData\chocolatey\bin"
    Write-Host "✅ تم تثبيت Chocolatey" -ForegroundColor Green
} else {
    Write-Host "✅ Chocolatey موجود" -ForegroundColor Green
}

# 3. تثبيت PostgreSQL
Write-Host "🐘 تثبيت PostgreSQL..." -ForegroundColor Yellow
try {
    choco install postgresql15 -y --params '/Password:postgres123'
    Write-Host "✅ تم تثبيت PostgreSQL" -ForegroundColor Green
} catch {
    Write-Host "⚠️ PostgreSQL قد يكون مثبت مسبقاً" -ForegroundColor Yellow
}

# 4. تثبيت Strawberry Perl
Write-Host "🍓 تثبيت Strawberry Perl..." -ForegroundColor Yellow
try {
    choco install strawberryperl -y
    Write-Host "✅ تم تثبيت Strawberry Perl" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Strawberry Perl قد يكون مثبت مسبقاً" -ForegroundColor Yellow
}

# 5. تثبيت Git
Write-Host "🔧 تثبيت Git..." -ForegroundColor Yellow
try {
    choco install git -y
    Write-Host "✅ تم تثبيت Git" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Git قد يكون مثبت مسبقاً" -ForegroundColor Yellow
}

# 6. تحديث متغيرات البيئة
Write-Host "🔄 تحديث متغيرات البيئة..." -ForegroundColor Yellow
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
$env:Path += ";C:\Program Files\PostgreSQL\15\bin"
$env:Path += ";C:\Strawberry\perl\bin"
$env:Path += ";C:\Strawberry\c\bin"
$env:Path += ";C:\Program Files\Git\bin"

# 7. إنشاء مجلد العمل
$workDir = "C:\LedgerSMB"
Write-Host "📁 إنشاء مجلد العمل: $workDir" -ForegroundColor Yellow
if (-not (Test-Path $workDir)) {
    New-Item -ItemType Directory -Path $workDir -Force
}
Set-Location $workDir

# 8. تحميل LedgerSMB
Write-Host "⬇️ تحميل LedgerSMB..." -ForegroundColor Yellow
if (Test-Path "LedgerSMB") {
    Remove-Item -Recurse -Force "LedgerSMB"
}

try {
    git clone https://github.com/ledgersmb/LedgerSMB.git
    Set-Location "LedgerSMB"
    Write-Host "✅ تم تحميل LedgerSMB" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في تحميل LedgerSMB" -ForegroundColor Red
    exit 1
}

# 9. إعداد قاعدة البيانات
Write-Host "🗄️ إعداد قاعدة البيانات..." -ForegroundColor Yellow
try {
    # بدء خدمة PostgreSQL
    Start-Service postgresql-x64-15
    
    # إنشاء قاعدة البيانات
    & "C:\Program Files\PostgreSQL\15\bin\createdb.exe" -U postgres ledgersmb
    Write-Host "✅ تم إنشاء قاعدة البيانات" -ForegroundColor Green
} catch {
    Write-Host "⚠️ قاعدة البيانات قد تكون موجودة مسبقاً" -ForegroundColor Yellow
}

# 10. تثبيت تبعيات Perl
Write-Host "📦 تثبيت تبعيات Perl..." -ForegroundColor Yellow
try {
    cpanm --installdeps . --notest
    Write-Host "✅ تم تثبيت التبعيات" -ForegroundColor Green
} catch {
    Write-Host "⚠️ بعض التبعيات قد تكون مثبتة مسبقاً" -ForegroundColor Yellow
}

# 11. إنشاء ملف الإعداد
Write-Host "⚙️ إنشاء ملف الإعداد..." -ForegroundColor Yellow
$configContent = @"
[main]
auth = DB
logging = 1

[environment]
# PostgreSQL connection
PGUSER = postgres
PGPASSWORD = postgres123
PGDATABASE = ledgersmb
PGHOST = localhost
PGPORT = 5432

[database]
host = localhost
port = 5432
default_db = ledgersmb
contrib_dir = C:/Program Files/PostgreSQL/15/share/contrib

[paths]
localepath = locale/po
cssdir = UI/css
"@

$configContent | Out-File -FilePath "ledgersmb.conf" -Encoding UTF8
Write-Host "✅ تم إنشاء ملف الإعداد" -ForegroundColor Green

# 12. إنشاء سكريبت التشغيل
Write-Host "🚀 إنشاء سكريبت التشغيل..." -ForegroundColor Yellow
$startScript = @"
@echo off
echo Starting LedgerSMB...
cd /d C:\LedgerSMB\LedgerSMB
perl -Ilib starman --port 5762 --workers 2 bin/ledgersmb-server.psgi
pause
"@

$startScript | Out-File -FilePath "start-ledgersmb.bat" -Encoding ASCII
Write-Host "✅ تم إنشاء سكريبت التشغيل" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 تم الانتهاء من التثبيت!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 الخطوات التالية:" -ForegroundColor Cyan
Write-Host "1. تشغيل LedgerSMB: انقر نقراً مزدوجاً على start-ledgersmb.bat" -ForegroundColor White
Write-Host "2. فتح المتصفح: http://localhost:5762" -ForegroundColor White
Write-Host "3. إعداد الشركة الأولى" -ForegroundColor White
Write-Host ""
Write-Host "📍 مسار التثبيت: $workDir\LedgerSMB" -ForegroundColor Yellow
Write-Host "📍 ملف الإعداد: $workDir\LedgerSMB\ledgersmb.conf" -ForegroundColor Yellow
Write-Host "📍 سكريبت التشغيل: $workDir\LedgerSMB\start-ledgersmb.bat" -ForegroundColor Yellow

# 13. محاولة تشغيل LedgerSMB
Write-Host ""
$response = Read-Host "هل تريد تشغيل LedgerSMB الآن؟ (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host "🚀 تشغيل LedgerSMB..." -ForegroundColor Green
    Start-Process -FilePath "start-ledgersmb.bat" -WorkingDirectory $workDir\LedgerSMB
    Write-Host "✅ تم تشغيل LedgerSMB في نافذة منفصلة" -ForegroundColor Green
    Write-Host "🌐 افتح المتصفح على: http://localhost:5762" -ForegroundColor Cyan
}
