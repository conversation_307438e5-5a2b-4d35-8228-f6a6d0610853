-- إنشاء جدول السندات الرئيسي
CREATE TABLE IF NOT EXISTS vouchers_master (
    id SERIAL PRIMARY KEY,
    voucher_number VARCHAR(50) UNIQUE NOT NULL,
    reference_number VARCHAR(100),
    voucher_date DATE NOT NULL,
    voucher_type VARCHAR(50) NOT NULL, -- 'سند قبض', 'سند صرف', 'قيد يومية'
    cost_center_id INTEGER REFERENCES cost_centers(id),
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    description TEXT,
    status VARCHAR(20) DEFAULT 'مسودة', -- 'مسودة', 'معتمد', 'ملغي'
    created_by VARCHAR(100) DEFAULT 'النظام',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المعاملات المالية
CREATE TABLE IF NOT EXISTS financial_transactions (
    id SERIAL PRIMARY KEY,
    voucher_id INTEGER REFERENCES vouchers_master(id) ON DELETE CASCADE,
    account_id INTEGER NOT NULL, -- يمكن أن يكون من chart_of_accounts أو clients أو employees
    account_type VARCHAR(20) DEFAULT 'chart', -- 'chart', 'client', 'employee'
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    transaction_date DATE NOT NULL,
    currency VARCHAR(20) DEFAULT 'ريال سعودي',
    exchange_rate DECIMAL(10,4) DEFAULT 1,
    created_by VARCHAR(100) DEFAULT 'النظام',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_vouchers_master_voucher_number ON vouchers_master(voucher_number);
CREATE INDEX IF NOT EXISTS idx_vouchers_master_voucher_type ON vouchers_master(voucher_type);
CREATE INDEX IF NOT EXISTS idx_vouchers_master_voucher_date ON vouchers_master(voucher_date);
CREATE INDEX IF NOT EXISTS idx_vouchers_master_cost_center ON vouchers_master(cost_center_id);

CREATE INDEX IF NOT EXISTS idx_financial_transactions_voucher_id ON financial_transactions(voucher_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_account_id ON financial_transactions(account_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_date ON financial_transactions(transaction_date);

-- إضافة قيود التحقق
ALTER TABLE financial_transactions 
ADD CONSTRAINT chk_transaction_amounts 
CHECK (debit_amount >= 0 AND credit_amount >= 0);

ALTER TABLE financial_transactions 
ADD CONSTRAINT chk_transaction_not_both_zero 
CHECK (debit_amount > 0 OR credit_amount > 0);

-- إضافة تعليقات للجداول
COMMENT ON TABLE vouchers_master IS 'جدول السندات الرئيسي - يحتوي على بيانات السندات الأساسية';
COMMENT ON TABLE financial_transactions IS 'جدول المعاملات المالية - يحتوي على تفاصيل القيود المحاسبية';

COMMENT ON COLUMN vouchers_master.voucher_number IS 'رقم السند - فريد لكل سند';
COMMENT ON COLUMN vouchers_master.voucher_type IS 'نوع السند - سند قبض، سند صرف، قيد يومية';
COMMENT ON COLUMN vouchers_master.status IS 'حالة السند - مسودة، معتمد، ملغي';

COMMENT ON COLUMN financial_transactions.account_type IS 'نوع الحساب - chart (دليل الحسابات)، client (موكل)، employee (موظف)';
COMMENT ON COLUMN financial_transactions.debit_amount IS 'المبلغ المدين';
COMMENT ON COLUMN financial_transactions.credit_amount IS 'المبلغ الدائن';
