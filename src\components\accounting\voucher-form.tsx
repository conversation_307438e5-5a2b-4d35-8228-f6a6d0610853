'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  X,
  Save,
  Plus,
  Trash2,
  Calculator,
  AlertCircle
} from 'lucide-react'

interface Account {
  id: string
  code: string
  name: string
  type: string
}

interface VoucherEntry {
  id: string
  debit_account_id: string
  debit_account_name: string
  credit_account_id: string
  credit_account_name: string
  amount: number
  description: string
}

interface VoucherFormProps {
  voucherType: 'payment' | 'receipt' | 'journal'
  isOpen: boolean
  onClose: () => void
  onSave: (data: any) => void
  editingData?: any
  mode: 'add' | 'edit' | 'view'
}

export function VoucherForm({ voucherType, isOpen, onClose, onSave, editingData, mode }: VoucherFormProps) {
  const [formData, setFormData] = useState({
    voucher_number: '',
    voucher_date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    status: 'pending'
  })

  const [entries, setEntries] = useState<VoucherEntry[]>([
    {
      id: '1',
      debit_account_id: '',
      debit_account_name: '',
      credit_account_id: '',
      credit_account_name: '',
      amount: 0,
      description: ''
    }
  ])

  const [accounts, setAccounts] = useState<Account[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [userSession, setUserSession] = useState<any>(null)

  // جلب بيانات المستخدم
  useEffect(() => {
    const session = localStorage.getItem('userSession')
    if (session) {
      setUserSession(JSON.parse(session))
    }
  }, [])

  // جلب الحسابات
  useEffect(() => {
    fetchAccounts()
  }, [])

  const fetchAccounts = async () => {
    try {
      // محاكاة جلب الحسابات - يجب ربطها بـ API حقيقي
      const mockAccounts = [
        { id: '1', code: '1001', name: 'الصندوق', type: 'asset' },
        { id: '2', code: '1002', name: 'البنك الأهلي', type: 'asset' },
        { id: '3', code: '2001', name: 'الموردون', type: 'liability' },
        { id: '4', code: '3001', name: 'رأس المال', type: 'equity' },
        { id: '5', code: '4001', name: 'إيرادات الأتعاب', type: 'revenue' },
        { id: '6', code: '5001', name: 'مصروفات الرواتب', type: 'expense' },
        { id: '7', code: '5002', name: 'مصروفات الإيجار', type: 'expense' },
        { id: '8', code: '5003', name: 'مصروفات الكهرباء', type: 'expense' }
      ]
      setAccounts(mockAccounts)
    } catch (error) {
      console.error('Error fetching accounts:', error)
    }
  }

  // إضافة قيد جديد
  const addEntry = () => {
    const newEntry: VoucherEntry = {
      id: Date.now().toString(),
      debit_account_id: '',
      debit_account_name: '',
      credit_account_id: '',
      credit_account_name: '',
      amount: 0,
      description: ''
    }
    setEntries([...entries, newEntry])
  }

  // حذف قيد
  const removeEntry = (entryId: string) => {
    if (entries.length > 1) {
      setEntries(entries.filter(entry => entry.id !== entryId))
    }
  }

  // تحديث قيد
  const updateEntry = (entryId: string, field: string, value: any) => {
    setEntries(entries.map(entry => {
      if (entry.id === entryId) {
        const updatedEntry = { ...entry, [field]: value }
        
        // تحديث اسم الحساب عند تغيير المعرف
        if (field === 'debit_account_id') {
          const account = accounts.find(acc => acc.id === value)
          updatedEntry.debit_account_name = account ? `${account.code} - ${account.name}` : ''
        } else if (field === 'credit_account_id') {
          const account = accounts.find(acc => acc.id === value)
          updatedEntry.credit_account_name = account ? `${account.code} - ${account.name}` : ''
        }
        
        return updatedEntry
      }
      return entry
    }))
  }

  // حساب الإجماليات
  const totalDebit = entries.reduce((sum, entry) => sum + (entry.amount || 0), 0)
  const totalCredit = totalDebit // في النظام المحاسبي المدين = الدائن
  const isBalanced = totalDebit === totalCredit && totalDebit > 0

  // حفظ السند
  const handleSave = async () => {
    if (!isBalanced) {
      alert('يجب أن يكون مجموع المدين مساوياً لمجموع الدائن')
      return
    }

    if (entries.some(entry => !entry.debit_account_id || !entry.credit_account_id || !entry.amount)) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const voucherData = {
      ...formData,
      voucher_type: voucherType,
      total_amount: totalDebit,
      entries: entries,
      user_id: userSession?.id
    }

    try {
      setIsLoading(true)
      await onSave(voucherData)
      onClose()
    } catch (error) {
      console.error('Error saving voucher:', error)
      alert('حدث خطأ في حفظ السند')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const getVoucherTitle = () => {
    switch (voucherType) {
      case 'payment': return 'سند دفع'
      case 'receipt': return 'سند قبض'
      case 'journal': return 'قيد يومي'
      default: return 'سند'
    }
  }

  const getModeTitle = () => {
    switch (mode) {
      case 'add': return 'إضافة'
      case 'edit': return 'تعديل'
      case 'view': return 'عرض'
      default: return ''
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[95vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b p-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold">
              {getModeTitle()} {getVoucherTitle()}
            </h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* معلومات السند الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle>معلومات السند</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="voucher_number">رقم السند *</Label>
                  <Input
                    id="voucher_number"
                    value={formData.voucher_number}
                    onChange={(e) => setFormData({...formData, voucher_number: e.target.value})}
                    disabled={mode === 'view'}
                    placeholder="سيتم توليده تلقائياً"
                  />
                </div>
                <div>
                  <Label htmlFor="voucher_date">التاريخ *</Label>
                  <Input
                    id="voucher_date"
                    type="date"
                    value={formData.voucher_date}
                    onChange={(e) => setFormData({...formData, voucher_date: e.target.value})}
                    disabled={mode === 'view'}
                  />
                </div>
                <div>
                  <Label htmlFor="reference">المرجع</Label>
                  <Input
                    id="reference"
                    value={formData.reference}
                    onChange={(e) => setFormData({...formData, reference: e.target.value})}
                    disabled={mode === 'view'}
                    placeholder="رقم الفاتورة أو المرجع"
                  />
                </div>
              </div>
              <div className="mt-4">
                <Label htmlFor="description">الوصف *</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  disabled={mode === 'view'}
                  placeholder="وصف العملية المحاسبية"
                />
              </div>
            </CardContent>
          </Card>

          {/* القيود المحاسبية */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>القيود المحاسبية</CardTitle>
                {mode !== 'view' && (
                  <Button onClick={addEntry} size="sm" variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة قيد
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {entries.map((entry, index) => (
                  <div key={entry.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">القيد رقم {index + 1}</h4>
                      {mode !== 'view' && entries.length > 1 && (
                        <Button
                          onClick={() => removeEntry(entry.id)}
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      {/* الحساب المدين */}
                      <div>
                        <Label>الحساب المدين *</Label>
                        <select
                          value={entry.debit_account_id}
                          onChange={(e) => updateEntry(entry.id, 'debit_account_id', e.target.value)}
                          disabled={mode === 'view'}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="">اختر الحساب المدين</option>
                          {accounts.map(account => (
                            <option key={account.id} value={account.id}>
                              {account.code} - {account.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* الحساب الدائن */}
                      <div>
                        <Label>الحساب الدائن *</Label>
                        <select
                          value={entry.credit_account_id}
                          onChange={(e) => updateEntry(entry.id, 'credit_account_id', e.target.value)}
                          disabled={mode === 'view'}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="">اختر الحساب الدائن</option>
                          {accounts.map(account => (
                            <option key={account.id} value={account.id}>
                              {account.code} - {account.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* المبلغ */}
                      <div>
                        <Label>المبلغ *</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={entry.amount || ''}
                          onChange={(e) => updateEntry(entry.id, 'amount', parseFloat(e.target.value) || 0)}
                          disabled={mode === 'view'}
                          placeholder="0.00"
                        />
                      </div>

                      {/* الوصف */}
                      <div>
                        <Label>وصف القيد</Label>
                        <Input
                          value={entry.description}
                          onChange={(e) => updateEntry(entry.id, 'description', e.target.value)}
                          disabled={mode === 'view'}
                          placeholder="وصف تفصيلي للقيد"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* ملخص الإجماليات */}
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <div className="text-center">
                      <div className="text-sm text-gray-600">إجمالي المدين</div>
                      <div className="text-lg font-bold text-blue-600">
                        {totalDebit.toLocaleString()} ريال
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-gray-600">إجمالي الدائن</div>
                      <div className="text-lg font-bold text-green-600">
                        {totalCredit.toLocaleString()} ريال
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    {isBalanced ? (
                      <Badge className="bg-green-100 text-green-800">
                        <Calculator className="h-4 w-4 mr-1" />
                        متوازن
                      </Badge>
                    ) : (
                      <Badge className="bg-red-100 text-red-800">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        غير متوازن
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* أزرار الحفظ */}
          {mode !== 'view' && (
            <div className="flex justify-end space-x-4 space-x-reverse">
              <Button variant="outline" onClick={onClose}>
                إلغاء
              </Button>
              <Button 
                onClick={handleSave} 
                disabled={!isBalanced || isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? 'جاري الحفظ...' : 'حفظ السند'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
