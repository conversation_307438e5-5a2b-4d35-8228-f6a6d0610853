import { NextRequest, NextResponse } from 'next/server'

// GET - جلب جميع النسب المالية
export async function GET() {
  try {
    // بيانات النسب المالية - الهيكل النهائي المطلوب (3 أعمدة فقط)
    const sampleData = [
      {
        id: 1,
        name: 'نسب القضايا المدنية',
        admin_percentage: 15.0
      },
      {
        id: 2,
        name: 'نسب القضايا التجارية',
        admin_percentage: 20.0
      },
      {
        id: 3,
        name: 'نسب القضايا الجنائية',
        admin_percentage: 10.0
      },
      {
        id: 4,
        name: 'نسب القضايا العمالية',
        admin_percentage: 25.0
      },
      {
        id: 5,
        name: 'نسب القضايا العقارية',
        admin_percentage: 18.0
      },
      {
        id: 6,
        name: 'نسب القضايا الإدارية',
        admin_percentage: 12.0
      },
      {
        id: 7,
        name: 'نسب القضايا الأسرية',
        admin_percentage: 8.0
      },
      {
        id: 8,
        name: 'نسب القضايا الضريبية',
        admin_percentage: 22.0
      }
    ]

    return NextResponse.json({
      success: true,
      data: sampleData
    })
  } catch (error) {
    console.error('Error fetching percentages:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات النسب المالية' },
      { status: 500 }
    )
  }
}

// POST - إضافة نسبة مالية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      admin_percentage
    } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم النسبة مطلوب' },
        { status: 400 }
      )
    }

    if (admin_percentage === undefined || admin_percentage < 0 || admin_percentage > 100) {
      return NextResponse.json(
        { success: false, error: 'نسبة الإدارة يجب أن تكون بين 0% و 100%' },
        { status: 400 }
      )
    }

    // محاكاة إضافة نسبة جديدة
    const newPercentage = {
      id: Date.now(),
      name,
      admin_percentage: Number(admin_percentage),
      created_date: new Date().toISOString().split('T')[0]
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة النسبة المالية بنجاح',
      data: newPercentage
    })
  } catch (error) {
    console.error('Error creating percentage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة النسبة المالية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث نسبة مالية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      name,
      admin_percentage
    } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم النسبة مطلوبان' },
        { status: 400 }
      )
    }

    if (admin_percentage === undefined || admin_percentage < 0 || admin_percentage > 100) {
      return NextResponse.json(
        { success: false, error: 'نسبة الإدارة يجب أن تكون بين 0% و 100%' },
        { status: 400 }
      )
    }

    // محاكاة تحديث النسبة
    return NextResponse.json({
      success: true,
      message: 'تم تحديث النسبة المالية بنجاح'
    })
  } catch (error) {
    console.error('Error updating percentage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث النسبة المالية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف نسبة مالية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف النسبة المالية مطلوب' },
        { status: 400 }
      )
    }

    // محاكاة حذف النسبة
    return NextResponse.json({
      success: true,
      message: 'تم حذف النسبة المالية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting percentage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف النسبة المالية' },
      { status: 500 }
    )
  }
}
