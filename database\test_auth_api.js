// اختبار API المصادقة
const fetch = require('node-fetch');

async function testAuthAPI() {
  try {
    console.log('🔄 اختبار API المصادقة...');
    
    const response = await fetch('http://localhost:7443/api/auth/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin',
        deviceId: 'test-device'
      })
    });

    console.log('📊 حالة الاستجابة:', response.status);
    
    const result = await response.text();
    console.log('📋 نص الاستجابة:', result);

    if (response.ok) {
      const jsonResult = JSON.parse(result);
      console.log('✅ تم تسجيل الدخول بنجاح!');
      console.log('👤 بيانات المستخدم:', jsonResult.user?.username);
      console.log('🔑 Token موجود:', !!jsonResult.token);
    } else {
      console.log('❌ فشل في تسجيل الدخول');
      try {
        const errorResult = JSON.parse(result);
        console.log('📝 رسالة الخطأ:', errorResult.error);
      } catch (e) {
        console.log('📝 خطأ غير مفهوم:', result);
      }
    }

  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
  }
}

testAuthAPI();
