import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع صفحات التنقل
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        id,
        page_title,
        page_url,
        page_description,
        category,
        keywords,
        is_active,
        created_date,
        updated_at
      FROM navigation_pages
      ORDER BY category, page_title
    `)

    return NextResponse.json({
      success: true,
      data: result.rows,
      total: result.rows.length,
      message: 'تم جلب جميع الصفحات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الصفحات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الصفحات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
