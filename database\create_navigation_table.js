// إنشاء جدول صفحات التنقل
const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function createNavigationTable() {
  try {
    await client.connect();
    console.log('🔗 تم الاتصال بقاعدة البيانات');

    // إنشاء جدول navigation_pages
    await client.query(`
      CREATE TABLE IF NOT EXISTS navigation_pages (
        id SERIAL PRIMARY KEY,
        page_title VARCHAR(255) NOT NULL,
        page_url VARCHAR(500) NOT NULL,
        page_description TEXT,
        category VARCHAR(100),
        keywords TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ تم إنشاء جدول navigation_pages بنجاح');

    // التحقق من وجود الجدول
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'navigation_pages'
    `);

    if (result.rows.length > 0) {
      console.log('✅ تم التأكد من وجود الجدول');
    } else {
      console.log('❌ الجدول غير موجود');
    }

  } catch (error) {
    console.error('❌ خطأ في إنشاء الجدول:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإنشاء
createNavigationTable();
