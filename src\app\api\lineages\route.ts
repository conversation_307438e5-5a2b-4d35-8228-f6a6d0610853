import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع النسب المالية
export async function GET() {
  try {
    const result = await query(`
      SELECT * FROM lineages 
      ORDER BY created_date DESC
    `)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching lineages:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات النسب المالية' },
      { status: 500 }
    )
  }
}

// POST - إضافة نسبة مالية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      name, management_share, court_share, commission_share, other_share 
    } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم المجموعة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من أن مجموع النسب لا يتجاوز 100%
    const total = (management_share || 0) + (court_share || 0) + (commission_share || 0) + (other_share || 0)
    if (total > 100) {
      return NextResponse.json(
        { success: false, error: 'مجموع النسب لا يمكن أن يتجاوز 100%' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO lineages (
        name, management_share, court_share, commission_share, other_share
      )
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [name, management_share || 0, court_share || 0, commission_share || 0, other_share || 0])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة النسبة المالية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating lineage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة النسبة المالية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث نسبة مالية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      id, name, management_share, court_share, commission_share, other_share 
    } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم المجموعة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من أن مجموع النسب لا يتجاوز 100%
    const total = (management_share || 0) + (court_share || 0) + (commission_share || 0) + (other_share || 0)
    if (total > 100) {
      return NextResponse.json(
        { success: false, error: 'مجموع النسب لا يمكن أن يتجاوز 100%' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE lineages 
      SET name = $1, management_share = $2, court_share = $3, 
          commission_share = $4, other_share = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [name, management_share || 0, court_share || 0, commission_share || 0, other_share || 0, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث النسبة المالية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating lineage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث النسبة المالية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف نسبة مالية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف النسبة المالية مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM lineages WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'النسبة المالية غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف النسبة المالية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting lineage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف النسبة المالية' },
      { status: 500 }
    )
  }
}
