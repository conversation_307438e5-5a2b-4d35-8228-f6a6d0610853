'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ClientSelect } from '@/components/ui/client-select'
import Link from 'next/link'
import {
  FileText,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Filter,
  Calendar,
  User,
  Building,
  Scale,
  X,
  Save
} from 'lucide-react'

interface Issue {
  id: number
  case_number: string
  title: string
  client_name: string
  client_phone: string
  court_name: string
  issue_type: string
  status: 'new' | 'pending' | 'in_progress' | 'completed' | 'cancelled'
  created_date: string
  next_hearing: string
  amount: number
  contract_method: 'بالجلسة' | 'بالعقد'
}

interface Court {
  id: number
  name: string
  branch_name: string
  governorate_name: string
}

export default function IssuesPage() {
  const [issues, setIssues] = useState<Issue[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingIssue, setEditingIssue] = useState<Issue | null>(null)
  const [courts, setCourts] = useState<Court[]>([])
  const [formData, setFormData] = useState({
    case_number: '',
    title: '',
    client_id: '',
    client_name: '',
    court_name: '',
    issue_type: '',
    status: 'pending',
    next_hearing: '',
    amount: '',
    contract_method: 'بالجلسة'
  })

  // جلب المحاكم من قاعدة البيانات
  const fetchCourts = async () => {
    try {
      const response = await fetch('/api/courts')
      const result = await response.json()
      if (result.success) {
        setCourts(result.data)
      }
    } catch (error) {
      console.error('Error fetching courts:', error)
    }
  }

  // جلب البيانات من قاعدة البيانات
  const fetchIssues = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/issues')
      const result = await response.json()

      if (result.success) {
        setIssues(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات القضايا')
        setIssues([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setIssues([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchIssues()
    fetchCourts()
  }, [])

  const filteredIssues = issues.filter(issue => {
    const matchesSearch =
      issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.client_name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || issue.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-purple-100 text-purple-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'in_progress': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'new': return 'جديدة'
      case 'pending': return 'معلقة'
      case 'in_progress': return 'قيد المعالجة'
      case 'completed': return 'مكتملة'
      case 'cancelled': return 'ملغية'
      default: return 'غير محدد'
    }
  }

  const handleAddNew = () => {
    setEditingIssue(null)
    setFormData({
      case_number: '',
      title: '',
      client_id: '',
      client_name: '',
      court_name: '',
      issue_type: '',
      status: 'new',
      next_hearing: '',
      amount: '',
      contract_method: 'بالجلسة'
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleClientChange = (clientId: string, clientData: any) => {
    setFormData({
      ...formData,
      client_id: clientId,
      client_name: clientData ? clientData.name : ''
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        // إضافة رقم المستخدم تلقائياً (يمكن الحصول عليه من الجلسة لاحقاً)
        const dataToSubmit = {
          ...formData,
          created_by: 1 // رقم المستخدم الافتراضي - يجب تحديثه ليأخذ من الجلسة
        }

        const response = await fetch('/api/issues', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dataToSubmit)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في إضافة القضية')
          return
        }
      } else if (modalType === 'edit' && editingIssue) {
        const response = await fetch(`/api/issues/${editingIssue.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم تحديث القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في تحديث القضية')
          return
        }
      }

      setIsModalOpen(false)
      setEditingIssue(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const handleViewIssue = (issue: Issue) => {
    setEditingIssue(issue)
    setFormData({
      case_number: issue.case_number,
      title: issue.title,
      client_id: '',
      client_name: issue.client_name,
      court_name: issue.court_name,
      issue_type: issue.issue_type,
      status: issue.status,
      next_hearing: issue.next_hearing,
      amount: issue.amount.toString(),
      contract_method: issue.contract_method
    })
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleEditIssue = (issue: Issue) => {
    setEditingIssue(issue)
    setFormData({
      case_number: issue.case_number,
      title: issue.title,
      client_id: '',
      client_name: issue.client_name,
      court_name: issue.court_name,
      issue_type: issue.issue_type,
      status: issue.status,
      next_hearing: issue.next_hearing,
      amount: issue.amount.toString(),
      contract_method: issue.contract_method
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleDeleteIssue = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه القضية؟')) return

    try {
      const response = await fetch(`/api/issues/${id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        alert('تم حذف القضية بنجاح')
        fetchIssues()
      } else {
        alert(result.error || 'فشل في حذف القضية')
      }
    } catch (error) {
      console.error('Error deleting issue:', error)
      alert('حدث خطأ في حذف القضية')
    }
  }

  const stats = {
    total: issues.length,
    new: issues.filter(i => i.status === 'new').length,
    pending: issues.filter(i => i.status === 'pending').length,
    in_progress: issues.filter(i => i.status === 'in_progress').length,
    completed: issues.filter(i => i.status === 'completed').length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والإحصائيات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Scale className="h-8 w-8 mr-3 text-blue-600" />
              إدارة القضايا
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع القضايا القانونية</p>
          </div>

          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة قضية جديدة
          </Button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي القضايا</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.new}</div>
                  <div className="text-sm text-gray-600">قضايا جديدة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Scale className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.in_progress}</div>
                  <div className="text-sm text-gray-600">قيد المعالجة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.completed}</div>
                  <div className="text-sm text-gray-600">قضايا مكتملة</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* شريط البحث والفلترة */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث برقم القضية، العنوان، أو اسم الموكل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="new">جديدة</option>
                  <option value="pending">معلقة</option>
                  <option value="in_progress">قيد المعالجة</option>
                  <option value="completed">مكتملة</option>
                  <option value="cancelled">ملغية</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول القضايا */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              قائمة القضايا ({filteredIssues.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b bg-gray-50">
                    <th className="text-right p-4 font-semibold text-lg">رقم القضية</th>
                    <th className="text-right p-4 font-semibold text-lg">العنوان</th>
                    <th className="text-right p-4 font-semibold text-lg">الموكل</th>
                    <th className="text-center p-4 font-semibold text-lg">الهاتف</th>
                    <th className="text-center p-4 font-semibold text-lg">المحكمة</th>
                    <th className="text-center p-4 font-semibold text-lg">النوع</th>
                    <th className="text-center p-4 font-semibold text-lg">طريقة التعاقد</th>
                    <th className="text-center p-4 font-semibold text-lg">الحالة</th>
                    <th className="text-center p-4 font-semibold text-lg">المبلغ</th>
                    <th className="text-center p-4 font-semibold text-lg">الجلسة القادمة</th>
                    <th className="text-center p-4 font-semibold text-lg">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredIssues.map((issue) => (
                    <tr key={issue.id} className="border-b hover:bg-gray-50">
                      <td className="p-4 font-medium text-blue-600">{issue.case_number}</td>
                      <td className="p-4 font-medium">{issue.title}</td>
                      <td className="p-4">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-400" />
                          {issue.client_name}
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center">
                          <span className="text-blue-600 font-medium">
                            {issue.client_phone || 'غير محدد'}
                          </span>
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center">
                          <Building className="h-4 w-4 mr-2 text-gray-400" />
                          <span className="font-medium">{issue.court_name}</span>
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          {issue.issue_type}
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <Badge
                          variant="outline"
                          className={issue.contract_method === 'بالجلسة' ? 'bg-green-50 text-green-700' : 'bg-purple-50 text-purple-700'}
                        >
                          {issue.contract_method}
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <Badge className={getStatusColor(issue.status)}>
                          {getStatusText(issue.status)}
                        </Badge>
                      </td>
                      <td className="text-center p-4 font-medium text-green-600">
                        {issue.amount ? issue.amount.toLocaleString() : '0'} ريال
                      </td>
                      <td className="text-center p-4">
                        {issue.next_hearing ? (
                          <div className="flex items-center justify-center">
                            <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                            <span className="font-medium">{issue.next_hearing}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="text-center p-4">
                        <div className="flex justify-center space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewIssue(issue)}
                            className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 hover:border-blue-300"
                            title="عرض تفاصيل القضية"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditIssue(issue)}
                            className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 hover:border-yellow-300"
                            title="تعديل القضية"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteIssue(issue.id)}
                            className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300"
                            title="حذف القضية"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Modal لإضافة قضية جديدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto border-t-4 border-blue-500">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-gray-800 flex items-center">
                  <FileText className="h-6 w-6 mr-3 text-blue-600" />
                  {modalType === 'add' && '📋 إضافة قضية جديدة'}
                  {modalType === 'edit' && '✏️ تعديل القضية'}
                  {modalType === 'view' && '👁️ عرض تفاصيل القضية'}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)} className="hover:bg-red-50 hover:text-red-600">
                  <X className="h-5 w-5" />
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* الصف الأول: رقم القضية وعنوان القضية */}
                <div className="grid grid-cols-10 gap-6">
                  <div className="col-span-3">
                    <Label htmlFor="case_number" className="text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2">
                      📋 رقم القضية *
                    </Label>
                    <Input
                      id="case_number"
                      value={formData.case_number}
                      onChange={(e) => setFormData({...formData, case_number: e.target.value})}
                      required={modalType !== 'view'}
                      readOnly={modalType === 'view'}
                      className={`h-10 ${modalType === 'view' ? 'bg-gray-50' : 'bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors'}`}
                      placeholder="أدخل رقم القضية..."
                    />
                  </div>
                  <div className="col-span-7">
                    <Label htmlFor="title" className="text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2">
                      📝 عنوان القضية *
                    </Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      required={modalType !== 'view'}
                      readOnly={modalType === 'view'}
                      className={`h-10 ${modalType === 'view' ? 'bg-gray-50' : 'bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors'}`}
                      placeholder="أدخل عنوان القضية..."
                    />
                  </div>
                </div>

                {/* الصف الثاني: الموكل والمحكمة */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="col-span-1">
                    <Label className="text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2">
                      👤 الموكل *
                    </Label>
                    <div className="bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md">
                      <ClientSelect
                        value={formData.client_id}
                        onChange={handleClientChange}
                        label=""
                        placeholder="اختر الموكل..."
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="court_name" className="text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2">
                      🏛️ المحكمة
                    </Label>
                    <select
                      id="court_name"
                      value={formData.court_name}
                      onChange={(e) => setFormData({...formData, court_name: e.target.value})}
                      className="w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors"
                    >
                      <option value="">اختر المحكمة...</option>
                      {courts.map((court) => (
                        <option key={court.id} value={court.name}>
                          {court.name} - {court.governorate_name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* الصف الثالث: طريقة التعاقد وحالة القضية ونوع القضية */}
                <div className="grid grid-cols-6 gap-4">
                  <div className="col-span-1">
                    <Label htmlFor="contract_method" className="text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2">
                      📄 طريقة التعاقد
                    </Label>
                    <select
                      id="contract_method"
                      value={formData.contract_method}
                      onChange={(e) => setFormData({...formData, contract_method: e.target.value})}
                      className="w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm"
                    >
                      <option value="بالجلسة">بالجلسة</option>
                      <option value="بالعقد">بالعقد</option>
                    </select>
                  </div>
                  <div className="col-span-1">
                    <Label htmlFor="status" className="text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2">
                      🔄 حالة القضية
                    </Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => setFormData({...formData, status: e.target.value})}
                      className="w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm"
                    >
                      <option value="new">جديدة</option>
                      <option value="pending">معلقة</option>
                      <option value="in_progress">قيد المعالجة</option>
                      <option value="completed">مكتملة</option>
                      <option value="cancelled">ملغية</option>
                    </select>
                  </div>
                  <div className="col-span-1">
                    <Label htmlFor="issue_type" className="text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2">
                      ⚖️ نوع القضية
                    </Label>
                    <select
                      id="issue_type"
                      value={formData.issue_type}
                      onChange={(e) => setFormData({...formData, issue_type: e.target.value})}
                      className="w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm"
                    >
                      <option value="">اختر نوع القضية...</option>
                      <option value="مدنية">مدنية</option>
                      <option value="تجارية">تجارية</option>
                      <option value="جنائية">جنائية</option>
                      <option value="أحوال شخصية">أحوال شخصية</option>
                      <option value="عمالية">عمالية</option>
                      <option value="إدارية">إدارية</option>
                    </select>
                  </div>
                  <div className="col-span-3">
                    {/* مساحة فارغة للتوازن */}
                  </div>
                </div>

                {/* الصف الرابع: قيمة القضية وتاريخ العقد */}
                <div className="grid grid-cols-4 gap-6">
                  <div className="col-span-1">
                    <Label htmlFor="amount" className="text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2">
                      💰 قيمة القضية
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      value={formData.amount}
                      onChange={(e) => setFormData({...formData, amount: e.target.value})}
                      className="h-10 bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors text-sm"
                      placeholder="أدخل قيمة القضية..."
                    />
                  </div>
                  <div className="col-span-1">
                    <Label htmlFor="next_hearing" className="text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2">
                      📅 تاريخ العقد
                    </Label>
                    <Input
                      id="next_hearing"
                      type="date"
                      value={formData.next_hearing}
                      onChange={(e) => setFormData({...formData, next_hearing: e.target.value})}
                      className="h-10 bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors text-sm"
                    />
                  </div>
                  <div className="col-span-2">
                    {/* مساحة فارغة للتوازن */}
                  </div>
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-200">
                  {modalType !== 'view' && (
                    <Button type="submit" className="flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold">
                      <Save className="h-5 w-5 mr-2" />
                      {modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'}
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                    className={`${modalType === 'view' ? 'w-full' : 'flex-1'} h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50`}
                  >
                    {modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
