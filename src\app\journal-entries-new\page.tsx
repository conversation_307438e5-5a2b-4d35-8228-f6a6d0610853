'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { VoucherForm } from '@/components/accounting/voucher-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  TrendingUp,
  CheckCircle,
  Clock,
  XCircle,
  Calculator
} from 'lucide-react'

interface JournalEntry {
  id: number
  voucher_number: string
  voucher_date: string
  description: string
  total_amount: number
  reference: string
  status: string
  created_by: string
  created_at: string
  entries: any[]
}

export default function JournalEntriesPage() {
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [filteredEntries, setFilteredEntries] = useState<JournalEntry[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingEntry, setEditingEntry] = useState<JournalEntry | null>(null)
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add')

  // جلب البيانات
  useEffect(() => {
    fetchEntries()
  }, [])

  // تطبيق الفلاتر
  useEffect(() => {
    let filtered = entries

    if (searchTerm) {
      filtered = filtered.filter(entry =>
        entry.voucher_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.reference.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(entry => entry.status === statusFilter)
    }

    setFilteredEntries(filtered)
  }, [entries, searchTerm, statusFilter])

  const fetchEntries = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/journal-entries')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setEntries(result.data)
        }
      }
    } catch (error) {
      console.error('Error fetching journal entries:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddNew = () => {
    setEditingEntry(null)
    setFormMode('add')
    setIsFormOpen(true)
  }

  const handleEdit = (entry: JournalEntry) => {
    setEditingEntry(entry)
    setFormMode('edit')
    setIsFormOpen(true)
  }

  const handleView = (entry: JournalEntry) => {
    setEditingEntry(entry)
    setFormMode('view')
    setIsFormOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا القيد؟')) {
      try {
        const response = await fetch(`/api/journal-entries/${id}`, {
          method: 'DELETE'
        })
        if (response.ok) {
          fetchEntries()
        }
      } catch (error) {
        console.error('Error deleting entry:', error)
      }
    }
  }

  const handleSaveEntry = async (entryData: any) => {
    try {
      const url = editingEntry 
        ? `/api/journal-entries/${editingEntry.id}`
        : '/api/journal-entries'
      
      const method = editingEntry ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(entryData)
      })

      if (response.ok) {
        fetchEntries()
        setIsFormOpen(false)
      }
    } catch (error) {
      console.error('Error saving entry:', error)
      throw error
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'معتمد'
      case 'pending': return 'في الانتظار'
      case 'rejected': return 'مرفوض'
      default: return 'غير محدد'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />
      case 'pending': return <Clock className="h-4 w-4" />
      case 'rejected': return <XCircle className="h-4 w-4" />
      default: return null
    }
  }

  // حساب الإحصائيات
  const stats = {
    total: entries.length,
    approved: entries.filter(e => e.status === 'approved').length,
    pending: entries.filter(e => e.status === 'pending').length,
    totalAmount: entries.reduce((sum, e) => sum + (e.total_amount || 0), 0)
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <FileText className="h-8 w-8 mr-3 text-blue-600" />
              القيود اليومية
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع القيود المحاسبية اليومية</p>
          </div>
          
          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة قيد يومي جديد
          </Button>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي القيود</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.approved}</div>
                  <div className="text-sm text-gray-600">قيود معتمدة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.pending}</div>
                  <div className="text-sm text-gray-600">في الانتظار</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calculator className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {stats.totalAmount.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">إجمالي المبالغ</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في القيود اليومية..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="pending">في الانتظار</option>
                  <option value="approved">معتمد</option>
                  <option value="rejected">مرفوض</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول القيود */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              قائمة القيود اليومية ({filteredEntries.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
              </div>
            ) : filteredEntries.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد قيود يومية</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">رقم القيد</th>
                      <th className="text-center p-3 font-semibold">التاريخ</th>
                      <th className="text-right p-3 font-semibold">الوصف</th>
                      <th className="text-center p-3 font-semibold">المبلغ الإجمالي</th>
                      <th className="text-right p-3 font-semibold">المرجع</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-right p-3 font-semibold">المنشئ</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEntries.map((entry) => (
                      <tr key={entry.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{entry.voucher_number}</td>
                        <td className="text-center p-3">
                          <div className="flex items-center justify-center">
                            <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                            {entry.voucher_date}
                          </div>
                        </td>
                        <td className="p-3">{entry.description}</td>
                        <td className="text-center p-3">
                          <div className="flex items-center justify-center">
                            <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                            {(entry.total_amount || 0).toLocaleString()}
                          </div>
                        </td>
                        <td className="p-3">{entry.reference}</td>
                        <td className="text-center p-3">
                          <Badge className={getStatusColor(entry.status)}>
                            <div className="flex items-center">
                              {getStatusIcon(entry.status)}
                              <span className="mr-1">{getStatusText(entry.status)}</span>
                            </div>
                          </Badge>
                        </td>
                        <td className="p-3">{entry.created_by}</td>
                        <td className="text-center p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline" onClick={() => handleView(entry)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(entry)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(entry.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج القيد */}
        <VoucherForm
          voucherType="journal"
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onSave={handleSaveEntry}
          editingData={editingEntry}
          mode={formMode}
        />
      </div>
    </MainLayout>
  )
}
