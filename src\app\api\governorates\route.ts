import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع المحافظات من قاعدة البيانات
export async function GET() {
  try {
    // جلب البيانات من قاعدة البيانات الحقيقية
    const result = await query('SELECT * FROM governorates ORDER BY id')

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching governorates:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات المحافظات' },
      { status: 500 }
    )
  }
}

// POST - إضافة محافظة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, code, is_active = true } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم المحافظة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO governorates (name, code, is_active)
      VALUES ($1, $2, $3)
      RETURNING *
    `, [name, code, is_active])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المحافظة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating governorate:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المحافظة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث محافظة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, code, is_active } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم المحافظة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE governorates
      SET name = $1, code = $2, is_active = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `, [name, code, is_active, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المحافظة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating governorate:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات المحافظة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف محافظة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المحافظة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM governorates WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المحافظة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المحافظة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting governorate:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المحافظة' },
      { status: 500 }
    )
  }
}
