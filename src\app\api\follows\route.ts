import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع المتابعات
export async function GET() {
  try {
    const result = await query(`
      SELECT * FROM follows 
      ORDER BY created_date DESC
    `)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching follows:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات المتابعات' },
      { status: 500 }
    )
  }
}

// POST - إضافة متابعة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      case_number, case_title, client_name, follow_type, description, 
      due_date, status = 'pending', priority = 'medium', created_by 
    } = body

    if (!case_number || !follow_type || !description) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية ونوع المتابعة والوصف مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO follows (
        case_number, case_title, client_name, follow_type, description, 
        due_date, status, priority, created_by
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [
      case_number, case_title, client_name, follow_type, description, 
      due_date, status, priority, created_by
    ])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المتابعة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating follow:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المتابعة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث متابعة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      id, case_number, case_title, client_name, follow_type, description, 
      due_date, status, priority, created_by 
    } = body

    if (!id || !case_number || !follow_type || !description) {
      return NextResponse.json(
        { success: false, error: 'المعرف ورقم القضية ونوع المتابعة والوصف مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE follows 
      SET case_number = $1, case_title = $2, client_name = $3, 
          follow_type = $4, description = $5, due_date = $6, 
          status = $7, priority = $8, created_by = $9, 
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $10
      RETURNING *
    `, [
      case_number, case_title, client_name, follow_type, description, 
      due_date, status, priority, created_by, id
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المتابعة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating follow:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المتابعة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف متابعة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المتابعة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM follows WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المتابعة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المتابعة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting follow:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المتابعة' },
      { status: 500 }
    )
  }
}
