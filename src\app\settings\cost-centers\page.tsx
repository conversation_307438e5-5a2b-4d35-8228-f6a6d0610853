'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Edit,
  Trash2,
  Building2,
  Search,
  Save,
  X
} from 'lucide-react'

interface CostCenter {
  id: number
  center_code: string
  center_name: string
  center_level: number
  is_active: boolean
  created_date: string
}

export default function CostCentersPage() {
  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingCenter, setEditingCenter] = useState<CostCenter | null>(null)
  const [formData, setFormData] = useState({
    center_code: '',
    center_name: '',
    center_level: 1,
    is_active: true
  })

  // جلب مراكز التكلفة
  const fetchCostCenters = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/cost-centers')
      const data = await response.json()
      if (data.success) {
        setCostCenters(data.data)
      }
    } catch (error) {
      console.error('Error fetching cost centers:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCostCenters()
  }, [])

  // تصفية مراكز التكلفة
  const filteredCenters = costCenters.filter(center =>
    center.center_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    center.center_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // إضافة مركز تكلفة جديد
  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await fetch('/api/accounting/cost-centers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const data = await response.json()
      if (data.success) {
        await fetchCostCenters()
        setIsAddDialogOpen(false)
        setFormData({
          center_code: '',
          center_name: '',
          center_level: 1,
          is_active: true
        })
        alert('تم إضافة مركز التكلفة بنجاح')
      } else {
        alert(`خطأ: ${data.error}`)
      }
    } catch (error) {
      console.error('Error adding cost center:', error)
      alert('حدث خطأ أثناء إضافة مركز التكلفة')
    }
  }

  // تعديل مركز تكلفة
  const handleEdit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingCenter) return

    try {
      const response = await fetch('/api/accounting/cost-centers', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: editingCenter.id,
          ...formData
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchCostCenters()
        setIsEditDialogOpen(false)
        setEditingCenter(null)
        alert('تم تحديث مركز التكلفة بنجاح')
      } else {
        alert(`خطأ: ${data.error}`)
      }
    } catch (error) {
      console.error('Error updating cost center:', error)
      alert('حدث خطأ أثناء تحديث مركز التكلفة')
    }
  }

  // حذف مركز تكلفة
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف مركز التكلفة؟')) return

    try {
      const response = await fetch(`/api/accounting/cost-centers?id=${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      if (data.success) {
        await fetchCostCenters()
        alert('تم حذف مركز التكلفة بنجاح')
      } else {
        alert(`خطأ: ${data.error}`)
      }
    } catch (error) {
      console.error('Error deleting cost center:', error)
      alert('حدث خطأ أثناء حذف مركز التكلفة')
    }
  }

  // فتح نافذة التعديل
  const openEditDialog = (center: CostCenter) => {
    setEditingCenter(center)
    setFormData({
      center_code: center.center_code,
      center_name: center.center_name,
      center_level: center.center_level,
      is_active: center.is_active
    })
    setIsEditDialogOpen(true)
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان الرئيسي */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">مراكز التكلفة</h1>
            <p className="text-gray-600 mt-1">إدارة مراكز التكلفة في النظام</p>
          </div>
          
          {/* زر إضافة مركز تكلفة جديد */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة مركز تكلفة
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة مركز تكلفة جديد</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleAdd} className="space-y-4">
                <div>
                  <Label htmlFor="center_code">كود المركز *</Label>
                  <Input
                    id="center_code"
                    value={formData.center_code}
                    onChange={(e) => setFormData({...formData, center_code: e.target.value})}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="center_name">اسم المركز *</Label>
                  <Input
                    id="center_name"
                    value={formData.center_name}
                    onChange={(e) => setFormData({...formData, center_name: e.target.value})}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="center_level">مستوى المركز</Label>
                  <Input
                    id="center_level"
                    type="number"
                    min="1"
                    value={formData.center_level}
                    onChange={(e) => setFormData({...formData, center_level: parseInt(e.target.value)})}
                  />
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                  />
                  <Label htmlFor="is_active">نشط</Label>
                </div>
                <div className="flex space-x-3 space-x-reverse">
                  <Button type="submit" className="flex-1">
                    <Save className="h-4 w-4 mr-2" />
                    حفظ
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsAddDialogOpen(false)}
                    className="flex-1"
                  >
                    <X className="h-4 w-4 mr-2" />
                    إلغاء
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في مراكز التكلفة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* جدول مراكز التكلفة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="h-5 w-5 mr-2" />
              قائمة مراكز التكلفة ({filteredCenters.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">جاري التحميل...</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>كود المركز</TableHead>
                    <TableHead>اسم المركز</TableHead>
                    <TableHead>المستوى</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الإنشاء</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCenters.map((center) => (
                    <TableRow key={center.id}>
                      <TableCell className="font-medium">{center.center_code}</TableCell>
                      <TableCell>{center.center_name}</TableCell>
                      <TableCell>{center.center_level}</TableCell>
                      <TableCell>
                        <Badge className={center.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                          {center.is_active ? 'نشط' : 'غير نشط'}
                        </Badge>
                      </TableCell>
                      <TableCell>{new Date(center.created_date).toLocaleDateString('en-GB')}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(center)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(center.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* نافذة التعديل */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل مركز التكلفة</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleEdit} className="space-y-4">
              <div>
                <Label htmlFor="edit_center_code">كود المركز *</Label>
                <Input
                  id="edit_center_code"
                  value={formData.center_code}
                  onChange={(e) => setFormData({...formData, center_code: e.target.value})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="edit_center_name">اسم المركز *</Label>
                <Input
                  id="edit_center_name"
                  value={formData.center_name}
                  onChange={(e) => setFormData({...formData, center_name: e.target.value})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="edit_center_level">مستوى المركز</Label>
                <Input
                  id="edit_center_level"
                  type="number"
                  min="1"
                  value={formData.center_level}
                  onChange={(e) => setFormData({...formData, center_level: parseInt(e.target.value)})}
                />
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="checkbox"
                  id="edit_is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                />
                <Label htmlFor="edit_is_active">نشط</Label>
              </div>
              <div className="flex space-x-3 space-x-reverse">
                <Button type="submit" className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  حفظ التغييرات
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditDialogOpen(false)}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  إلغاء
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
