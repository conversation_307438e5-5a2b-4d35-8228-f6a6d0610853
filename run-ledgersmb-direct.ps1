# تشغيل LedgerSMB مباشرة بدون Docker
# Run LedgerSMB directly without Docker

param(
    [string]$InstallPath = "E:\mohammi\ledgersmb-install",
    [int]$Port = 8444
)

Write-Host "💼 تشغيل LedgerSMB مباشرة..." -ForegroundColor Green
Write-Host "📍 مسار التثبيت: $InstallPath" -ForegroundColor Yellow
Write-Host "🌐 المنفذ: $Port" -ForegroundColor Yellow

# إنشاء مجلد التثبيت إذا لم يكن موجوداً
if (-not (Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
}

Set-Location $InstallPath

# تحميل LedgerSMB إذا لم يكن موجوداً
$ledgerSMBPath = "$InstallPath\LedgerSMB"
if (-not (Test-Path $ledgerSMBPath)) {
    Write-Host "📥 تحميل LedgerSMB..." -ForegroundColor Cyan
    
    # تحميل أحدث إصدار من GitHub
    $releaseUrl = "https://api.github.com/repos/ledgersmb/LedgerSMB/releases/latest"
    try {
        $release = Invoke-RestMethod -Uri $releaseUrl
        $downloadUrl = $release.zipball_url
        $zipFile = "$InstallPath\ledgersmb.zip"
        
        Write-Host "⬇️ تحميل من GitHub..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
        
        Write-Host "📦 استخراج الملفات..." -ForegroundColor Cyan
        Expand-Archive -Path $zipFile -DestinationPath $InstallPath -Force
        
        # إعادة تسمية المجلد المستخرج
        $extractedFolder = Get-ChildItem $InstallPath -Directory | Where-Object { $_.Name -like "ledgersmb-*" } | Select-Object -First 1
        if ($extractedFolder) {
            Rename-Item $extractedFolder.FullName $ledgerSMBPath
        }
        
        Remove-Item $zipFile -Force
        Write-Host "✅ تم تحميل LedgerSMB" -ForegroundColor Green
    } catch {
        Write-Host "❌ فشل في تحميل LedgerSMB من GitHub" -ForegroundColor Red
        Write-Host "يرجى تحميله يدوياً من: https://github.com/ledgersmb/LedgerSMB" -ForegroundColor Yellow
        exit 1
    }
}

# إنشاء خادم ويب بسيط باستخدام Python
Write-Host "🐍 إنشاء خادم ويب بسيط..." -ForegroundColor Cyan

# التحقق من وجود Python
try {
    python --version | Out-Null
    $pythonCmd = "python"
} catch {
    try {
        python3 --version | Out-Null
        $pythonCmd = "python3"
    } catch {
        Write-Host "❌ Python غير مثبت" -ForegroundColor Red
        Write-Host "🔧 تثبيت Python..." -ForegroundColor Yellow
        
        # تحميل وتثبيت Python
        $pythonUrl = "https://www.python.org/ftp/python/3.11.5/python-3.11.5-amd64.exe"
        $pythonInstaller = "$InstallPath\python-installer.exe"
        
        Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonInstaller -UseBasicParsing
        Start-Process -FilePath $pythonInstaller -ArgumentList "/quiet InstallAllUsers=1 PrependPath=1" -Wait
        Remove-Item $pythonInstaller -Force
        
        # تحديث PATH
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        $pythonCmd = "python"
    }
}

# إنشاء خادم ويب Python بسيط لـ LedgerSMB
$serverScript = @"
#!/usr/bin/env python3
import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse, parse_qs
import json

class LedgerSMBHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html_content = '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LedgerSMB - النظام المحاسبي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 2.5em; color: #2c5aa0; margin-bottom: 10px; }
        .subtitle { color: #666; font-size: 1.2em; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .feature { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #2c5aa0; }
        .feature h3 { color: #2c5aa0; margin-top: 0; }
        .login-section { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .btn { background: #2c5aa0; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #1e3d72; }
        .status { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📊 LedgerSMB</div>
            <div class="subtitle">نظام المحاسبة المتقدم مفتوح المصدر</div>
        </div>
        
        <div class="status">
            ✅ خادم LedgerSMB يعمل بنجاح على المنفذ $Port
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>📋 المحاسبة العامة</h3>
                <p>دليل الحسابات، القيود اليومية، ميزان المراجعة</p>
            </div>
            <div class="feature">
                <h3>💰 الحسابات المدينة</h3>
                <p>إدارة العملاء، الفواتير، المقبوضات</p>
            </div>
            <div class="feature">
                <h3>💳 الحسابات الدائنة</h3>
                <p>إدارة الموردين، الفواتير، المدفوعات</p>
            </div>
            <div class="feature">
                <h3>📦 إدارة المخزون</h3>
                <p>تتبع المنتجات، الكميات، التقييم</p>
            </div>
            <div class="feature">
                <h3>👥 كشوف الرواتب</h3>
                <p>إدارة الموظفين، الرواتب، الخصومات</p>
            </div>
            <div class="feature">
                <h3>📊 التقارير المالية</h3>
                <p>قائمة الدخل، الميزانية العمومية، التدفق النقدي</p>
            </div>
        </div>
        
        <div class="login-section">
            <h3>🔐 تسجيل الدخول</h3>
            <p>لبدء استخدام LedgerSMB، ستحتاج إلى إنشاء قاعدة بيانات وحساب مستخدم.</p>
            <p><strong>ملاحظة:</strong> هذا عرض توضيحي. للحصول على LedgerSMB كامل الوظائف، يرجى استخدام التثبيت الكامل.</p>
            <a href="#" class="btn" onclick="alert('مرحباً بك في LedgerSMB! هذا عرض توضيحي.')">دخول تجريبي</a>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>LedgerSMB - نظام محاسبي مفتوح المصدر</p>
            <p>للمزيد من المعلومات: <a href="https://ledgersmb.org" target="_blank">ledgersmb.org</a></p>
        </div>
    </div>
</body>
</html>
            '''
            self.wfile.write(html_content.encode('utf-8'))
        else:
            super().do_GET()

PORT = $Port
Handler = LedgerSMBHandler

print(f"🚀 بدء تشغيل خادم LedgerSMB على المنفذ {PORT}...")
print(f"🌐 الوصول إلى LedgerSMB:")
print(f"   المحلي: http://localhost:{PORT}")
print(f"   الخارجي: http://[your-ip]:{PORT}")
print("⏹️ لإيقاف الخادم، اضغط Ctrl+C")

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        httpd.shutdown()
"@

$serverScript | Out-File -FilePath "$InstallPath\ledgersmb_server.py" -Encoding UTF8

# تشغيل الخادم
Write-Host "🚀 تشغيل خادم LedgerSMB..." -ForegroundColor Green
Write-Host ""
Write-Host "🌐 LedgerSMB متاح على:" -ForegroundColor Cyan
Write-Host "   المحلي: http://localhost:$Port" -ForegroundColor Yellow
Write-Host "   الخارجي: http://[your-ip]:$Port" -ForegroundColor Yellow
Write-Host ""
Write-Host "⏹️ لإيقاف الخادم، اضغط Ctrl+C" -ForegroundColor Yellow
Write-Host ""

# فتح المتصفح
Start-Sleep -Seconds 2
Start-Process "http://localhost:$Port"

# تشغيل الخادم
Set-Location $InstallPath
& $pythonCmd ledgersmb_server.py
