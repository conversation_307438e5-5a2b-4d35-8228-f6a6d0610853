import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع مراكز التكلفة
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        cc.id,
        cc.center_code,
        cc.center_name,
        cc.parent_id,
        cc.center_level,
        cc.is_active,
        cc.description,
        cc.created_date,
        cc.updated_date,
        parent.center_name as parent_name
      FROM cost_centers cc
      LEFT JOIN cost_centers parent ON cc.parent_id = parent.id
      ORDER BY cc.center_level, cc.center_code
    `)

    return NextResponse.json({
      success: true,
      centers: result.rows
    })
  } catch (error) {
    console.error('خطأ في جلب مراكز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب مراكز التكلفة'
    }, { status: 500 })
  }
}

// POST - إضافة مركز تكلفة جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { center_code, center_name, parent_id, description } = body

    // التحقق من البيانات المطلوبة
    if (!center_code || !center_name) {
      return NextResponse.json({
        success: false,
        error: 'رمز المركز واسم المركز مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار رمز المركز
    const existingCenter = await query(
      'SELECT id FROM cost_centers WHERE center_code = $1',
      [center_code]
    )

    if (existingCenter.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رمز المركز موجود مسبقاً'
      }, { status: 400 })
    }

    // تحديد مستوى المركز
    let center_level = 1
    if (parent_id && parent_id !== '0') {
      const parentResult = await query(
        'SELECT center_level FROM cost_centers WHERE id = $1',
        [parent_id]
      )
      if (parentResult.rows.length > 0) {
        center_level = parentResult.rows[0].center_level + 1
      }
    }

    // إدراج مركز التكلفة الجديد
    const result = await query(`
      INSERT INTO cost_centers (
        center_code, center_name, parent_id, center_level, description, is_active
      )
      VALUES ($1, $2, $3, $4, $5, true)
      RETURNING *
    `, [
      center_code,
      center_name,
      parent_id && parent_id !== '0' ? parseInt(parent_id) : null,
      center_level,
      description || null
    ])

    return NextResponse.json({
      success: true,
      center: result.rows[0],
      message: 'تم إنشاء مركز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء مركز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء مركز التكلفة'
    }, { status: 500 })
  }
}

// PUT - تحديث مركز تكلفة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, center_code, center_name, parent_id, description, is_active } = body

    // التحقق من البيانات المطلوبة
    if (!id || !center_code || !center_name) {
      return NextResponse.json({
        success: false,
        error: 'معرف المركز ورمز المركز واسم المركز مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار رمز المركز (باستثناء المركز الحالي)
    const existingCenter = await query(
      'SELECT id FROM cost_centers WHERE center_code = $1 AND id != $2',
      [center_code, id]
    )

    if (existingCenter.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رمز المركز موجود مسبقاً'
      }, { status: 400 })
    }

    // تحديد مستوى المركز
    let center_level = 1
    if (parent_id && parent_id !== '0') {
      const parentResult = await query(
        'SELECT center_level FROM cost_centers WHERE id = $1',
        [parent_id]
      )
      if (parentResult.rows.length > 0) {
        center_level = parentResult.rows[0].center_level + 1
      }
    }

    // تحديث مركز التكلفة
    const result = await query(`
      UPDATE cost_centers 
      SET 
        center_code = $1,
        center_name = $2,
        parent_id = $3,
        center_level = $4,
        description = $5,
        is_active = $6,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `, [
      center_code,
      center_name,
      parent_id && parent_id !== '0' ? parseInt(parent_id) : null,
      center_level,
      description || null,
      is_active !== undefined ? is_active : true,
      id
    ])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'مركز التكلفة غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      center: result.rows[0],
      message: 'تم تحديث مركز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث مركز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث مركز التكلفة'
    }, { status: 500 })
  }
}

// DELETE - حذف مركز تكلفة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف مركز التكلفة مطلوب'
      }, { status: 400 })
    }

    // التحقق من وجود مراكز فرعية
    const subCenters = await query(
      'SELECT COUNT(*) as count FROM cost_centers WHERE parent_id = $1',
      [id]
    )

    if (parseInt(subCenters.rows[0].count) > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف مركز التكلفة لأنه يحتوي على مراكز فرعية'
      }, { status: 400 })
    }

    // التحقق من وجود معاملات مرتبطة
    const transactions = await query(
      'SELECT COUNT(*) as count FROM journal_entry_details WHERE cost_center_id = $1',
      [id]
    )

    if (parseInt(transactions.rows[0].count) > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف مركز التكلفة لأنه مرتبط بمعاملات مالية'
      }, { status: 400 })
    }

    // حذف مركز التكلفة
    const result = await query(
      'DELETE FROM cost_centers WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'مركز التكلفة غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف مركز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف مركز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف مركز التكلفة'
    }, { status: 500 })
  }
}
