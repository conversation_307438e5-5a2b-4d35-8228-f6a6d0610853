'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Save } from 'lucide-react'
import Link from 'next/link'

export default function AddIssuePage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    case_number: '',
    title: '',
    description: '',
    client_name: '',
    court_name: '',
    issue_type: '',
    amount: '',
    notes: '',
    contract_method: 'بالجلسة',
    contract_date: new Date().toISOString().split('T')[0]
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (result.success) {
        alert('تم إنشاء القضية بنجاح!')
        router.push('/issues')
      } else {
        alert(result.error || 'فشل في إنشاء القضية')
      }
    } catch (error) {
      console.error('Error creating issue:', error)
      alert('حدث خطأ أثناء إنشاء القضية')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Link href="/issues" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة إلى القضايا
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">إضافة قضية جديدة</h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* معلومات القضية الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle>معلومات القضية الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="case_number">رقم القضية *</Label>
                  <Input
                    id="case_number"
                    name="case_number"
                    value={formData.case_number}
                    onChange={handleChange}
                    placeholder="مثال: 2025-001"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="issue_type">نوع القضية</Label>
                  <select
                    id="issue_type"
                    name="issue_type"
                    value={formData.issue_type}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">اختر نوع القضية</option>
                    <option value="تجاري">تجاري</option>
                    <option value="مدني">مدني</option>
                    <option value="جنائي">جنائي</option>
                    <option value="عمالي">عمالي</option>
                    <option value="عقاري">عقاري</option>
                    <option value="أحوال شخصية">أحوال شخصية</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="title">عنوان القضية *</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="أدخل عنوان القضية"
                  required
                />
              </div>

              <div>
                <Label htmlFor="description">وصف القضية</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="أدخل وصف مختصر للقضية"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* معلومات الموكل والمحكمة */}
          <Card>
            <CardHeader>
              <CardTitle>معلومات الموكل والمحكمة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="client_name">اسم الموكل *</Label>
                  <Input
                    id="client_name"
                    name="client_name"
                    value={formData.client_name}
                    onChange={handleChange}
                    placeholder="أدخل اسم الموكل"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="court_name">المحكمة</Label>
                  <Input
                    id="court_name"
                    name="court_name"
                    value={formData.court_name}
                    onChange={handleChange}
                    placeholder="أدخل اسم المحكمة"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* المعلومات المالية */}
          <Card>
            <CardHeader>
              <CardTitle>المعلومات المالية والتعاقد</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="amount">قيمة القضية (ريال)</Label>
                  <Input
                    id="amount"
                    name="amount"
                    value={formData.amount}
                    onChange={handleChange}
                    placeholder="0"
                    type="number"
                    min="0"
                    step="1"
                  />
                </div>
                <div>
                  <Label htmlFor="contract_method">طريقة التعاقد</Label>
                  <select
                    id="contract_method"
                    name="contract_method"
                    value={formData.contract_method}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="بالجلسة">بالجلسة</option>
                    <option value="بالعقد">بالعقد</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="contract_date">تاريخ التعاقد</Label>
                  <Input
                    id="contract_date"
                    name="contract_date"
                    value={formData.contract_date}
                    onChange={handleChange}
                    type="date"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  placeholder="أدخل أي ملاحظات إضافية"
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>

          {/* أزرار الإجراءات */}
          <div className="flex justify-end space-x-4 space-x-reverse">
            <Link href="/issues">
              <Button type="button" variant="outline">
                إلغاء
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                'جاري الحفظ...'
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ القضية
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
