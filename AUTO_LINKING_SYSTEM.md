# نظام الربط التلقائي للحسابات المحاسبية

## 📋 نظرة عامة

نظام الربط التلقائي هو نظام متقدم يقوم بربط السجلات الجديدة (مثل الموكلين، الموظفين، القضايا) بحسابات محاسبية فرعية تلقائياً عند إضافتها إلى النظام.

## 🎯 الهدف من النظام

- **الأتمتة الكاملة**: ربط السجلات بالحسابات المحاسبية تلقائياً دون تدخل يدوي
- **التنظيم المحاسبي**: إنشاء حسابات فرعية منظمة لكل سجل
- **المرونة**: إمكانية تخصيص إعدادات الربط لكل جدول
- **الشفافية**: تتبع جميع الروابط والعمليات المحاسبية

## 🏗️ مكونات النظام

### 1. **جدول إعدادات الربط** (`account_linking_settings`)
```sql
CREATE TABLE account_linking_settings (
  id SERIAL PRIMARY KEY,
  table_name VARCHAR(100) UNIQUE NOT NULL,        -- اسم الجدول (clients, employees, etc.)
  table_display_name VARCHAR(255) NOT NULL,       -- الاسم المعروض (الموكلين، الموظفين)
  table_description TEXT,                         -- وصف الجدول
  is_enabled BOOLEAN DEFAULT TRUE,                -- هل الربط مفعل؟
  auto_create_on_insert BOOLEAN DEFAULT TRUE,     -- إنشاء تلقائي عند الإدراج؟
  account_prefix VARCHAR(10),                     -- بادئة كود الحساب (CLI, EMP)
  name_field VARCHAR(100) DEFAULT 'name',         -- حقل الاسم في الجدول
  id_field VARCHAR(100) DEFAULT 'id',             -- حقل المعرف في الجدول
  default_main_account_id INTEGER,                -- الحساب الرئيسي للربط
  created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. **جدول روابط الحسابات الفرعية** (`account_sub_links`)
```sql
CREATE TABLE account_sub_links (
  id SERIAL PRIMARY KEY,
  main_account_id INTEGER REFERENCES chart_of_accounts(id),
  linked_table VARCHAR(100) NOT NULL,             -- الجدول المربوط
  linked_record_id INTEGER NOT NULL,              -- معرف السجل المربوط
  sub_account_code VARCHAR(50) UNIQUE NOT NULL,   -- كود الحساب الفرعي
  sub_account_name VARCHAR(255) NOT NULL,         -- اسم الحساب الفرعي
  opening_balance DECIMAL(15,2) DEFAULT 0,        -- الرصيد الافتتاحي
  current_balance DECIMAL(15,2) DEFAULT 0,        -- الرصيد الحالي
  is_active BOOLEAN DEFAULT TRUE,                 -- هل الرابط نشط؟
  created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(100),                        -- من أنشأ الرابط
  notes TEXT,                                     -- ملاحظات
  UNIQUE(main_account_id, linked_table, linked_record_id)
);
```

### 3. **دوال قاعدة البيانات**
- `auto_create_account_link()`: دالة تنفذ تلقائياً عند إدراج سجل جديد
- محفزات (Triggers) لكل جدول لتطبيق الربط التلقائي

## 🔧 إعداد النظام

### 1. تشغيل سكريبت الإعداد
```bash
cd database
node setup_auto_linking.js
```

### 2. تحديث كلمة مرور قاعدة البيانات
قم بتحديث كلمة المرور في ملف `setup_auto_linking.js`:
```javascript
const client = new Client({
  user: 'postgres',
  host: 'localhost',
  database: 'law_firm_db',
  password: 'your_actual_password_here', // ضع كلمة المرور الصحيحة هنا
  port: 5432,
});
```

## 📊 الإعدادات الافتراضية

| الجدول | الاسم المعروض | الحساب الرئيسي | البادئة | حقل الاسم |
|--------|---------------|----------------|---------|-----------|
| clients | الموكلين | 1121 | CLI | name |
| employees | الموظفين | 1122 | EMP | name |
| issues | القضايا | 4110 | CASE | title |
| courts | المحاكم | 5110 | CRT | name |
| governorates | المحافظات | 5120 | GOV | name |
| branches | الفروع | 5130 | BR | name |

## 🎮 كيفية الاستخدام

### 1. **إدارة الإعدادات**
- انتقل إلى: `القوائم المالية > إعدادات الربط التلقائي`
- يمكنك إضافة/تعديل/حذف إعدادات الربط
- تفعيل/إلغاء تفعيل الربط التلقائي لكل جدول

### 2. **عرض الروابط**
- انتقل إلى: `القوائم المالية > روابط الحسابات الفرعية`
- عرض جميع الروابط النشطة
- تعديل أسماء الحسابات والأرصدة
- البحث والتصفية حسب الجدول

### 3. **الربط التلقائي**
عند إضافة سجل جديد (مثل موكل جديد):
```javascript
// سيتم تطبيق هذا تلقائياً في كود إضافة الموكل
const autoLinkResult = await autoLinkAccount('clients', newClientId, clientName);
```

## 🔄 سير العمل

### عند إضافة سجل جديد:

1. **إدراج السجل** في الجدول الأساسي (clients, employees, etc.)
2. **تفعيل المحفز** (Trigger) تلقائياً
3. **البحث عن الإعدادات** للجدول المحدد
4. **إنشاء كود الحساب الفرعي**: `{account_code}-{record_id:0000}`
5. **إنشاء اسم الحساب الفرعي**: `{table_display_name}: {record_name}`
6. **إدراج الرابط** في جدول `account_sub_links`
7. **إرجاع النتيجة** للمستخدم

### مثال عملي:
```
موكل جديد: أحمد محمد (ID: 15)
↓
كود الحساب الفرعي: 1121-0015
اسم الحساب الفرعي: الموكل: أحمد محمد
الحساب الرئيسي: 1121 - حسابات الموكلين
```

## 🛠️ API Endpoints

### إعدادات الربط
- `GET /api/account-linking-settings` - جلب جميع الإعدادات
- `POST /api/account-linking-settings` - إضافة/تحديث إعدادات
- `DELETE /api/account-linking-settings` - حذف إعدادات

### الربط التلقائي
- `POST /api/auto-link-account` - ربط سجل تلقائياً
- `GET /api/auto-link-account?table_name=clients` - التحقق من الإعدادات
- `PUT /api/auto-link-account` - تحديث رابط
- `DELETE /api/auto-link-account` - حذف رابط

### روابط الحسابات الفرعية
- `GET /api/account-sub-links` - جلب جميع الروابط
- `POST /api/account-sub-links` - إنشاء رابط جديد
- `PUT /api/account-sub-links` - تحديث رابط
- `DELETE /api/account-sub-links` - حذف رابط

## 📚 مكتبة المساعدة

### استيراد المكتبة
```javascript
import { 
  autoLinkAccount, 
  checkAutoLinkEnabled, 
  handleAutoLinkResult 
} from '@/lib/auto-account-linking'
```

### الدوال المتاحة
- `autoLinkAccount(tableName, recordId, recordName?)` - ربط سجل تلقائياً
- `checkAutoLinkEnabled(tableName)` - التحقق من تفعيل الربط
- `autoLinkMultipleRecords(tableName, records[])` - ربط عدة سجلات
- `getLinkedAccountInfo(tableName, recordId)` - معلومات الحساب المربوط
- `deleteAccountLink(linkId)` - حذف رابط
- `updateAccountLink(linkId, updates)` - تحديث رابط

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

1. **لا يتم الربط التلقائي**
   - تأكد من تفعيل الإعدادات في صفحة إعدادات الربط
   - تحقق من وجود الحساب الرئيسي
   - تأكد من تشغيل المحفزات في قاعدة البيانات

2. **خطأ في كود الحساب الفرعي**
   - تحقق من عدم تكرار الكود
   - تأكد من صحة تنسيق الكود

3. **عدم ظهور الروابط**
   - تحقق من حالة `is_active = true`
   - تأكد من صحة معرف الحساب الرئيسي

### فحص حالة النظام:
```sql
-- فحص الإعدادات المفعلة
SELECT * FROM account_linking_settings WHERE is_enabled = true;

-- فحص الروابط النشطة
SELECT COUNT(*) FROM account_sub_links WHERE is_active = true;

-- فحص المحفزات
SELECT * FROM information_schema.triggers 
WHERE trigger_name LIKE '%auto_create_account_link%';
```

## 🚀 التطوير المستقبلي

### ميزات مخططة:
- [ ] ربط تلقائي للقيود المحاسبية
- [ ] تقارير تفصيلية للروابط
- [ ] إشعارات عند إنشاء روابط جديدة
- [ ] نسخ احتياطي للروابط
- [ ] تصدير/استيراد إعدادات الربط

### تحسينات الأداء:
- [ ] فهرسة محسنة للجداول
- [ ] تحسين استعلامات قاعدة البيانات
- [ ] ذاكرة تخزين مؤقت للإعدادات

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من هذا الدليل أولاً
2. راجع ملفات السجل (logs) للأخطاء
3. تأكد من تحديث النظام لآخر إصدار

---

**ملاحظة**: هذا النظام جزء من نظام إدارة مكتب المحاماة المتكامل ويتطلب إعداد قاعدة البيانات والحسابات المحاسبية بشكل صحيح.
