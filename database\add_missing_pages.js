// إضافة الصفحات المفقودة إلى جدول البحث
const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123'
});

async function addMissingPages() {
  try {
    await client.connect();
    console.log('🔗 تم الاتصال بقاعدة البيانات');

    // الصفحات المفقودة
    const missingPages = [
      {
        title: 'توزيع القضايا',
        url: '/case-distribution',
        description: 'توزيع القضايا على الموظفين والمحامين',
        category: 'قضايا',
        keywords: 'توزيع,قضايا,case,distribution,محامين,موظفين'
      },
      {
        title: 'توزيع الخدمات',
        url: '/service-distribution',
        description: 'توزيع الخدمات والمهام على الفريق',
        category: 'قضايا',
        keywords: 'توزيع,خدمات,service,distribution,مهام'
      },
      {
        title: 'إدارة النسب المالية',
        url: '/lineages',
        description: 'إدارة النسب المالية المتقدمة للقضايا',
        category: 'مالية',
        keywords: 'نسب,مالية,lineages,متقدمة,إدارة'
      },
      {
        title: 'القيود اليومية الجديدة',
        url: '/journal-entries-new',
        description: 'واجهة محدثة لإدارة القيود اليومية',
        category: 'محاسبة',
        keywords: 'قيود,يومية,جديدة,journal,entries,محدثة'
      },
      {
        title: 'سندات الدفع',
        url: '/payment-vouchers',
        description: 'إدارة سندات الدفع والمصروفات',
        category: 'محاسبة',
        keywords: 'سندات,دفع,payment,vouchers,مصروفات'
      },
      {
        title: 'إدارة الشركات',
        url: '/companies',
        description: 'إدارة بيانات الشركات والمؤسسات',
        category: 'إدارة',
        keywords: 'شركات,companies,مؤسسات,إدارة'
      },
      {
        title: 'الترحيل',
        url: '/transfers',
        description: 'ترحيل البيانات والحسابات',
        category: 'محاسبة',
        keywords: 'ترحيل,transfers,بيانات,حسابات'
      },
      {
        title: 'إعدادات العملات',
        url: '/settings/currencies',
        description: 'إدارة العملات وأسعار الصرف',
        category: 'إعدادات',
        keywords: 'عملات,currencies,صرف,أسعار'
      },
      {
        title: 'إعدادات طرق الدفع',
        url: '/settings/payment-methods',
        description: 'إدارة طرق الدفع والسداد المختلفة',
        category: 'إعدادات',
        keywords: 'طرق,دفع,payment,methods,سداد'
      },
      {
        title: 'مراكز التكلفة',
        url: '/cost-centers',
        description: 'إدارة مراكز التكلفة المحاسبية',
        category: 'محاسبة',
        keywords: 'مراكز,تكلفة,cost,centers,محاسبية'
      }
    ];

    // إضافة الصفحات المفقودة
    for (const page of missingPages) {
      // التحقق من عدم وجود الصفحة
      const existingPage = await client.query(
        'SELECT id FROM navigation_pages WHERE page_url = $1',
        [page.url]
      );

      if (existingPage.rows.length === 0) {
        await client.query(`
          INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords)
          VALUES ($1, $2, $3, $4, $5)
        `, [page.title, page.url, page.description, page.category, page.keywords]);
        
        console.log(`✅ تم إضافة: ${page.title} (${page.url})`);
      } else {
        console.log(`ℹ️ موجود مسبقاً: ${page.title} (${page.url})`);
      }
    }

    // عرض إحصائيات
    const totalResult = await client.query('SELECT COUNT(*) as total FROM navigation_pages');
    const categoryResult = await client.query(`
      SELECT category, COUNT(*) as count 
      FROM navigation_pages 
      GROUP BY category 
      ORDER BY count DESC
    `);

    console.log(`\n📊 إحصائيات الصفحات:`);
    console.log(`📋 إجمالي الصفحات: ${totalResult.rows[0].total}`);
    console.log(`\n📂 توزيع الفئات:`);
    categoryResult.rows.forEach(row => {
      console.log(`   ${row.category}: ${row.count} صفحة`);
    });

    // عرض الصفحات الجديدة المضافة
    const newPagesResult = await client.query(`
      SELECT page_title, page_url, category 
      FROM navigation_pages 
      WHERE page_url IN (${missingPages.map((_, i) => `$${i + 1}`).join(',')})
      ORDER BY category, page_title
    `, missingPages.map(p => p.url));

    if (newPagesResult.rows.length > 0) {
      console.log(`\n🆕 الصفحات المضافة حديثاً:`);
      newPagesResult.rows.forEach(row => {
        console.log(`   - ${row.page_title} (${row.category}): ${row.page_url}`);
      });
    }

  } catch (error) {
    console.error('❌ خطأ في إضافة الصفحات:', error);
  } finally {
    await client.end();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإضافة
addMissingPages();
