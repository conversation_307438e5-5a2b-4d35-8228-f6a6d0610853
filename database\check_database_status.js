// التحقق من حالة قاعدة البيانات والجداول
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkDatabaseStatus() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔍 التحقق من حالة قاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من الجداول الموجودة
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    console.log('📋 الجداول الموجودة:');
    tablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    // التحقق من جدول دليل الحسابات
    console.log('\n🔍 التحقق من جدول دليل الحسابات...');
    
    try {
      const chartResult = await client.query('SELECT COUNT(*) as count FROM chart_of_accounts');
      console.log(`   ✅ جدول chart_of_accounts موجود - عدد السجلات: ${chartResult.rows[0].count}`);
      
      // عرض بعض البيانات
      const sampleData = await client.query('SELECT account_code, account_name, account_type FROM chart_of_accounts LIMIT 5');
      console.log('   📊 عينة من البيانات:');
      sampleData.rows.forEach(row => {
        console.log(`      ${row.account_code} - ${row.account_name} (${row.account_type})`);
      });
    } catch (error) {
      console.log(`   ❌ مشكلة في جدول chart_of_accounts: ${error.message}`);
    }

    // التحقق من الجداول الأساسية الأخرى
    const tables = ['clients', 'employees', 'users', 'issues', 'courts', 'branches'];
    
    console.log('\n🔍 التحقق من الجداول الأساسية:');
    for (const table of tables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`   ✅ ${table}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${table}: ${error.message}`);
      }
    }

    // التحقق من ملف الاتصال
    console.log('\n🔍 التحقق من إعدادات الاتصال...');
    const fs = require('fs');
    const path = require('path');
    
    try {
      const configPath = path.join(__dirname, '..', 'mohammi.txt');
      if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        console.log('   ✅ ملف mohammi.txt موجود');
        console.log('   📄 محتوى الملف:');
        console.log(configContent);
      } else {
        console.log('   ❌ ملف mohammi.txt غير موجود');
      }
    } catch (error) {
      console.log(`   ❌ خطأ في قراءة ملف الإعدادات: ${error.message}`);
    }

    // التحقق من ملف database.ts
    console.log('\n🔍 التحقق من ملف الاتصال بقاعدة البيانات...');
    try {
      const dbFilePath = path.join(__dirname, '..', 'src', 'lib', 'database.ts');
      if (fs.existsSync(dbFilePath)) {
        console.log('   ✅ ملف database.ts موجود');
        const dbFileContent = fs.readFileSync(dbFilePath, 'utf8');
        if (dbFileContent.includes('mohammi')) {
          console.log('   ✅ الملف يحتوي على إعدادات قاعدة البيانات الصحيحة');
        } else {
          console.log('   ⚠️  الملف قد لا يحتوي على إعدادات صحيحة');
        }
      } else {
        console.log('   ❌ ملف database.ts غير موجود');
      }
    } catch (error) {
      console.log(`   ❌ خطأ في قراءة ملف database.ts: ${error.message}`);
    }

    // اختبار API
    console.log('\n🔍 اختبار الاتصال من خلال API...');
    try {
      // محاكاة استدعاء API
      const testQuery = await client.query('SELECT 1 as test');
      console.log('   ✅ الاستعلام يعمل بشكل صحيح');
    } catch (error) {
      console.log(`   ❌ خطأ في الاستعلام: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    console.log('\n🔧 الحلول المقترحة:');
    console.log('   1. تأكد من تشغيل PostgreSQL');
    console.log('   2. تحقق من صحة بيانات الاتصال');
    console.log('   3. تأكد من وجود قاعدة البيانات "mohammi"');
    console.log('   4. تحقق من صلاحيات المستخدم "postgres"');
  } finally {
    await client.end();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

checkDatabaseStatus();
