import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// تسجيل دخول مبسط للمستخدمين
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 بدء عملية تسجيل الدخول المبسطة...')
    const body = await request.json()
    console.log('📋 البيانات المستلمة:', { username: body.username, hasPassword: !!body.password })
    const { username, password } = body

    // التحقق من البيانات المطلوبة
    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // البحث عن المستخدم
    console.log('🔍 البحث عن المستخدم:', username)
    const userResult = await query(`
      SELECT u.*, e.name as employee_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      WHERE u.username = $1
    `, [username])

    console.log('📊 نتائج البحث:', userResult.rows.length, 'مستخدم')

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم غير موجود' },
        { status: 401 }
      )
    }

    const user = userResult.rows[0]
    console.log('👤 بيانات المستخدم:', { id: user.id, username: user.username, status: user.status })

    // تحقق مبسط من كلمة المرور (للاختبار فقط)
    if (password !== username) { // كلمة المرور = اسم المستخدم للاختبار
      return NextResponse.json(
        { success: false, error: 'كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    // التحقق من حالة المستخدم
    if (user.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'حساب المستخدم غير نشط' },
        { status: 403 }
      )
    }

    // تحديث بيانات تسجيل الدخول
    await query(`
      UPDATE users
      SET last_login = CURRENT_TIMESTAMP,
          is_online = true,
          login_attempts = 0
      WHERE id = $1
    `, [user.id])

    // إرجاع بيانات المستخدم (بدون كلمة المرور)
    const { password_hash, ...userWithoutPassword } = user

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        ...userWithoutPassword,
        name: user.employee_name || user.username
      },
      token: 'simple-token-' + user.id
    })

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تسجيل الدخول: ' + error.message },
      { status: 500 }
    )
  }
}

// GET - التحقق من حالة تسجيل الدخول
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, JWT_SECRET) as any

    // جلب بيانات المستخدم المحدثة
    const userResult = await query(`
      SELECT u.*, e.name as employee_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      WHERE u.id = $1 AND u.status = 'active'
    `, [decoded.userId])

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود أو غير نشط' },
        { status: 401 }
      )
    }

    const user = userResult.rows[0]
    const { password_hash, ...userWithoutPassword } = user

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        name: user.employee_name || user.username
      }
    })

  } catch (error) {
    console.error('Error verifying token:', error)
    return NextResponse.json(
      { success: false, error: 'رمز المصادقة غير صالح' },
      { status: 401 }
    )
  }
}

// DELETE - تسجيل الخروج
export async function DELETE(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, JWT_SECRET) as any

    // تحديث حالة المستخدم إلى غير متصل
    await query(`
      UPDATE users
      SET is_online = false,
          last_logout = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [decoded.userId])

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    })

  } catch (error) {
    console.error('Error during logout:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تسجيل الخروج' },
      { status: 500 }
    )
  }
}
