from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time

# إعداد متصفح كروم
options = webdriver.ChromeOptions()
options.add_argument('--start-maximized')
driver = webdriver.Chrome(options=options)

# فتح صفحة النظام التجريبي
url = "https://remox.net/app/default.aspx"
driver.get(url)

time.sleep(3)  # انتظار تحميل الصفحة

# مثال: الضغط على زر تجربة النظام إذا كان موجوداً
try:
    trial_button = driver.find_element(By.XPATH, "//a[contains(text(), 'تجريب النظام')]")
    trial_button.click()
    time.sleep(3)
except Exception as e:
    print("لم يتم العثور على زر تجربة النظام أو حدث خطأ:", e)

# بعد الضغط على زر تجربة النظام، اختيار أول خيار وهو "نظام عام"
try:
    time.sleep(2)
    system_options = driver.find_elements(By.TAG_NAME, "a")
    general_system = None
    for option in system_options:
        if "نظام عام" in option.text:
            general_system = option
            break
    if general_system:
        general_system.click()
        print("تم اختيار نظام عام بنجاح.")
        time.sleep(3)
    else:
        print("لم يتم العثور على خيار نظام عام.")
except Exception as e:
    print("حدث خطأ أثناء اختيار نظام عام:", e)

# بعد اختيار نظام عام، الضغط على زر "موافق" في صفحة الترحيب
try:
    time.sleep(2)
    agree_button = None
    buttons = driver.find_elements(By.TAG_NAME, "button")
    for btn in buttons:
        if "موافق" in btn.text:
            agree_button = btn
            break
    if not agree_button:
        # أحياناً يكون الزر عبارة عن عنصر آخر مثل input أو div
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.get_attribute("value") and "موافق" in inp.get_attribute("value"):
                agree_button = inp
                break
    if agree_button:
        agree_button.click()
        print("تم الضغط على زر موافق.")
        time.sleep(3)
    else:
        print("لم يتم العثور على زر موافق.")
except Exception as e:
    print("حدث خطأ أثناء الضغط على زر موافق:", e)

# مثال: التنقل بين القوائم (تعديل حسب القوائم الفعلية)
try:
    # ابحث عن قائمة أو زر معين
    menu_items = driver.find_elements(By.TAG_NAME, "a")
    for item in menu_items:
        print("القائمة:", item.text)
        # يمكنك الضغط على القائمة إذا رغبت
        # item.click()
        # time.sleep(2)
except Exception as e:
    print("خطأ في استعراض القوائم:", e)

# مثال: استخراج معلومات من قائمة الحسابات
try:
    # ابحث عن زر أو رابط قائمة الحسابات
    accounts_menu = None
    menu_items = driver.find_elements(By.TAG_NAME, "a")
    for item in menu_items:
        if "حسابات" in item.text or "الحسابات" in item.text:
            accounts_menu = item
            break
    if accounts_menu:
        accounts_menu.click()
        time.sleep(3)
        # استخراج عناصر الحسابات من الصفحة الجديدة
        account_elements = driver.find_elements(By.TAG_NAME, "td")
        accounts = [el.text for el in account_elements if el.text.strip()]
        # حفظ النتائج في ملف نصي
        with open("accounts_list.txt", "w", encoding="utf-8") as f:
            for acc in accounts:
                f.write(acc + "\n")
        print("تم استخراج وحفظ قائمة الحسابات في accounts_list.txt")
    else:
        print("لم يتم العثور على قائمة الحسابات.")
except Exception as e:
    print("حدث خطأ أثناء استخراج قائمة الحسابات:", e)

# مثال: استخراج بيانات جميع القوائم الفرعية بقائمة الحسابات بناءً على الصورة المرفقة
try:
    accounts_menu = None
    menu_items = driver.find_elements(By.TAG_NAME, "a")
    for item in menu_items:
        if "دليل الحسابات" in item.text:
            accounts_menu = item
            break
    if accounts_menu:
        accounts_menu.click()
        time.sleep(3)
        # القوائم الفرعية حسب الصورة
        submenus = {
            "سندات القبض": "receipts_list.txt",
            "سندات الصرف": "payments_list.txt",
            "قيود اليومية": "entries_list.txt",
            "الإشعارات": "notifications_list.txt",
            "عمليات مالية مبسطة": "simple_finance_list.txt",
            "خطابات الضمان": "guarantees_list.txt",
            "الإعتمادات المستندية": "credits_list.txt",
            "الموازنة التقديرية": "budget_list.txt",
            "المراجعة و الترحيل": "review_posting_list.txt",
            "المطابقة والتسوية": "reconciliation_list.txt",
            "خطوات إغلاق الفترة المحاسبية": "closing_steps_list.txt",
            "التقارير المالية": "financial_reports_list.txt"
        }
        for submenu, filename in submenus.items():
            try:
                submenu_link = None
                submenu_items = driver.find_elements(By.TAG_NAME, "a")
                for subitem in submenu_items:
                    if submenu in subitem.text:
                        submenu_link = subitem
                        break
                if submenu_link:
                    submenu_link.click()
                    time.sleep(3)
                    elements = driver.find_elements(By.TAG_NAME, "td")
                    data = [el.text for el in elements if el.text.strip()]
                    with open(filename, "w", encoding="utf-8") as f:
                        for d in data:
                            f.write(d + "\n")
                    print(f"تم استخراج وحفظ بيانات {submenu} في {filename}")
                    driver.back()
                    time.sleep(2)
                else:
                    print(f"لم يتم العثور على قائمة {submenu}.")
            except Exception as e:
                print(f"حدث خطأ أثناء استخراج قائمة {submenu}: {e}")
    else:
        print("لم يتم العثور على دليل الحسابات.")
except Exception as e:
    print("حدث خطأ أثناء استخراج القوائم الفرعية لدليل الحسابات:", e)

# زيارة الروابط المباشرة واستخراج البيانات
links = {
    "دليل الحسابات": "https://remox.net/app/fms/?fm=fi-accs&cmd=show-chart",
    "سندات القبض": "https://remox.net/app/fms/?fm=es-cash-rcpt&cmd=add",
    "سندات الصرف": "https://remox.net/app/fms/?fm=es-pay-vouch&cmd=add",
    "قيد بسيط": "https://remox.net/app/fms/?fm=es-pay-vouch&cmd=add",
    "قيد يومي": "https://remox.net/app/fms/?fm=fi-gl-entry&cmd=add"
}
for name, url in links.items():
    try:
        driver.get(url)
        print(f"تم فتح صفحة {name}")
        time.sleep(3)
        elements = driver.find_elements(By.TAG_NAME, "td")
        data = [el.text for el in elements if el.text.strip()]
        filename = f"{name.replace(' ', '_')}_data.txt"
        with open(filename, "w", encoding="utf-8") as f:
            for d in data:
                f.write(d + "\n")
        print(f"تم استخراج وحفظ بيانات {name} في {filename}")
    except Exception as e:
        print(f"حدث خطأ أثناء استخراج بيانات {name}: {e}")

# إغلاق المتصفح بعد الانتهاء
input("اضغط Enter لإغلاق المتصفح...")
driver.quit()
