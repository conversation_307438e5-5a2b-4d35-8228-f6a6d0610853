'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { autoLinkAccount } from '@/lib/auto-account-linking'
import {
  Users,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
  X,
  Save,
  User,
  Briefcase
} from 'lucide-react'

interface Employee {
  id: number
  employee_number: string
  name: string
  position: string
  department_id: number
  department_name: string
  branch_id: number
  branch_name: string
  governorate_id: number
  governorate_name: string
  court_id?: number
  court_name?: string
  phone: string
  email: string
  salary: number
  hire_date: string
  status: string
  is_active: boolean
  created_date: string
}

export default function EmployeesPage() {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    position: 'محامي رسمي',
    department_id: '',
    branch_id: '1',
    governorate_id: '1',
    court_id: '',
    phone: '',
    email: '',
    salary: '',
    hire_date: '',
    status: 'نشط'
  })

  // قوائم الوظائف والمحاكم والفروع والأقسام
  const [courts, setCourts] = useState<any[]>([])
  const [branches, setBranches] = useState<any[]>([])
  const [governorates, setGovernorates] = useState<any[]>([])
  const [departments, setDepartments] = useState<any[]>([])

  const positions = [
    'مدير عام',
    'نائب مدير عام',
    'استشاري',
    'محامي رسمي',
    'متدرب',
    'موظف رسمي',
    'محاسب',
    'امين صندوق',
    'سكرتارية',
    'خدمات',
    'اخرى'
  ]

  const fetchEmployees = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/employees')
      const result = await response.json()

      if (result.success) {
        setEmployees(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات الموظفين')
        setEmployees([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setEmployees([])
    } finally {
      setIsLoading(false)
    }
  }

  // جلب البيانات المرجعية
  const fetchReferenceData = async () => {
    try {
      // جلب المحاكم
      const courtsResponse = await fetch('/api/courts')
      const courtsResult = await courtsResponse.json()
      if (courtsResult.success) {
        setCourts(courtsResult.data)
      }

      // جلب الفروع
      const branchesResponse = await fetch('/api/branches')
      const branchesResult = await branchesResponse.json()
      if (branchesResult.success) {
        setBranches(branchesResult.data)
      }

      // جلب المحافظات
      const governoratesResponse = await fetch('/api/governorates')
      const governoratesResult = await governoratesResponse.json()
      if (governoratesResult.success) {
        setGovernorates(governoratesResult.data)
      }

      // جلب الأقسام (المحاكم)
      const departmentsResponse = await fetch('/api/courts')
      const departmentsResult = await departmentsResponse.json()
      if (departmentsResult.success) {
        setDepartments(departmentsResult.data)
      }
    } catch (error) {
      console.error('Error fetching reference data:', error)
    }
  }

  useEffect(() => {
    fetchEmployees()
    fetchReferenceData()
  }, [])

  const filteredEmployees = employees.filter(employee =>
    (employee.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.position || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.department_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.employee_number || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }

  const getStatusText = (status: string) => {
    return status === 'active' ? 'نشط' : 'غير نشط'
  }

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
      try {
        const response = await fetch(`/api/employees?id=${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          alert('تم حذف الموظف بنجاح')
          fetchEmployees()
        } else {
          alert(result.error || 'فشل في حذف الموظف')
        }
      } catch (error) {
        console.error('Error deleting employee:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  const handleEdit = (employee: Employee) => {
    setEditingEmployee(employee)
    setFormData({
      name: employee.name,
      position: employee.position,
      department_id: employee.department_id?.toString() || '',
      branch_id: employee.branch_id?.toString() || '',
      governorate_id: employee.governorate_id?.toString() || '',
      court_id: employee.court_id?.toString() || '',
      phone: employee.phone,
      email: employee.email,
      salary: employee.salary ? employee.salary.toString() : '',
      hire_date: employee.hire_date,
      status: employee.status
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleView = (employee: Employee) => {
    setEditingEmployee(employee)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingEmployee(null)
    setFormData({
      name: '',
      position: 'محامي رسمي',
      department_id: '',
      branch_id: '1',
      governorate_id: '1',
      court_id: '',
      phone: '',
      email: '',
      salary: '',
      hire_date: '',
      status: 'نشط'
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/employees', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة الموظف بنجاح')

          // تطبيق الربط التلقائي للحساب المحاسبي
          if (result.data?.id) {
            try {
              const autoLinkResult = await autoLinkAccount('employees', result.data.id, formData.name)
              if (autoLinkResult.success) {
                console.log(`تم ربط الموظف بالحساب المحاسبي: ${autoLinkResult.sub_account_code}`)
              }
            } catch (error) {
              console.log('لم يتم الربط التلقائي للحساب المحاسبي')
            }
          }

          fetchEmployees()
        } else {
          alert(result.error || 'فشل في إضافة الموظف')
          return
        }
      } else if (modalType === 'edit' && editingEmployee) {
        const response = await fetch('/api/employees', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, id: editingEmployee.id })
        })

        const result = await response.json()

        if (result.success) {
          alert('تم تحديث بيانات الموظف بنجاح')
          fetchEmployees()
        } else {
          alert(result.error || 'فشل في تحديث بيانات الموظف')
          return
        }
      }

      setIsModalOpen(false)
      setEditingEmployee(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const stats = {
    total: employees.length,
    active: employees.filter(e => e.is_active === true).length,
    inactive: employees.filter(e => e.is_active === false).length,
    totalSalary: employees.filter(e => e.is_active === true).reduce((sum, e) => sum + (e.salary || 0), 0)
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Users className="h-8 w-8 mr-3 text-blue-600" />
              إدارة الموظفين
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع الموظفين</p>
          </div>

          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة موظف جديد
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي الموظفين</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <User className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.active}</div>
                  <div className="text-sm text-gray-600">موظفين نشطين</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <User className="h-6 w-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.inactive}</div>
                  <div className="text-sm text-gray-600">موظفين غير نشطين</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Briefcase className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.totalSalary.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">إجمالي الرواتب</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في الموظفين..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              قائمة الموظفين ({filteredEmployees.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto" style={{ maxWidth: '100vw' }}>
              <table className="w-full border-collapse min-w-max">
                <thead>
                  <tr className="border-b bg-gray-50">
                    <th className="text-right p-4 font-semibold text-lg min-w-[100px]">رقم الموظف</th>
                    <th className="text-right p-4 font-semibold text-lg min-w-[200px]">اسم الموظف</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[150px]">الوظيفة</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[150px]">القسم</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[120px]">الهاتف</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[100px]">الحالة</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[150px]">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEmployees.map((employee) => (
                    <tr key={employee.id} className="border-b hover:bg-gray-50">
                      <td className="p-4 font-medium">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          {employee.employee_number || `EMP${employee.id.toString().padStart(3, '0')}`}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center">
                          <User className="h-5 w-5 mr-2 text-gray-400" />
                          <span className="font-medium text-lg">{employee.name}</span>
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <Badge className="bg-green-100 text-green-800 text-sm px-3 py-1">
                          {employee.position}
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <Badge className="bg-purple-100 text-purple-800 text-sm px-3 py-1">
                          {employee.department_name || 'غير محدد'}
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-400" />
                          <span className="text-lg">{employee.phone}</span>
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <Badge className={employee.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                          {employee.is_active ? 'نشط' : 'غير نشط'}
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex justify-center space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleView(employee)}
                            className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(employee)}
                            className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(employee.id)}
                            className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {modalType === 'add' && 'إضافة موظف جديد'}
                  {modalType === 'edit' && 'تعديل بيانات الموظف'}
                  {modalType === 'view' && 'عرض بيانات الموظف'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {modalType === 'view' && editingEmployee ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>الاسم</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.name}</p>
                    </div>
                    <div>
                      <Label>المنصب</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.position}</p>
                    </div>
                    <div>
                      <Label>القسم</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.department_name || 'غير محدد'}</p>
                    </div>
                    <div>
                      <Label>المحكمة</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.court_name || 'غير محدد'}</p>
                    </div>
                    <div>
                      <Label>الهاتف</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.phone}</p>
                    </div>
                    <div>
                      <Label>البريد الإلكتروني</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.email}</p>
                    </div>
                    <div>
                      <Label>الراتب</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.salary ? editingEmployee.salary.toLocaleString() : '0'} ريال</p>
                    </div>
                    <div>
                      <Label>تاريخ التوظيف</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.hire_date}</p>
                    </div>
                  </div>
                  <div>
                    <Label>العنوان</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingEmployee.address}</p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">الاسم *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="position">المنصب *</Label>
                      <select
                        id="position"
                        value={formData.position}
                        onChange={(e) => setFormData({...formData, position: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        required
                      >
                        {positions.map(position => (
                          <option key={position} value={position}>{position}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="department_id">القسم *</Label>
                      <select
                        id="department_id"
                        value={formData.department_id}
                        onChange={(e) => setFormData({...formData, department_id: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        required
                      >
                        <option value="">اختر القسم</option>
                        {departments.map(dept => (
                          <option key={dept.id} value={dept.id}>{dept.name}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="id_number">رقم الهوية *</Label>
                      <Input
                        id="id_number"
                        value={formData.id_number}
                        onChange={(e) => setFormData({...formData, id_number: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">البريد الإلكتروني</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="salary">الراتب</Label>
                      <Input
                        id="salary"
                        type="number"
                        value={formData.salary}
                        onChange={(e) => setFormData({...formData, salary: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="hire_date">تاريخ التوظيف</Label>
                      <Input
                        id="hire_date"
                        type="date"
                        value={formData.hire_date}
                        onChange={(e) => setFormData({...formData, hire_date: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="court_id">المحكمة (اختياري)</Label>
                      <select
                        id="court_id"
                        value={formData.court_id}
                        onChange={(e) => setFormData({...formData, court_id: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      >
                        <option value="">لا توجد محكمة محددة</option>
                        {courts.map(court => (
                          <option key={court.id} value={court.id}>{court.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">العنوان</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({...formData, address: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="status">الحالة</Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => setFormData({...formData, status: e.target.value as 'active' | 'inactive'})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="active">نشط</option>
                      <option value="inactive">غير نشط</option>
                    </select>
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button type="submit" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsModalOpen(false)}
                      className="flex-1"
                    >
                      إلغاء
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
