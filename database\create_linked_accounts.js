const { Client } = require('pg')

async function createLinkedAccounts() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  })

  try {
    await client.connect()
    console.log('🔄 إنشاء حسابات العملاء والموظفين...')

    // جلب العملاء النشطين
    const clients = await client.query(`
      SELECT id, name FROM clients 
      WHERE status = 'active' 
      ORDER BY id 
      LIMIT 5
    `)

    // جلب الموظفين النشطين
    const employees = await client.query(`
      SELECT id, name FROM employees 
      WHERE status = 'نشط' 
      ORDER BY id 
      LIMIT 5
    `)

    // البحث عن الحساب الأب للعملاء
    const clientParentAccount = await client.query(`
      SELECT id FROM chart_of_accounts 
      WHERE account_code = '010102'
    `)

    // البحث عن الحساب الأب للموظفين
    const employeeParentAccount = await client.query(`
      SELECT id FROM chart_of_accounts 
      WHERE account_code = '010106'
    `)

    const clientParentId = clientParentAccount.rows.length > 0 ? clientParentAccount.rows[0].id : null
    const employeeParentId = employeeParentAccount.rows.length > 0 ? employeeParentAccount.rows[0].id : null

    // إنشاء حسابات العملاء
    console.log('\n👥 إنشاء حسابات العملاء...')
    for (const clientRow of clients.rows) {
      const accountCode = `C${clientRow.id.toString().padStart(6, '0')}`
      
      await client.query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature,
          account_level, parent_id, allow_transactions, linked_table, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (account_code) DO NOTHING
      `, [
        accountCode,
        `${clientRow.name} (عميل)`,
        'أصول',
        'مدين',
        4,
        clientParentId,
        true,
        'clients',
        true
      ])

      console.log(`   ✅ ${accountCode}: ${clientRow.name}`)
    }

    // إنشاء حسابات الموظفين
    console.log('\n👨‍💼 إنشاء حسابات الموظفين...')
    for (const empRow of employees.rows) {
      const accountCode = `E${empRow.id.toString().padStart(6, '0')}`
      
      await client.query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature,
          account_level, parent_id, allow_transactions, linked_table, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (account_code) DO NOTHING
      `, [
        accountCode,
        `${empRow.name} (موظف)`,
        'خصوم',
        'دائن',
        4,
        employeeParentId,
        true,
        'employees',
        true
      ])

      console.log(`   ✅ ${accountCode}: ${empRow.name}`)
    }

    console.log('\n✅ تم إنشاء جميع الحسابات المرتبطة بنجاح!')

  } catch (error) {
    console.error('❌ خطأ:', error)
  } finally {
    await client.end()
  }
}

createLinkedAccounts()
