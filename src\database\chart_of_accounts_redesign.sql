-- إعادة تصميم جدول دليل الحسابات مع نظام المراتب الأربع

-- إنشاء جدول جديد بالتصميم المحسن
CREATE TABLE chart_of_accounts_new (
  id SERIAL PRIMARY KEY,
  account_code VARCHAR(20) UNIQUE NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  account_name_en VARCHAR(255),
  
  -- نظام المراتب الأربع
  level_1_id INTEGER, -- المرتبة الأولى (الأصول، الخصوم، إلخ)
  level_2_id INTEGER, -- المرتبة الثانية (الأصول المتداولة، الأصول الثابتة، إلخ)
  level_3_id INTEGER, -- المرتبة الثالثة (النقدية والبنوك، المخزون، إلخ)
  level_4_id INTEGER, -- المرتبة الرابعة (الحسابات الفرعية النهائية)
  
  -- تحديد المرتبة الحالية للحساب
  account_level INTEGER NOT NULL CHECK (account_level BETWEEN 1 AND 4),
  
  -- نوع الحساب المحاسبي
  account_type VARCHAR(50) NOT NULL, -- أصول، خصوم، حقوق ملكية، إيرادات، مصروفات
  account_subtype VARCHAR(100), -- فرعي من نوع الحساب
  
  -- طبيعة الحساب
  normal_balance VARCHAR(10) NOT NULL CHECK (normal_balance IN ('مدين', 'دائن')),
  
  -- معلومات الرصيد
  opening_balance DECIMAL(15,2) DEFAULT 0,
  current_balance DECIMAL(15,2) DEFAULT 0,
  
  -- حالة الحساب
  is_active BOOLEAN DEFAULT true,
  is_system_account BOOLEAN DEFAULT false, -- حساب نظام لا يمكن حذفه
  allow_transactions BOOLEAN DEFAULT false, -- يسمح بالمعاملات (فقط للمرتبة الرابعة)
  
  -- معلومات إضافية
  description TEXT,
  notes TEXT,
  
  -- تواريخ
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER,
  updated_by INTEGER
);

-- إنشاء الفهارس
CREATE INDEX idx_chart_accounts_code ON chart_of_accounts_new(account_code);
CREATE INDEX idx_chart_accounts_level ON chart_of_accounts_new(account_level);
CREATE INDEX idx_chart_accounts_type ON chart_of_accounts_new(account_type);
CREATE INDEX idx_chart_accounts_level1 ON chart_of_accounts_new(level_1_id);
CREATE INDEX idx_chart_accounts_level2 ON chart_of_accounts_new(level_2_id);
CREATE INDEX idx_chart_accounts_level3 ON chart_of_accounts_new(level_3_id);
CREATE INDEX idx_chart_accounts_level4 ON chart_of_accounts_new(level_4_id);
CREATE INDEX idx_chart_accounts_active ON chart_of_accounts_new(is_active);
CREATE INDEX idx_chart_accounts_transactions ON chart_of_accounts_new(allow_transactions);

-- إدراج البيانات الأساسية (المرتبة الأولى)
INSERT INTO chart_of_accounts_new (
  account_code, account_name, account_level, account_type, normal_balance, 
  is_system_account, allow_transactions, description
) VALUES
-- الأصول
('1', 'الأصول', 1, 'أصول', 'مدين', true, false, 'مجموعة الأصول الرئيسية'),
-- الخصوم
('2', 'الخصوم', 1, 'خصوم', 'دائن', true, false, 'مجموعة الخصوم الرئيسية'),
-- حقوق الملكية
('3', 'حقوق الملكية', 1, 'حقوق ملكية', 'دائن', true, false, 'مجموعة حقوق الملكية'),
-- الإيرادات
('4', 'الإيرادات', 1, 'إيرادات', 'دائن', true, false, 'مجموعة الإيرادات'),
-- المصروفات
('5', 'المصروفات', 1, 'مصروفات', 'مدين', true, false, 'مجموعة المصروفات');

-- إدراج البيانات (المرتبة الثانية)
INSERT INTO chart_of_accounts_new (
  account_code, account_name, account_level, account_type, normal_balance,
  level_1_id, is_system_account, allow_transactions, description
) VALUES
-- الأصول المتداولة
('11', 'الأصول المتداولة', 2, 'أصول', 'مدين', 1, true, false, 'الأصول قصيرة الأجل'),
-- الأصول الثابتة
('12', 'الأصول الثابتة', 2, 'أصول', 'مدين', 1, true, false, 'الأصول طويلة الأجل'),
-- الخصوم المتداولة
('21', 'الخصوم المتداولة', 2, 'خصوم', 'دائن', 2, true, false, 'الخصوم قصيرة الأجل'),
-- الخصوم طويلة الأجل
('22', 'الخصوم طويلة الأجل', 2, 'خصوم', 'دائن', 2, true, false, 'الخصوم طويلة الأجل'),
-- رأس المال
('31', 'رأس المال', 2, 'حقوق ملكية', 'دائن', 3, true, false, 'رأس المال المدفوع'),
-- الأرباح المحتجزة
('32', 'الأرباح المحتجزة', 2, 'حقوق ملكية', 'دائن', 3, true, false, 'الأرباح المتراكمة'),
-- إيرادات التشغيل
('41', 'إيرادات التشغيل', 2, 'إيرادات', 'دائن', 4, true, false, 'الإيرادات الأساسية'),
-- إيرادات أخرى
('42', 'إيرادات أخرى', 2, 'إيرادات', 'دائن', 4, true, false, 'الإيرادات الفرعية'),
-- مصروفات التشغيل
('51', 'مصروفات التشغيل', 2, 'مصروفات', 'مدين', 5, true, false, 'المصروفات الأساسية'),
-- مصروفات أخرى
('52', 'مصروفات أخرى', 2, 'مصروفات', 'مدين', 5, true, false, 'المصروفات الفرعية');

-- دالة لحساب الرصيد الإجمالي للحسابات الرئيسية
CREATE OR REPLACE FUNCTION calculate_parent_balance(parent_level INTEGER, parent_id INTEGER)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    total_balance DECIMAL(15,2) := 0;
BEGIN
    -- حساب مجموع أرصدة الحسابات الفرعية
    CASE parent_level
        WHEN 1 THEN
            SELECT COALESCE(SUM(current_balance), 0) INTO total_balance
            FROM chart_of_accounts_new 
            WHERE level_1_id = parent_id AND account_level = 4;
        WHEN 2 THEN
            SELECT COALESCE(SUM(current_balance), 0) INTO total_balance
            FROM chart_of_accounts_new 
            WHERE level_2_id = parent_id AND account_level = 4;
        WHEN 3 THEN
            SELECT COALESCE(SUM(current_balance), 0) INTO total_balance
            FROM chart_of_accounts_new 
            WHERE level_3_id = parent_id AND account_level = 4;
    END CASE;
    
    RETURN total_balance;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث أرصدة الحسابات الرئيسية
CREATE OR REPLACE FUNCTION update_parent_balances()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث رصيد المرتبة الثالثة
    IF NEW.level_3_id IS NOT NULL THEN
        UPDATE chart_of_accounts_new 
        SET current_balance = calculate_parent_balance(3, NEW.level_3_id),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.level_3_id;
    END IF;
    
    -- تحديث رصيد المرتبة الثانية
    IF NEW.level_2_id IS NOT NULL THEN
        UPDATE chart_of_accounts_new 
        SET current_balance = calculate_parent_balance(2, NEW.level_2_id),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.level_2_id;
    END IF;
    
    -- تحديث رصيد المرتبة الأولى
    IF NEW.level_1_id IS NOT NULL THEN
        UPDATE chart_of_accounts_new 
        SET current_balance = calculate_parent_balance(1, NEW.level_1_id),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.level_1_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفز لتحديث الأرصدة تلقائياً
CREATE TRIGGER trigger_update_parent_balances
    AFTER INSERT OR UPDATE OF current_balance ON chart_of_accounts_new
    FOR EACH ROW
    WHEN (NEW.account_level = 4)
    EXECUTE FUNCTION update_parent_balances();

-- دالة للحصول على المسار الكامل للحساب
CREATE OR REPLACE FUNCTION get_account_path(account_id INTEGER)
RETURNS TEXT AS $$
DECLARE
    account_record RECORD;
    path_text TEXT := '';
    level1_name TEXT := '';
    level2_name TEXT := '';
    level3_name TEXT := '';
BEGIN
    SELECT * INTO account_record FROM chart_of_accounts_new WHERE id = account_id;
    
    IF account_record.level_1_id IS NOT NULL THEN
        SELECT account_name INTO level1_name FROM chart_of_accounts_new WHERE id = account_record.level_1_id;
        path_text := level1_name;
    END IF;
    
    IF account_record.level_2_id IS NOT NULL THEN
        SELECT account_name INTO level2_name FROM chart_of_accounts_new WHERE id = account_record.level_2_id;
        path_text := path_text || ' > ' || level2_name;
    END IF;
    
    IF account_record.level_3_id IS NOT NULL THEN
        SELECT account_name INTO level3_name FROM chart_of_accounts_new WHERE id = account_record.level_3_id;
        path_text := path_text || ' > ' || level3_name;
    END IF;
    
    path_text := path_text || ' > ' || account_record.account_name;
    
    RETURN path_text;
END;
$$ LANGUAGE plpgsql;
