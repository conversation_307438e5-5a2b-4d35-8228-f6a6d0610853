'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { IssueSelect } from '@/components/ui/issue-select'
import { LineageSelect } from '@/components/ui/lineage-select'
import { EmployeeSelect } from '@/components/ui/employee-select'
import {
  Share2,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  Calculator,
  DollarSign,
  Users,
  FileText
} from 'lucide-react'

interface ServiceDistribution {
  service_id: number
  service_name: string
  percentage: number
  amount: number
  lawyer_id: number
  lawyer_name: string
}

interface CaseDistribution {
  id: number
  issue_id: number
  issue_title: string
  case_number: string
  case_amount: number
  lineage_id: number
  lineage_name: string
  admin_percentage: number
  admin_amount: number
  remaining_amount: number
  service_distributions: ServiceDistribution[]
  created_date: string
}

export default function CaseDistributionPage() {
  const [distributions, setDistributions] = useState<CaseDistribution[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingDistribution, setEditingDistribution] = useState<CaseDistribution | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    issue_id: '',
    issue_data: null as any,
    lineage_id: '',
    lineage_data: null as any,
    service_distributions: [] as ServiceDistribution[]
  })

  const fetchDistributions = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/case-distribution')
      const result = await response.json()

      if (result.success) {
        setDistributions(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات توزيع القضايا')
        setDistributions([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setDistributions([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDistributions()
  }, [])

  const filteredDistributions = distributions.filter(dist =>
    dist.issue_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dist.case_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dist.lineage_name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف توزيع هذه القضية؟')) {
      try {
        const response = await fetch(`/api/case-distribution?id=${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          alert('تم حذف توزيع القضية بنجاح')
          fetchDistributions()
        } else {
          alert(result.error || 'فشل في حذف توزيع القضية')
        }
      } catch (error) {
        console.error('Error deleting distribution:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  const handleView = (distribution: CaseDistribution) => {
    setEditingDistribution(distribution)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingDistribution(null)
    setFormData({
      issue_id: '',
      issue_data: null,
      lineage_id: '',
      lineage_data: null,
      service_distributions: []
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleIssueChange = (issueId: string, issueData: any) => {
    setFormData({
      ...formData,
      issue_id: issueId,
      issue_data: issueData
    })
  }

  const handleLineageChange = (lineageId: string, lineageData: any) => {
    if (lineageData && formData.issue_data) {
      // حساب نسبة الإدارة ومبلغها
      const adminAmount = (formData.issue_data.amount * lineageData.admin_percentage) / 100
      const remainingAmount = formData.issue_data.amount - adminAmount

      // إنشاء توزيع الخدمات بنسب افتراضية (يمكن تعديلها لاحقاً)
      const defaultServicePercentages = {
        'اعداد': 30,
        'جلسة': 25,
        'متابعة': 25,
        'اشراف': 20
      }

      const serviceDistributions = [
        {
          service_id: 1,
          service_name: 'اعداد',
          percentage: defaultServicePercentages['اعداد'],
          amount: (remainingAmount * defaultServicePercentages['اعداد']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        },
        {
          service_id: 2,
          service_name: 'جلسة',
          percentage: defaultServicePercentages['جلسة'],
          amount: (remainingAmount * defaultServicePercentages['جلسة']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        },
        {
          service_id: 3,
          service_name: 'متابعة',
          percentage: defaultServicePercentages['متابعة'],
          amount: (remainingAmount * defaultServicePercentages['متابعة']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        },
        {
          service_id: 4,
          service_name: 'اشراف',
          percentage: defaultServicePercentages['اشراف'],
          amount: (remainingAmount * defaultServicePercentages['اشراف']) / 100,
          lawyer_id: 0,
          lawyer_name: ''
        }
      ]

      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        service_distributions: serviceDistributions
      })
    } else {
      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        service_distributions: []
      })
    }
  }

  const handleLawyerChange = (serviceId: number, lawyerId: string, lawyerData: any) => {
    const updatedDistributions = formData.service_distributions.map(dist =>
      dist.service_id === serviceId
        ? { ...dist, lawyer_id: Number(lawyerId), lawyer_name: lawyerData ? lawyerData.name : '' }
        : dist
    )

    setFormData({
      ...formData,
      service_distributions: updatedDistributions
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/case-distribution', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة توزيع القضية بنجاح')
          fetchDistributions()
        } else {
          alert(result.error || 'فشل في إضافة توزيع القضية')
          return
        }
      }

      setIsModalOpen(false)
      setEditingDistribution(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const stats = {
    total: distributions.length,
    totalAmount: distributions.reduce((sum, d) => sum + d.case_amount, 0),
    totalAdminAmount: distributions.reduce((sum, d) => sum + d.admin_amount, 0)
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Share2 className="h-8 w-8 mr-3 text-green-600" />
              توزيع القضايا
            </h1>
            <p className="text-gray-600 mt-1">إدارة وتوزيع مبالغ القضايا على الخدمات والمحامين</p>
          </div>

          <Button onClick={handleAddNew} className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة توزيع جديد
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Share2 className="h-8 w-8 text-green-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي التوزيعات</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي المبالغ</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalAmount.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Calculator className="h-8 w-8 text-purple-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">مبالغ الإدارة</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalAdminAmount.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="max-w-md">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في توزيعات القضايا..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* عرض رسالة الخطأ */}
        {dbError && (
          <Card>
            <CardContent className="p-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">خطأ في الاتصال بقاعدة البيانات</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={fetchDistributions} variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Share2 className="h-5 w-5 mr-2" />
              قائمة توزيعات القضايا ({filteredDistributions.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!dbError && !isLoading && (
              <div className="space-y-6">
                {filteredDistributions.map((distribution) => (
                  <div key={distribution.id} className="border rounded-lg p-6 bg-gray-50">
                    {/* معلومات القضية الأساسية */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600">القضية</Label>
                        <p className="font-medium">{distribution.issue_title}</p>
                        <p className="text-sm text-gray-500">{distribution.case_number}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">مبلغ القضية</Label>
                        <p className="font-bold text-blue-600">{distribution.case_amount.toLocaleString()} ريال</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">نسبة الإدارة</Label>
                        <p className="font-medium text-purple-600">
                          {distribution.admin_percentage}% = {distribution.admin_amount.toLocaleString()} ريال
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">المبلغ المتبقي للتوزيع</Label>
                        <p className="font-bold text-green-600">{distribution.remaining_amount.toLocaleString()} ريال</p>
                      </div>
                    </div>

                    {/* جدول توزيع الخدمات */}
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-300">
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="border border-gray-300 p-3 text-right font-semibold">اسم الخدمة</th>
                            <th className="border border-gray-300 p-3 text-center font-semibold">النسبة (%)</th>
                            <th className="border border-gray-300 p-3 text-center font-semibold">المبلغ</th>
                            <th className="border border-gray-300 p-3 text-right font-semibold">اسم المحامي</th>
                          </tr>
                        </thead>
                        <tbody>
                          {distribution.service_distributions.map((service) => (
                            <tr key={service.service_id} className="hover:bg-gray-50">
                              <td className="border border-gray-300 p-3 font-medium">{service.service_name}</td>
                              <td className="border border-gray-300 p-3 text-center">
                                <Badge variant="outline">{service.percentage}%</Badge>
                              </td>
                              <td className="border border-gray-300 p-3 text-center font-bold text-green-600">
                                {service.amount.toLocaleString()}
                              </td>
                              <td className="border border-gray-300 p-3">{service.lawyer_name || 'غير محدد'}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* أزرار الإجراءات */}
                    <div className="flex justify-end space-x-2 space-x-reverse mt-4">
                      <Button size="sm" variant="outline" onClick={() => handleView(distribution)}>
                        <Eye className="h-4 w-4 mr-1" />
                        عرض
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(distribution.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        حذف
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {!dbError && !isLoading && distributions.length === 0 && (
              <div className="text-center py-8">
                <Share2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد توزيعات</h3>
                <p className="text-gray-600">لم يتم العثور على أي توزيعات للقضايا في النظام</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal للإضافة/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-7xl max-h-[95vh] flex flex-col">
              {/* Header ثابت */}
              <div className="flex items-center justify-between p-6 border-b bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold text-gray-800">
                  {modalType === 'add' && '📋 إضافة توزيع قضية جديد'}
                  {modalType === 'view' && '👁️ عرض توزيع القضية'}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)} className="hover:bg-gray-200">
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Content قابل للتمرير */}
              <div className="flex-1 overflow-y-auto p-6">
                {modalType === 'view' && editingDistribution ? (
                  <div className="space-y-6">
                    {/* معلومات القضية */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg text-blue-600">📄 معلومات القضية</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-6">
                          <div>
                            <Label className="text-sm font-medium text-gray-600">عنوان القضية</Label>
                            <p className="mt-1 p-3 bg-gray-50 rounded-lg font-medium">{editingDistribution.issue_title}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">مبلغ القضية</Label>
                            <p className="mt-1 p-3 bg-blue-50 rounded-lg text-blue-800 font-bold text-lg">
                              💰 {editingDistribution.case_amount.toLocaleString()} ريال
                            </p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">مجموعة النسب</Label>
                            <p className="mt-1 p-3 bg-gray-50 rounded-lg font-medium">{editingDistribution.lineage_name}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">نسبة الإدارة</Label>
                            <p className="mt-1 p-3 bg-purple-50 rounded-lg text-purple-800 font-bold text-lg">
                              📊 {editingDistribution.admin_percentage}% = {editingDistribution.admin_amount.toLocaleString()} ريال
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* توزيع الخدمات */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg text-green-600">⚖️ توزيع الخدمات على المحامين</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border border-gray-300 rounded-lg overflow-hidden">
                            <thead>
                              <tr className="bg-gradient-to-r from-gray-100 to-gray-200">
                                <th className="border border-gray-300 p-4 text-right font-bold text-gray-700">🔧 الخدمة</th>
                                <th className="border border-gray-300 p-4 text-center font-bold text-gray-700">📊 النسبة</th>
                                <th className="border border-gray-300 p-4 text-center font-bold text-gray-700">💰 المبلغ</th>
                                <th className="border border-gray-300 p-4 text-right font-bold text-gray-700">👨‍💼 المحامي</th>
                              </tr>
                            </thead>
                            <tbody>
                              {editingDistribution.service_distributions.map((service, index) => (
                                <tr key={service.service_id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                  <td className="border border-gray-300 p-4 font-medium">{service.service_name}</td>
                                  <td className="border border-gray-300 p-4 text-center">
                                    <Badge className="bg-blue-100 text-blue-800 font-bold">{service.percentage}%</Badge>
                                  </td>
                                  <td className="border border-gray-300 p-4 text-center font-bold text-green-600 text-lg">
                                    {service.amount.toLocaleString()} ريال
                                  </td>
                                  <td className="border border-gray-300 p-4 font-medium">{service.lawyer_name || 'غير محدد'}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* اختيار القضية */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg text-blue-600">📋 اختيار القضية</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-6">
                          <IssueSelect
                            value={formData.issue_id}
                            onChange={handleIssueChange}
                            label="القضية"
                            placeholder="اختر القضية"
                            required
                          />

                          {formData.issue_data && (
                            <div>
                              <Label className="text-sm font-medium text-gray-600">مبلغ القضية</Label>
                              <div className="mt-1 p-3 bg-blue-50 border-2 border-blue-200 rounded-lg text-blue-800 font-bold text-lg">
                                💰 {formData.issue_data.amount?.toLocaleString() || 0} ريال
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* اختيار النسب المالية */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg text-purple-600">📊 النسب المالية</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <LineageSelect
                          value={formData.lineage_id}
                          onChange={handleLineageChange}
                          label="مجموعة النسب المالية"
                          placeholder="اختر مجموعة النسب"
                          required
                        />

                        {formData.lineage_data && formData.issue_data && (
                          <div className="p-4 bg-purple-50 border-2 border-purple-200 rounded-lg">
                            <Label className="text-purple-800 font-bold text-lg">💼 نسبة الإدارة</Label>
                            <div className="mt-2 text-purple-800">
                              <span className="font-bold text-xl">{formData.lineage_data.admin_percentage}%</span>
                              <span className="mx-3 text-lg">=</span>
                              <span className="font-bold text-xl">
                                {((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100).toLocaleString()} ريال
                              </span>
                            </div>
                            <div className="mt-2 p-2 bg-green-100 rounded text-green-700 font-medium">
                              💵 المبلغ المتبقي للتوزيع: {(formData.issue_data.amount - ((formData.issue_data.amount * formData.lineage_data.admin_percentage) / 100)).toLocaleString()} ريال
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* توزيع الخدمات */}
                    {formData.service_distributions.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg text-green-600">⚖️ توزيع الخدمات على المحامين</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {formData.service_distributions.map((service, index) => (
                              <div key={service.service_id} className="p-4 border-2 border-gray-200 rounded-lg bg-gradient-to-r from-gray-50 to-white">
                                <div className="grid grid-cols-12 gap-4 items-end">
                                  <div className="col-span-3">
                                    <Label className="text-sm font-medium text-gray-600">🔧 الخدمة</Label>
                                    <div className="mt-1 p-3 bg-white border-2 border-gray-200 rounded-lg font-bold text-gray-800">
                                      {service.service_name}
                                    </div>
                                  </div>
                                  <div className="col-span-2">
                                    <Label className="text-sm font-medium text-gray-600">📊 النسبة</Label>
                                    <div className="mt-1 p-3 bg-blue-50 border-2 border-blue-200 rounded-lg text-center font-bold text-blue-600 text-lg">
                                      {service.percentage}%
                                    </div>
                                  </div>
                                  <div className="col-span-2">
                                    <Label className="text-sm font-medium text-gray-600">💰 المبلغ</Label>
                                    <div className="mt-1 p-3 bg-green-50 border-2 border-green-200 rounded-lg text-center font-bold text-green-600 text-lg">
                                      {service.amount.toLocaleString()}
                                    </div>
                                  </div>
                                  <div className="col-span-5">
                                    <EmployeeSelect
                                      value={service.lawyer_id.toString()}
                                      onChange={(lawyerId, lawyerData) => handleLawyerChange(service.service_id, lawyerId, lawyerData)}
                                      label="👨‍💼 المحامي المكلف"
                                      placeholder="اختر المحامي"
                                      required
                                    />
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </form>
                )}
              </div>

              {/* Footer ثابت */}
              {modalType === 'add' && (
                <div className="p-6 border-t bg-gray-50 rounded-b-lg">
                  <div className="flex space-x-4 space-x-reverse">
                    <Button
                      type="submit"
                      onClick={handleSubmit}
                      className="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-3"
                    >
                      <Save className="h-5 w-5 mr-2" />
                      💾 حفظ التوزيع
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsModalOpen(false)}
                      className="flex-1 border-2 border-gray-300 hover:bg-gray-100 font-bold py-3"
                    >
                      ❌ إلغاء
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
