'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { FileText, Plus, Edit, Search, User, DollarSign, Eye, Printer, Trash2 } from 'lucide-react'

interface ReceiptVoucher {
  id: number
  entry_number: string
  entry_date: string
  payer_name: string
  payer_type: string
  payer_id?: number
  debit_account_id: number
  credit_account_id: number
  amount: number
  currency_id: number
  currency_code?: string
  payment_method_id: number
  payment_method_name?: string
  cost_center_id?: number
  cost_center_name?: string
  description: string
  reference_number?: string
  case_id?: number
  case_number?: string
  service_id?: number
  service_name?: string
  status: string
  created_by_name?: string
  created_at: string
  voucher_type: string
  account_name?: string
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  is_linked_record?: boolean
  original_table?: string
  external_id?: number
}

interface Currency {
  id: number
  currency_code: string
  currency_name: string
  symbol: string
}

interface PaymentMethod {
  id: number
  method_code: string
  method_name: string
}

interface CostCenter {
  id: number
  center_code: string
  center_name: string
}

interface Case {
  id: number
  case_number: string
  title: string
  client_name: string
}

interface Service {
  id: number
  name: string
  lineage_name: string
}

export default function ReceiptVouchersPage() {
  const [vouchers, setVouchers] = useState<ReceiptVoucher[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [cases, setCases] = useState<Case[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingVoucher, setEditingVoucher] = useState<ReceiptVoucher | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  const [formData, setFormData] = useState({
    entry_date: new Date().toISOString().split('T')[0],
    debit_account_id: '',
    credit_account_id: '',
    amount: '',
    currency_id: '1',
    payment_method_id: '1',
    cost_center_id: '',
    description: '',
    reference_number: '',
    case_id: '',
    service_id: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      const vouchersResponse = await fetch('/api/accounting/receipt-vouchers')
      if (vouchersResponse.ok) {
        const vouchersData = await vouchersResponse.json()
        setVouchers(vouchersData.vouchers || [])
      }

      const accountsResponse = await fetch('/api/accounting/accounts')
      if (accountsResponse.ok) {
        const accountsData = await accountsResponse.json()
        setAccounts(accountsData.accounts || [])
      }

      const currenciesResponse = await fetch('/api/accounting/currencies')
      if (currenciesResponse.ok) {
        const currenciesData = await currenciesResponse.json()
        setCurrencies(currenciesData.currencies || [])
      }

      const paymentMethodsResponse = await fetch('/api/accounting/payment-methods')
      if (paymentMethodsResponse.ok) {
        const paymentMethodsData = await paymentMethodsResponse.json()
        setPaymentMethods(paymentMethodsData.methods || [])
      }

      const costCentersResponse = await fetch('/api/accounting/cost-centers')
      if (costCentersResponse.ok) {
        const costCentersData = await costCentersResponse.json()
        console.log('Cost centers data:', costCentersData)
        setCostCenters(costCentersData.data || costCentersData.centers || [])
      }

      const casesResponse = await fetch('/api/issues')
      if (casesResponse.ok) {
        const casesData = await casesResponse.json()
        console.log('Cases data:', casesData)
        setCases(casesData.data || casesData.issues || [])
      }

      const servicesResponse = await fetch('/api/services')
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json()
        console.log('Services data:', servicesData)
        setServices(servicesData.data || servicesData.services || [])
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = editingVoucher
        ? `/api/accounting/receipt-vouchers/${editingVoucher.id}`
        : '/api/accounting/receipt-vouchers'

      const method = editingVoucher ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        await fetchData()
        setShowAddDialog(false)
        setEditingVoucher(null)
        resetForm()
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حفظ السند:', error)
      alert('حدث خطأ أثناء حفظ السند')
    }
  }

  const resetForm = () => {
    setFormData({
      entry_date: new Date().toISOString().split('T')[0],
      debit_account_id: '',
      credit_account_id: '',
      amount: '',
      currency_id: '1',
      payment_method_id: '1',
      cost_center_id: '',
      description: '',
      reference_number: '',
      case_id: '',
      service_id: ''
    })
  }

  const handleEdit = (voucher: ReceiptVoucher) => {
    setEditingVoucher(voucher)
    setFormData({
      entry_date: voucher.entry_date,
      debit_account_id: voucher.debit_account_id.toString(),
      credit_account_id: voucher.credit_account_id.toString(),
      amount: voucher.amount.toString(),
      currency_id: voucher.currency_id.toString(),
      payment_method_id: voucher.payment_method_id?.toString() || '',
      cost_center_id: voucher.cost_center_id?.toString() || '',
      description: voucher.description,
      reference_number: voucher.reference_number || '',
      case_id: voucher.case_id?.toString() || '',
      service_id: voucher.service_id?.toString() || ''
    })
    setShowAddDialog(true)
  }

  const handleView = (voucher: ReceiptVoucher) => {
    const printWindow = window.open('', '_blank', 'width=800,height=600')
    if (printWindow) {
      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>سند قبض رقم ${voucher.entry_number}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .voucher-info { border: 2px solid #333; padding: 20px; }
              .row { display: flex; justify-content: space-between; margin: 10px 0; text-align: right; }
              .label { font-weight: bold; text-align: right; }
              .amount { font-size: 18px; font-weight: bold; color: #2e7d32; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>سند قبض</h1>
              <h2>رقم: ${voucher.entry_number}</h2>
            </div>
            <div class="voucher-info">
              <div class="row">
                <span class="label">التاريخ:</span>
                <span>${new Date(voucher.entry_date).toLocaleDateString('en-GB')}</span>
              </div>
              <div class="row">
                <span class="label">المبلغ:</span>
                <span class="amount">${voucher.amount.toLocaleString()} ${voucher.currency_code || 'ر.ي'}</span>
              </div>
              <div class="row">
                <span class="label">قبض من:</span>
                <span>${voucher.payer_name}</span>
              </div>
              <div class="row">
                <span class="label">إلى حساب:</span>
                <span>${voucher.account_name}</span>
              </div>
              <div class="row">
                <span class="label">البيان:</span>
                <span>${voucher.description}</span>
              </div>
            </div>
          </body>
        </html>
      `)
      printWindow.document.close()
    }
  }

  const handleDelete = async (voucherId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا السند؟')) {
      return
    }

    try {
      const response = await fetch(`/api/accounting/receipt-vouchers/${voucherId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchData()
        alert('تم حذف السند بنجاح')
      } else {
        const errorData = await response.json()
        alert(`خطأ في حذف السند: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حذف السند:', error)
      alert('حدث خطأ أثناء حذف السند')
    }
  }

  const filteredVouchers = vouchers.filter(voucher =>
    voucher.entry_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    voucher.payer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    voucher.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'draft': { label: 'مسودة', variant: 'secondary' as const },
      'approved': { label: 'معتمد', variant: 'default' as const },
      'cancelled': { label: 'ملغي', variant: 'destructive' as const }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <FileText className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">سندات القبض</h1>
              <p className="text-gray-600">إدارة سندات القبض والمقبوضات</p>
            </div>
          </div>

          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 ml-2" />
            سند قبض جديد
          </Button>
        </div>

        {/* البحث */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في سندات القبض..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* جدول السندات */}
        <Card>
          <CardHeader>
            <CardTitle>سندات القبض ({filteredVouchers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل السندات...</p>
              </div>
            ) : filteredVouchers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد سندات قبض</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-right p-3 font-semibold text-gray-700">رقم السند</th>
                      <th className="text-right p-3 font-semibold text-gray-700">التاريخ</th>
                      <th className="text-right p-3 font-semibold text-gray-700">الدافع</th>
                      <th className="text-right p-3 font-semibold text-gray-700">المبلغ</th>
                      <th className="text-right p-3 font-semibold text-gray-700">البيان</th>
                      <th className="text-center p-3 font-semibold text-gray-700">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredVouchers.map((voucher, index) => (
                      <tr key={voucher.id} className={`border-b hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                        <td className="p-3">
                          <span className="font-mono text-blue-600 font-medium">
                            {voucher.entry_number}
                          </span>
                        </td>
                        <td className="p-3 text-gray-600">
                          {new Date(voucher.entry_date).toLocaleDateString('en-GB')}
                        </td>
                        <td className="p-3">
                          <div className="flex items-center">
                            <User className="h-4 w-4 text-gray-400 ml-2" />
                            <span className="font-medium text-gray-900">{voucher.payer_name}</span>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex items-center">
                            <DollarSign className="h-4 w-4 text-green-500 ml-1" />
                            <span className="font-bold text-green-600">
                              {voucher.amount.toLocaleString()} {voucher.currency_code || 'ر.ي'}
                            </span>
                          </div>
                        </td>
                        <td className="p-3 text-gray-600 max-w-xs truncate" title={voucher.description}>
                          {voucher.description}
                        </td>
                        <td className="p-3">
                          <div className="flex justify-center space-x-1 space-x-reverse">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleView(voucher)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              title="مشاهدة"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(voucher)}
                              className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                              title="تعديل"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(voucher.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="حذف"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل السند */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
            <DialogHeader className="bg-gradient-to-r from-green-50 to-green-100 -m-6 mb-6 p-6 rounded-t-lg">
              <DialogTitle className="text-2xl font-bold text-green-800 flex items-center">
                <FileText className="h-7 w-7 mr-3" />
                {editingVoucher ? '✏️ تعديل سند القبض' : '💰 سند قبض جديد'}
              </DialogTitle>
              <DialogDescription className="text-green-600 mt-2">
                {editingVoucher ? 'تعديل بيانات سند القبض المحدد' : 'إنشاء سند قبض جديد مع تفاصيل المبلغ والحساب'}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* الصف الأول: رقم المرجع ورقم القضية */}
              <div className="grid grid-cols-4 gap-4 mb-4">
                <div className="col-span-1">
                  <Label htmlFor="reference_number" className="text-sm font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md inline-block mb-2">
                    📄 رقم المرجع
                  </Label>
                  <Input
                    id="reference_number"
                    value={formData.reference_number}
                    onChange={(e) => setFormData({...formData, reference_number: e.target.value})}
                    placeholder="رقم المرجع (اختياري)"
                    className="bg-gray-50 border-gray-300 focus:border-blue-500 focus:bg-white transition-colors"
                  />
                </div>

                <div className="col-span-2">
                  <Label htmlFor="case_id" className="text-sm font-semibold text-purple-700 bg-purple-50 px-2 py-1 rounded-md inline-block mb-2">
                    ⚖️ القضية
                  </Label>
                  <Select value={formData.case_id} onValueChange={(value) => setFormData({...formData, case_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-purple-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="🔍 اختر القضية أو ابحث..." />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون قضية</SelectItem>
                      {cases.map((caseItem) => (
                        <SelectItem key={caseItem.id} value={caseItem.id.toString()}>
                          <div className="flex flex-col py-1">
                            <span className="font-medium text-purple-700">#{caseItem.case_number}</span>
                            <span className="text-sm text-gray-600 truncate">{caseItem.title}</span>
                            <span className="text-xs text-gray-500">الموكل: {caseItem.client_name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* القوائم الثلاث: مركز التكلفة والخدمة */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="cost_center_id" className="text-sm font-semibold text-orange-700 bg-orange-50 px-2 py-1 rounded-md inline-block mb-2">
                    🏢 مركز التكلفة
                  </Label>
                  <Select value={formData.cost_center_id} onValueChange={(value) => setFormData({...formData, cost_center_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-orange-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="🔍 اختر مركز التكلفة..." />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون مركز تكلفة</SelectItem>
                      {costCenters.map((center) => (
                        <SelectItem key={center.id} value={center.id.toString()}>
                          <div className="flex flex-col py-1">
                            <span className="font-medium text-orange-700">{center.center_code}</span>
                            <span className="text-sm text-gray-600">{center.center_name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="service_id" className="text-sm font-semibold text-teal-700 bg-teal-50 px-2 py-1 rounded-md inline-block mb-2">
                    🔧 الخدمة
                  </Label>
                  <Select value={formData.service_id} onValueChange={(value) => setFormData({...formData, service_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-teal-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="🔍 اختر الخدمة..." />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون خدمة</SelectItem>
                      {services.map((service) => (
                        <SelectItem key={service.id} value={service.id.toString()}>
                          <div className="flex flex-col py-1">
                            <span className="font-medium text-teal-700">{service.name}</span>
                            <span className="text-sm text-gray-600">{service.lineage_name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* الصف الثاني: التاريخ والمبلغ والعملة وطريقة الدفع */}
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="entry_date" className="text-sm font-semibold text-green-700 bg-green-50 px-2 py-1 rounded-md inline-block mb-2">
                    📅 تاريخ السند
                  </Label>
                  <Input
                    id="entry_date"
                    type="date"
                    value={formData.entry_date}
                    onChange={(e) => setFormData({...formData, entry_date: e.target.value})}
                    required
                    className="bg-gray-50 border-gray-300 focus:border-green-500 focus:bg-white transition-colors"
                  />
                </div>

                <div>
                  <Label htmlFor="amount" className="text-sm font-semibold text-green-700 bg-green-50 px-2 py-1 rounded-md inline-block mb-2">
                    💰 المبلغ
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount}
                    onChange={(e) => setFormData({...formData, amount: e.target.value})}
                    placeholder="0.00"
                    required
                    className="bg-gray-50 border-gray-300 focus:border-green-500 focus:bg-white transition-colors"
                  />
                </div>

                <div>
                  <Label htmlFor="currency_id" className="text-sm font-semibold text-yellow-700 bg-yellow-50 px-2 py-1 rounded-md inline-block mb-2">
                    💱 العملة
                  </Label>
                  <Select value={formData.currency_id} onValueChange={(value) => setFormData({...formData, currency_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-yellow-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="اختر العملة" />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies.map((currency) => (
                        <SelectItem key={currency.id} value={currency.id.toString()}>
                          {currency.currency_code} - {currency.currency_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="payment_method_id" className="text-sm font-semibold text-indigo-700 bg-indigo-50 px-2 py-1 rounded-md inline-block mb-2">
                    💳 طريقة الدفع
                  </Label>
                  <Select value={formData.payment_method_id} onValueChange={(value) => setFormData({...formData, payment_method_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-indigo-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="اختر طريقة الدفع" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.id.toString()}>
                          {method.method_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* الصف الثالث: الحسابات */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="debit_account_id" className="text-sm font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md inline-block mb-2">
                    🏦 الحساب المدين (المقبوض إليه)
                  </Label>
                  <Select value={formData.debit_account_id} onValueChange={(value) => setFormData({...formData, debit_account_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-blue-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="اختر الحساب المدين..." />
                    </SelectTrigger>
                    <SelectContent>
                      {accounts.filter(account => account.account_type === 'A').map((account) => (
                        <SelectItem key={account.id} value={account.id.toString()}>
                          {account.account_code} - {account.account_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="credit_account_id" className="text-sm font-semibold text-red-700 bg-red-50 px-2 py-1 rounded-md inline-block mb-2">
                    👤 الحساب الدائن (الدافع)
                  </Label>
                  <Select value={formData.credit_account_id} onValueChange={(value) => setFormData({...formData, credit_account_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-red-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="اختر الحساب الدائن..." />
                    </SelectTrigger>
                    <SelectContent>
                      {accounts.map((account) => (
                        <SelectItem key={account.id} value={account.id.toString()}>
                          {account.account_code} - {account.account_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* البيان */}
              <div>
                <Label htmlFor="description" className="text-sm font-semibold text-gray-700 bg-gray-50 px-2 py-1 rounded-md inline-block mb-2">
                  📝 البيان
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="وصف تفصيلي للسند..."
                  required
                  rows={3}
                  className="bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors"
                />
              </div>

              <DialogFooter className="flex justify-between pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                  إلغاء
                </Button>
                <Button type="submit" className="bg-green-600 hover:bg-green-700">
                  {editingVoucher ? 'تحديث السند' : 'حفظ السند'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
