// إضافة عمود employee_id لجدول المستخدمين (الموظفين أب - المستخدمين فرع)
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function addEmployeeIdToUsers() {
  const client = new Client(dbConfig);

  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من هيكل جدول المستخدمين الحالي
    const usersColumns = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'users'
      ORDER BY ordinal_position
    `);

    console.log('📋 هيكل جدول المستخدمين الحالي:');
    usersColumns.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // إضافة عمود employee_id مع المرجع للموظفين
    try {
      await client.query('ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_id INTEGER REFERENCES employees(id)');
      console.log('✅ تم إضافة عمود employee_id مع المرجع للموظفين');
    } catch (error) {
      console.log(`⚠️ عمود employee_id موجود مسبقاً: ${error.message}`);
    }

    // إضافة عمود is_active إذا لم يكن موجوداً
    try {
      await client.query('ALTER TABLE users ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true');
      console.log('✅ تم إضافة عمود is_active');
    } catch (error) {
      console.log(`⚠️ عمود is_active موجود مسبقاً: ${error.message}`);
    }

    // تحديث بيانات المستخدمين مع ربطهم بالموظفين
    await client.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE');

    const usersData = [
      // الموظف الأول (مدير عام) - له مستخدمين متعددين
      {
        username: 'admin',
        email: '<EMAIL>',
        employee_id: 1, // ماجد أحمد علي - مدير عام
        password_hash: '***********',
        device_id: 'DEVICE_ADMIN_001',
        last_login: '2024-01-15 10:30:00',
        is_active: true
      },
      {
        username: 'majed.manager',
        email: '<EMAIL>',
        employee_id: 1, // نفس الموظف - حساب إداري
        password_hash: '***********',
        device_id: 'DEVICE_MANAGER_002',
        last_login: '2024-01-14 09:15:00',
        is_active: true
      },

      // الموظف الثاني (محامي رسمي) - له مستخدم واحد
      {
        username: 'yahya.lawyer',
        email: '<EMAIL>',
        employee_id: 2, // يحيى علي محمد - محامي رسمي
        password_hash: '***********',
        device_id: 'DEVICE_LAWYER_003',
        last_login: '2024-01-13 14:20:00',
        is_active: true
      },

      // الموظف الثالث (استشاري) - له مستخدمين متعددين
      {
        username: 'ahmed.consultant',
        email: '<EMAIL>',
        employee_id: 3, // أحمد صالح حسن - استشاري
        password_hash: '***********',
        device_id: 'DEVICE_CONSULTANT_004',
        last_login: '2024-01-12 11:45:00',
        is_active: true
      },
      {
        username: 'ahmed.mobile',
        email: '<EMAIL>',
        employee_id: 3, // نفس الموظف - حساب الجوال
        password_hash: '***********',
        device_id: 'DEVICE_MOBILE_005',
        last_login: '2024-01-11 16:30:00',
        is_active: true
      },

      // الموظف الرابع (محاسب) - له مستخدم واحد
      {
        username: 'mohamed.accountant',
        email: '<EMAIL>',
        employee_id: 4, // محمد صالح عبدالله - محاسب
        password_hash: '***********',
        device_id: 'DEVICE_ACCOUNTANT_006',
        last_login: '2024-01-10 08:20:00',
        is_active: true
      },

      // الموظف الخامس (سكرتارية) - له مستخدم واحد
      {
        username: 'fatima.secretary',
        email: '<EMAIL>',
        employee_id: 5, // فاطمة علي أحمد - سكرتارية
        password_hash: '***********',
        device_id: 'DEVICE_SECRETARY_007',
        last_login: '2024-01-09 12:15:00',
        is_active: true
      }
    ];

    for (const user of usersData) {
      await client.query(`
        INSERT INTO users (username, email, employee_id, password_hash, device_id, last_login, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [user.username, user.email, user.employee_id, user.password_hash, user.device_id, user.last_login, user.is_active]);
    }
    console.log(`✅ تم إدراج ${usersData.length} مستخدم`);

    // التحقق من النتائج مع عرض العلاقة
    const finalCheck = await client.query(`
      SELECT
        u.id as user_id,
        u.username,
        u.email,
        u.device_id,
        u.last_login,
        u.is_active,
        e.id as employee_id,
        e.name as employee_name,
        e.position as employee_position
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      ORDER BY e.id, u.id
    `);

    console.log('📋 المستخدمين مع ربطهم بالموظفين:');
    let currentEmployeeId = null;
    finalCheck.rows.forEach(row => {
      if (row.employee_id !== currentEmployeeId) {
        console.log(`\n👤 الموظف: ${row.employee_name} (${row.employee_position})`);
        currentEmployeeId = row.employee_id;
      }
      console.log(`   🔑 ${row.username} - ${row.device_id} - آخر دخول: ${row.last_login}`);
    });

    // عرض إحصائيات العلاقة
    const stats = await client.query(`
      SELECT
        e.name as employee_name,
        e.position,
        COUNT(u.id) as users_count
      FROM employees e
      LEFT JOIN users u ON e.id = u.employee_id
      GROUP BY e.id, e.name, e.position
      ORDER BY users_count DESC, e.name
    `);

    console.log('\n📊 إحصائيات المستخدمين لكل موظف:');
    stats.rows.forEach(row => {
      console.log(`   - ${row.employee_name} (${row.position}): ${row.users_count} مستخدم`);
    });

    console.log('\n🎉 تم إنشاء العلاقة بين الموظفين والمستخدمين بنجاح!');
    console.log('📋 العلاقة: employees (أب) ← users (فرع)');
    console.log('🔗 يمكن للموظف الواحد أن يكون له عدة مستخدمين');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
addEmployeeIdToUsers();
