// النسخ النهائي الشامل لجميع البيانات مع إصلاح جميع الجداول
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function finalCompleteMigration() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إصلاح جدول القضايا
    console.log('🔄 جاري إصلاح جدول القضايا...');
    
    const issuesColumns = await client.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'issues'
    `);
    
    const existingIssuesColumns = issuesColumns.rows.map(row => row.column_name);
    console.log('📋 أعمدة جدول القضايا الحالية:', existingIssuesColumns);

    // إضافة الأعمدة المفقودة لجدول القضايا
    if (!existingIssuesColumns.includes('court_id')) {
      await client.query('ALTER TABLE issues ADD COLUMN court_id INTEGER');
      console.log('✅ تم إضافة عمود court_id');
    }

    if (!existingIssuesColumns.includes('case_amount')) {
      await client.query('ALTER TABLE issues ADD COLUMN case_amount DECIMAL(15,2)');
      console.log('✅ تم إضافة عمود case_amount');
    }

    if (!existingIssuesColumns.includes('start_date')) {
      await client.query('ALTER TABLE issues ADD COLUMN start_date DATE');
      console.log('✅ تم إضافة عمود start_date');
    }

    if (!existingIssuesColumns.includes('end_date')) {
      await client.query('ALTER TABLE issues ADD COLUMN end_date DATE');
      console.log('✅ تم إضافة عمود end_date');
    }

    if (!existingIssuesColumns.includes('case_number')) {
      await client.query('ALTER TABLE issues ADD COLUMN case_number VARCHAR(100)');
      console.log('✅ تم إضافة عمود case_number');
    }

    // نسخ بيانات القضايا
    console.log('🔄 جاري نسخ بيانات القضايا...');
    await client.query('TRUNCATE TABLE issues RESTART IDENTITY CASCADE');
    
    const issuesData = [
      { title: 'قضية تجارية - شركة الأمل', case_number: 'C2024001', client_id: 2, issue_type_id: 1, court_id: 1, case_amount: 500000, status: 'جارية', start_date: '2024-01-15' },
      { title: 'قضية عقارية - النزاع العقاري', case_number: 'C2024002', client_id: 1, issue_type_id: 1, court_id: 2, case_amount: 750000, status: 'جارية', start_date: '2024-02-10' },
      { title: 'قضية أحوال شخصية - طلاق', case_number: 'C2024003', client_id: 3, issue_type_id: 3, court_id: 4, case_amount: 50000, status: 'مكتملة', start_date: '2024-01-20' },
      { title: 'قضية عمالية - حقوق عامل', case_number: 'C2024004', client_id: 4, issue_type_id: 4, court_id: 3, case_amount: 100000, status: 'جارية', start_date: '2024-03-05' }
    ];

    for (const issue of issuesData) {
      await client.query(`
        INSERT INTO issues (title, case_number, client_id, issue_type_id, court_id, case_amount, status, start_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [issue.title, issue.case_number, issue.client_id, issue.issue_type_id, issue.court_id, issue.case_amount, issue.status, issue.start_date]);
    }
    console.log(`✅ تم نسخ ${issuesData.length} قضية`);

    // إنشاء بيانات تجريبية للمتابعات
    console.log('🔄 جاري إنشاء بيانات المتابعات...');
    
    // التحقق من وجود جدول المتابعات
    const followsExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'follows'
      )
    `);

    if (followsExists.rows[0].exists) {
      await client.query('TRUNCATE TABLE follows RESTART IDENTITY CASCADE');
      
      const followsData = [
        { issue_id: 1, follow_date: '2024-01-20', follow_type: 'اتصال هاتفي', description: 'متابعة مع العميل حول تطورات القضية', next_follow_date: '2024-01-27', employee_id: 1, status: 'مكتمل' },
        { issue_id: 2, follow_date: '2024-02-15', follow_type: 'زيارة ميدانية', description: 'زيارة موقع النزاع العقاري', next_follow_date: '2024-02-22', employee_id: 2, status: 'مكتمل' },
        { issue_id: 3, follow_date: '2024-01-25', follow_type: 'جلسة محكمة', description: 'حضور جلسة المحكمة', next_follow_date: null, employee_id: 3, status: 'مكتمل' },
        { issue_id: 4, follow_date: '2024-03-10', follow_type: 'مراجعة وثائق', description: 'مراجعة وثائق القضية العمالية', next_follow_date: '2024-03-17', employee_id: 4, status: 'جاري' }
      ];

      for (const follow of followsData) {
        await client.query(`
          INSERT INTO follows (issue_id, follow_date, follow_type, description, next_follow_date, employee_id, status)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [follow.issue_id, follow.follow_date, follow.follow_type, follow.description, follow.next_follow_date, follow.employee_id, follow.status]);
      }
      console.log(`✅ تم نسخ ${followsData.length} متابعة`);
    }

    // إنشاء بيانات تجريبية للحركات
    console.log('🔄 جاري إنشاء بيانات الحركات...');
    
    const movementsExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'movements'
      )
    `);

    if (movementsExists.rows[0].exists) {
      await client.query('TRUNCATE TABLE movements RESTART IDENTITY CASCADE');
      
      const movementsData = [
        { issue_id: 1, movement_date: '2024-01-16', movement_type: 'تقديم دعوى', description: 'تم تقديم الدعوى للمحكمة التجارية', court_id: 1, employee_id: 1 },
        { issue_id: 2, movement_date: '2024-02-11', movement_type: 'جلسة أولى', description: 'الجلسة الأولى للنظر في القضية', court_id: 2, employee_id: 2 },
        { issue_id: 3, movement_date: '2024-01-21', movement_type: 'حكم نهائي', description: 'صدور الحكم النهائي في القضية', court_id: 4, employee_id: 3 },
        { issue_id: 4, movement_date: '2024-03-06', movement_type: 'تأجيل جلسة', description: 'تأجيل الجلسة لحين استكمال الأوراق', court_id: 3, employee_id: 4 }
      ];

      for (const movement of movementsData) {
        await client.query(`
          INSERT INTO movements (issue_id, movement_date, movement_type, description, court_id, employee_id)
          VALUES ($1, $2, $3, $4, $5, $6)
        `, [movement.issue_id, movement.movement_date, movement.movement_type, movement.description, movement.court_id, movement.employee_id]);
      }
      console.log(`✅ تم نسخ ${movementsData.length} حركة`);
    }

    // إنشاء بيانات تجريبية للقيود اليومية
    console.log('🔄 جاري إنشاء بيانات القيود اليومية...');
    
    const journalExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'journal_entries'
      )
    `);

    if (journalExists.rows[0].exists) {
      await client.query('TRUNCATE TABLE journal_entries RESTART IDENTITY CASCADE');
      
      const journalData = [
        { entry_number: 'JE2024001', entry_date: '2024-01-15', description: 'قيد استلام أتعاب قضية تجارية', total_debit: 5000, total_credit: 5000, reference_number: 'REF001' },
        { entry_number: 'JE2024002', entry_date: '2024-02-10', description: 'قيد مصروفات قضائية', total_debit: 1500, total_credit: 1500, reference_number: 'REF002' },
        { entry_number: 'JE2024003', entry_date: '2024-01-20', description: 'قيد استلام أتعاب استشارة', total_debit: 3000, total_credit: 3000, reference_number: 'REF003' },
        { entry_number: 'JE2024004', entry_date: '2024-03-05', description: 'قيد مصروفات مكتبية', total_debit: 800, total_credit: 800, reference_number: 'REF004' }
      ];

      for (const entry of journalData) {
        await client.query(`
          INSERT INTO journal_entries (entry_number, entry_date, description, total_debit, total_credit, reference_number)
          VALUES ($1, $2, $3, $4, $5, $6)
        `, [entry.entry_number, entry.entry_date, entry.description, entry.total_debit, entry.total_credit, entry.reference_number]);
      }
      console.log(`✅ تم نسخ ${journalData.length} قيد يومي`);
    }

    // التحقق النهائي من جميع الجداول
    console.log('🔄 جاري التحقق النهائي من جميع الجداول...');
    
    const allTables = [
      'governorates', 'branches', 'issue_types', 'employees', 'clients', 
      'issues', 'users', 'companies', 'lineages', 'services', 
      'opening_balances', 'courts', 'follows', 'movements', 'journal_entries'
    ];

    console.log('📋 ملخص جميع البيانات في قاعدة البيانات:');
    
    for (const table of allTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) FROM ${table}`);
        console.log(`   ✅ ${table}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${table}: غير موجود أو خطأ`);
      }
    }

    console.log('🎉 تم إكمال النسخ النهائي لجميع البيانات بنجاح!');
    console.log('📋 الآن جميع الجداول تحتوي على بيانات حقيقية وليس افتراضية');
    
  } catch (error) {
    console.error('❌ خطأ في النسخ النهائي:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل النسخ النهائي
finalCompleteMigration();
