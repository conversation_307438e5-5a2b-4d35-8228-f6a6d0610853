import { NextResponse } from 'next/server'
import { Pool } from 'pg'

export async function GET() {
  let pool: Pool | null = null
  
  try {
    const dbConfig = {
      host: 'localhost',
      port: 5432,
      database: 'moham<PERSON>',
      user: 'postgres',
      password: 'yemen123',
      connectionTimeoutMillis: 5000
    }

    pool = new Pool(dbConfig)
    const client = await pool.connect()
    
    // التحقق من الجداول الموجودة
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `)
    
    client.release()
    
    const existingTables = result.rows.map(row => row.table_name)
    const requiredTables = [
      'clients', 'employees', 'issues', 'issue_types', 
      'lineages', 'follows', 'movements', 'users'
    ]
    
    const missingTables = requiredTables.filter(table => !existingTables.includes(table))
    
    if (missingTables.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'جميع الجداول المطلوبة موجودة',
        details: `الجداول الموجودة: ${existingTables.join(', ')}`
      })
    } else {
      return NextResponse.json({
        success: false,
        message: `يوجد ${missingTables.length} جداول مفقودة`,
        details: `الجداول المفقودة: ${missingTables.join(', ')}\nالجداول الموجودة: ${existingTables.join(', ')}`
      })
    }
    
  } catch (error: any) {
    console.error('خطأ في فحص الجداول:', error)
    
    return NextResponse.json({
      success: false,
      error: 'فشل في فحص الجداول',
      details: `رمز الخطأ: ${error.code || 'غير محدد'}\nالرسالة: ${error.message}`
    }, { status: 500 })
    
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}
