-- التحديث النهائي لقاعدة البيانات مع جميع العلاقات المطلوبة
-- Database: mohammi

-- ===================================
-- 1. تحديث جدول النسب المالية (lineages)
-- ===================================

-- حذف الأعمدة غير المطلوبة وإعادة هيكلة الجدول
DROP TABLE IF EXISTS lineages CASCADE;

CREATE TABLE lineages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    admin_percentage DECIMAL(5,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);

-- إدراج البيانات النموذجية للنسب المالية
INSERT INTO lineages (name, admin_percentage, created_date) VALUES
('نسب القضايا المدنية', 15.0, '2024-01-01'),
('نسب القضايا التجارية', 20.0, '2024-01-02'),
('نسب القضايا الجنائية', 10.0, '2024-01-03'),
('نسب القضايا العمالية', 25.0, '2024-01-04'),
('نسب القضايا العقارية', 18.0, '2024-01-05'),
('نسب القضايا الإدارية', 12.0, '2024-01-06'),
('نسب القضايا الأسرية', 8.0, '2024-01-07'),
('نسب القضايا الضريبية', 22.0, '2024-01-08');

-- ===================================
-- 2. إنشاء جدول الخدمات مع الربط (services)
-- ===================================

-- إنشاء جدول الخدمات مع ربطه بالنسب المالية والموظفين
DROP TABLE IF EXISTS services CASCADE;

CREATE TABLE services (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
    employee_id INTEGER, -- سيتم ربطه بجدول employees
    created_date DATE DEFAULT CURRENT_DATE
);

-- إدراج البيانات النموذجية للخدمات مع الربط
INSERT INTO services (name, lineage_id, employee_id, created_date) VALUES
('اعداد', 1, 1, '2024-01-01'),
('جلسة', 1, 2, '2024-01-01'),
('متابعة', 2, 3, '2024-01-01'),
('اشراف', 2, 4, '2024-01-01'),
('مصروفات قضائية', 3, 1, '2024-01-01'),
('استشارات قانونية', 4, 2, '2024-01-01'),
('صياغة عقود', 5, 3, '2024-01-01'),
('تمثيل قانوني', 6, 4, '2024-01-01');

-- ===================================
-- 3. تحديث جدول المحاكم (courts)
-- ===================================

-- إضافة الأعمدة الجديدة لجدول المحاكم
ALTER TABLE courts 
ADD COLUMN IF NOT EXISTS employee_id INTEGER,
ADD COLUMN IF NOT EXISTS issue_id INTEGER;

-- تحديث البيانات النموذجية للمحاكم
UPDATE courts SET 
    employee_id = 1,
    issue_id = 1
WHERE id = 1;

UPDATE courts SET 
    employee_id = 2,
    issue_id = 2
WHERE id = 2;

-- ===================================
-- 4. إنشاء جدول توزيع القضايا (case_distribution)
-- ===================================

CREATE TABLE IF NOT EXISTS case_distribution (
    id SERIAL PRIMARY KEY,
    issue_id INTEGER, -- سيتم ربطه بجدول issues
    lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
    admin_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);

-- ===================================
-- 5. إنشاء جدول تفاصيل توزيع الخدمات (service_distributions)
-- ===================================

CREATE TABLE IF NOT EXISTS service_distributions (
    id SERIAL PRIMARY KEY,
    case_distribution_id INTEGER REFERENCES case_distribution(id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
    percentage DECIMAL(5,2) DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    lawyer_id INTEGER, -- سيتم ربطه بجدول employees
    created_date DATE DEFAULT CURRENT_DATE
);

-- ===================================
-- 6. إنشاء الفهارس لتحسين الأداء
-- ===================================

-- فهارس جدول النسب المالية
CREATE INDEX IF NOT EXISTS idx_lineages_name ON lineages(name);
CREATE INDEX IF NOT EXISTS idx_lineages_created_date ON lineages(created_date);

-- فهارس جدول الخدمات
CREATE INDEX IF NOT EXISTS idx_services_name ON services(name);
CREATE INDEX IF NOT EXISTS idx_services_lineage_id ON services(lineage_id);
CREATE INDEX IF NOT EXISTS idx_services_employee_id ON services(employee_id);
CREATE INDEX IF NOT EXISTS idx_services_created_date ON services(created_date);

-- فهارس جدول المحاكم
CREATE INDEX IF NOT EXISTS idx_courts_employee_id ON courts(employee_id);
CREATE INDEX IF NOT EXISTS idx_courts_issue_id ON courts(issue_id);

-- فهارس جدول توزيع القضايا
CREATE INDEX IF NOT EXISTS idx_case_distribution_issue_id ON case_distribution(issue_id);
CREATE INDEX IF NOT EXISTS idx_case_distribution_lineage_id ON case_distribution(lineage_id);
CREATE INDEX IF NOT EXISTS idx_case_distribution_created_date ON case_distribution(created_date);

-- فهارس جدول تفاصيل توزيع الخدمات
CREATE INDEX IF NOT EXISTS idx_service_distributions_case_id ON service_distributions(case_distribution_id);
CREATE INDEX IF NOT EXISTS idx_service_distributions_service_id ON service_distributions(service_id);
CREATE INDEX IF NOT EXISTS idx_service_distributions_lawyer_id ON service_distributions(lawyer_id);

-- ===================================
-- 7. إنشاء Views مفيدة للاستعلامات
-- ===================================

-- View لعرض الخدمات مع تفاصيل النسب والموظفين
CREATE OR REPLACE VIEW services_details AS
SELECT 
    s.id,
    s.name as service_name,
    s.lineage_id,
    l.name as lineage_name,
    l.admin_percentage,
    s.employee_id,
    s.created_date
FROM services s
LEFT JOIN lineages l ON s.lineage_id = l.id;

-- View لعرض المحاكم مع تفاصيل الموظفين والقضايا
CREATE OR REPLACE VIEW courts_details AS
SELECT 
    c.id,
    c.name as court_name,
    c.type,
    c.governorate_id,
    c.address,
    c.phone,
    c.employee_id,
    c.issue_id,
    c.is_active,
    c.created_date
FROM courts c;

-- View لعرض توزيع القضايا مع التفاصيل الكاملة
CREATE OR REPLACE VIEW case_distribution_full AS
SELECT 
    cd.id,
    cd.issue_id,
    cd.lineage_id,
    l.name as lineage_name,
    l.admin_percentage,
    cd.admin_amount,
    cd.remaining_amount,
    cd.created_date
FROM case_distribution cd
LEFT JOIN lineages l ON cd.lineage_id = l.id;

-- ===================================
-- 8. التحقق من النتائج
-- ===================================

-- عرض هيكل جدول النسب المالية
SELECT 'جدول النسب المالية (lineages):' as info;
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'lineages' ORDER BY ordinal_position;

-- عرض بيانات النسب المالية
SELECT 'بيانات النسب المالية:' as info;
SELECT * FROM lineages ORDER BY id;

-- عرض هيكل جدول الخدمات
SELECT 'جدول الخدمات (services):' as info;
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'services' ORDER BY ordinal_position;

-- عرض بيانات الخدمات مع التفاصيل
SELECT 'بيانات الخدمات مع التفاصيل:' as info;
SELECT * FROM services_details ORDER BY id;

-- عرض هيكل جدول المحاكم المحدث
SELECT 'جدول المحاكم المحدث (courts):' as info;
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'courts' ORDER BY ordinal_position;

-- عرض ملخص الجداول
SELECT 'ملخص الجداول:' as info;
SELECT 
    'lineages' as table_name,
    COUNT(*) as record_count
FROM lineages
UNION ALL
SELECT 
    'services' as table_name,
    COUNT(*) as record_count
FROM services
UNION ALL
SELECT 
    'case_distribution' as table_name,
    COUNT(*) as record_count
FROM case_distribution
UNION ALL
SELECT 
    'service_distributions' as table_name,
    COUNT(*) as record_count
FROM service_distributions;

-- عرض العلاقات بين الجداول
SELECT 'العلاقات بين الجداول:' as info;
SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('services', 'case_distribution', 'service_distributions')
ORDER BY tc.table_name;

-- رسالة النجاح
SELECT '✅ تم تحديث قاعدة البيانات بنجاح مع جميع العلاقات المطلوبة!' as result;
