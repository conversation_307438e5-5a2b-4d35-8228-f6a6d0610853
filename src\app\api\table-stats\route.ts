import { NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب إحصائيات الجداول القابلة للربط
export async function GET() {
  try {
    const tables = [
      { name: 'clients', display_name: 'الموكلين' },
      { name: 'employees', display_name: 'الموظفين' },
      { name: 'courts', display_name: 'المحاكم' },
      { name: 'branches', display_name: 'الفروع' },
      { name: 'cost_centers', display_name: 'مراكز التكلفة' },
      { name: 'issues', display_name: 'القضايا' }
    ]

    const stats = []

    for (const table of tables) {
      try {
        const result = await query(`SELECT COUNT(*) as count FROM ${table.name}`)
        stats.push({
          name: table.name,
          display_name: table.display_name,
          count: parseInt(result.rows[0].count)
        })
      } catch (error) {
        console.error(`Error counting ${table.name}:`, error)
        stats.push({
          name: table.name,
          display_name: table.display_name,
          count: 0
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Error fetching table stats:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب إحصائيات الجداول' },
      { status: 500 }
    )
  }
}
