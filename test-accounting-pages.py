import subprocess
import time
import webbrowser
import os
import signal
import sys

def run_server():
    print("جاري تشغيل الخادم...")
    # تشغيل خادم Next.js
    server_process = subprocess.Popen(
        "npm run dev",
        shell=True,
        cwd="e:\\mohammi\\legal-system",
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # انتظار بدء تشغيل الخادم
    time.sleep(10)
    
    # التحقق من أن الخادم يعمل
    if server_process.poll() is not None:
        print("فشل في تشغيل الخادم!")
        return None
    
    print("تم تشغيل الخادم بنجاح!")
    return server_process

def open_pages():
    # قائمة بالصفحات التي نريد تجربتها
    pages = [
        "http://localhost:3000/accounting/chart-of-accounts",  # دليل الحسابات
        "http://localhost:3000/accounting/journal-entries",     # القيود اليومية
        "http://localhost:3000/accounting/payment-voucher",     # سندات الصرف
        "http://localhost:3000/accounting/receipt-voucher",     # سندات القبض
        "http://localhost:3000/accounting/reports"              # التقارير والميزانية
    ]
    
    # فتح كل صفحة في المتصفح
    for page in pages:
        print(f"فتح الصفحة: {page}")
        webbrowser.open(page)
        time.sleep(2)  # انتظار قليلاً بين فتح كل صفحة

def main():
    server_process = run_server()
    if server_process:
        try:
            open_pages()
            
            print("\nالصفحات مفتوحة في المتصفح. اضغط Ctrl+C لإيقاف الخادم عند الانتهاء.")
            # انتظار حتى يضغط المستخدم Ctrl+C
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nإيقاف الخادم...")
        finally:
            # إيقاف الخادم
            if sys.platform == 'win32':
                # على Windows، نحتاج إلى إيقاف العملية بطريقة مختلفة
                subprocess.run("taskkill /F /T /PID " + str(server_process.pid), shell=True)
            else:
                # على Linux/Mac
                os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            print("تم إيقاف الخادم.")

if __name__ == "__main__":
    main()