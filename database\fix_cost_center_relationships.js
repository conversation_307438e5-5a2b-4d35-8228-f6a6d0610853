const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function fixCostCenterRelationships() {
  
  try {
    console.log('🔧 جاري إصلاح علاقات مراكز التكلفة...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. التحقق من الجداول الموجودة
    console.log('\n📋 فحص الجداول الموجودة...');
    
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('vouchers_master', 'financial_transactions', 'cost_centers')
      ORDER BY table_name
    `);
    
    console.log('📊 الجداول الموجودة:');
    tables.rows.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });

    // 2. فحص العلاقات الحالية
    console.log('\n🔗 فحص العلاقات الحالية...');
    
    const constraints = await client.query(`
      SELECT 
        tc.table_name,
        tc.constraint_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage ccu 
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND (tc.table_name IN ('vouchers_master', 'financial_transactions')
           OR ccu.table_name = 'cost_centers')
      ORDER BY tc.table_name, tc.constraint_name
    `);

    console.log('🔗 العلاقات الموجودة:');
    constraints.rows.forEach(rel => {
      console.log(`   ${rel.table_name}.${rel.column_name} → ${rel.foreign_table_name}.${rel.foreign_column_name}`);
    });

    // 3. التحقق من أعمدة cost_center_id
    console.log('\n📊 فحص أعمدة cost_center_id...');
    
    const voucherColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'vouchers_master' AND column_name = 'cost_center_id'
    `);

    const transactionColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'financial_transactions' AND column_name = 'cost_center_id'
    `);

    console.log(`   - vouchers_master.cost_center_id: ${voucherColumns.rows.length > 0 ? 'موجود ❌' : 'غير موجود ✅'}`);
    console.log(`   - financial_transactions.cost_center_id: ${transactionColumns.rows.length > 0 ? 'موجود ✅' : 'غير موجود ❌'}`);

    // 4. إصلاح العلاقات
    console.log('\n🔧 إصلاح العلاقات...');

    // إزالة cost_center_id من vouchers_master إذا كان موجوداً
    if (voucherColumns.rows.length > 0) {
      console.log('   🗑️ إزالة cost_center_id من vouchers_master...');
      
      // إزالة Foreign Key أولاً
      try {
        const voucherConstraints = await client.query(`
          SELECT constraint_name 
          FROM information_schema.table_constraints 
          WHERE table_name = 'vouchers_master' 
          AND constraint_type = 'FOREIGN KEY'
          AND constraint_name LIKE '%cost_center%'
        `);

        for (const constraint of voucherConstraints.rows) {
          await client.query(`ALTER TABLE vouchers_master DROP CONSTRAINT ${constraint.constraint_name}`);
          console.log(`     ✅ تم حذف ${constraint.constraint_name}`);
        }
      } catch (error) {
        console.log(`     ⚠️ خطأ في حذف Foreign Key: ${error.message}`);
      }

      // حذف العمود
      try {
        await client.query(`ALTER TABLE vouchers_master DROP COLUMN cost_center_id`);
        console.log('     ✅ تم حذف عمود cost_center_id من vouchers_master');
      } catch (error) {
        console.log(`     ⚠️ خطأ في حذف العمود: ${error.message}`);
      }
    }

    // إضافة cost_center_id إلى financial_transactions إذا لم يكن موجوداً
    if (transactionColumns.rows.length === 0) {
      console.log('   ➕ إضافة cost_center_id إلى financial_transactions...');
      
      try {
        await client.query(`
          ALTER TABLE financial_transactions 
          ADD COLUMN cost_center_id INTEGER REFERENCES cost_centers(id)
        `);
        console.log('     ✅ تم إضافة عمود cost_center_id إلى financial_transactions');
      } catch (error) {
        console.log(`     ⚠️ خطأ في إضافة العمود: ${error.message}`);
      }
    }

    // 5. التحقق من النتائج
    console.log('\n📊 التحقق من النتائج...');
    
    const finalVoucherColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'vouchers_master' AND column_name = 'cost_center_id'
    `);

    const finalTransactionColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'financial_transactions' AND column_name = 'cost_center_id'
    `);

    console.log('✅ النتائج النهائية:');
    console.log(`   - vouchers_master.cost_center_id: ${finalVoucherColumns.rows.length > 0 ? 'موجود ❌' : 'غير موجود ✅'}`);
    console.log(`   - financial_transactions.cost_center_id: ${finalTransactionColumns.rows.length > 0 ? 'موجود ✅' : 'غير موجود ❌'}`);

    // 6. عرض الهيكل الصحيح
    console.log('\n🏗️ الهيكل الصحيح للعلاقات:');
    console.log('');
    console.log('   cost_centers (مراكز التكلفة)');
    console.log('   ├── id (Primary Key)');
    console.log('   ├── center_code');
    console.log('   └── center_name');
    console.log('');
    console.log('   vouchers_master (السندات الرئيسية)');
    console.log('   ├── id (Primary Key)');
    console.log('   ├── voucher_number');
    console.log('   ├── voucher_date');
    console.log('   └── voucher_type');
    console.log('   └── ❌ بدون cost_center_id');
    console.log('');
    console.log('   financial_transactions (تفاصيل السندات)');
    console.log('   ├── id (Primary Key)');
    console.log('   ├── voucher_id → vouchers_master(id)');
    console.log('   ├── debit_amount');
    console.log('   ├── credit_amount');
    console.log('   └── ✅ cost_center_id → cost_centers(id)');

    // 7. عرض إحصائيات
    const costCenterCount = await client.query('SELECT COUNT(*) as count FROM cost_centers');
    const voucherCount = await client.query('SELECT COUNT(*) as count FROM vouchers_master');
    const transactionCount = await client.query('SELECT COUNT(*) as count FROM financial_transactions');

    console.log('\n📈 إحصائيات النظام:');
    console.log(`   - مراكز التكلفة: ${costCenterCount.rows[0].count}`);
    console.log(`   - السندات: ${voucherCount.rows[0].count}`);
    console.log(`   - تفاصيل السندات: ${transactionCount.rows[0].count}`);

    console.log('\n🎉 تم إصلاح علاقات مراكز التكلفة بنجاح!');
    console.log('\n📋 الآن:');
    console.log('   ✅ مراكز التكلفة مرتبطة بتفاصيل السندات فقط');
    console.log('   ✅ السندات الرئيسية بدون مركز تكلفة');
    console.log('   ✅ كل تفصيل سند يمكن ربطه بمركز تكلفة مختلف');
    console.log('   ✅ مرونة أكبر في التصنيف المحاسبي');

  } catch (error) {
    console.error('❌ خطأ في إصلاح علاقات مراكز التكلفة:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
fixCostCenterRelationships();
