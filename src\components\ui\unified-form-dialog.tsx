'use client'

import { ReactNode } from 'react'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Save, X } from 'lucide-react'

interface UnifiedFormDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  title: string
  trigger?: ReactNode
  children: ReactNode
  onSubmit: (e: React.FormEvent) => void
  onCancel: () => void
  submitText?: string
  cancelText?: string
  isSubmitting?: boolean
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
}

export function UnifiedFormDialog({
  isOpen,
  onOpenChange,
  title,
  trigger,
  children,
  onSubmit,
  onCancel,
  submitText = 'حفظ',
  cancelText = 'إلغاء',
  isSubmitting = false,
  maxWidth = 'md'
}: UnifiedFormDialogProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {trigger && (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      )}
      <DialogContent className={`${maxWidthClasses[maxWidth]} max-h-[90vh] overflow-y-auto`}>
        <DialogHeader>
          <DialogTitle className="text-right">{title}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={onSubmit} className="space-y-6">
          {/* محتوى النموذج */}
          <div className="space-y-4">
            {children}
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex space-x-3 space-x-reverse pt-4 border-t">
            <Button 
              type="submit" 
              className="flex-1 bg-blue-600 hover:bg-blue-700"
              disabled={isSubmitting}
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? 'جاري الحفظ...' : submitText}
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              className="flex-1"
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              {cancelText}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

// مكون حقل النموذج الموحد
interface FormFieldProps {
  label: string
  required?: boolean
  children: ReactNode
  error?: string
}

export function FormField({ label, required = false, children, error }: FormFieldProps) {
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 mr-1">*</span>}
      </label>
      {children}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

// مكون قائمة منسدلة موحدة
interface SelectOption {
  value: string | number
  label: string
}

interface UnifiedSelectProps {
  value: string | number
  onChange: (value: string | number) => void
  options: SelectOption[]
  placeholder?: string
  disabled?: boolean
}

export function UnifiedSelect({ 
  value, 
  onChange, 
  options, 
  placeholder = 'اختر...', 
  disabled = false 
}: UnifiedSelectProps) {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
    >
      <option value="">{placeholder}</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  )
}

// مكون checkbox موحد
interface UnifiedCheckboxProps {
  checked: boolean
  onChange: (checked: boolean) => void
  label: string
  disabled?: boolean
}

export function UnifiedCheckbox({ 
  checked, 
  onChange, 
  label, 
  disabled = false 
}: UnifiedCheckboxProps) {
  return (
    <div className="flex items-center space-x-2 space-x-reverse">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:cursor-not-allowed"
      />
      <label className="text-sm text-gray-700">
        {label}
      </label>
    </div>
  )
}

// مكون textarea موحد
interface UnifiedTextareaProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  rows?: number
  disabled?: boolean
}

export function UnifiedTextarea({ 
  value, 
  onChange, 
  placeholder, 
  rows = 3, 
  disabled = false 
}: UnifiedTextareaProps) {
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      rows={rows}
      disabled={disabled}
      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed resize-vertical"
    />
  )
}
