'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Settings, Database, CheckCircle, AlertCircle } from 'lucide-react'

export default function SetupPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<string[]>([])

  const createVouchersTables = async () => {
    setLoading(true)
    setResults([])
    
    try {
      const response = await fetch('/api/setup/vouchers-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        setResults(prev => [...prev, '✅ تم إنشاء جداول السندات بنجاح'])
      } else {
        setResults(prev => [...prev, `❌ فشل في إنشاء جداول السندات: ${result.error}`])
      }
    } catch (error) {
      setResults(prev => [...prev, `❌ خطأ في الاتصال: ${error}`])
    } finally {
      setLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Settings className="h-8 w-8 mr-3 text-blue-600" />
              إعداد النظام
            </h1>
            <p className="text-gray-600 mt-1">إعداد قاعدة البيانات والجداول المطلوبة</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              إعداد جداول السندات المالية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-600">
                انقر على الزر أدناه لإنشاء الجداول المطلوبة لنظام السندات المالية:
              </p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>جدول السندات الرئيسي (vouchers_master)</li>
                <li>جدول المعاملات المالية (financial_transactions)</li>
                <li>الفهارس والقيود المطلوبة</li>
              </ul>
              
              <Button 
                onClick={createVouchersTables} 
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? 'جاري الإنشاء...' : 'إنشاء جداول السندات'}
              </Button>

              {results.length > 0 && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-2">نتائج العملية:</h4>
                  <div className="space-y-1">
                    {results.map((result, index) => (
                      <div key={index} className="text-sm">
                        {result}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
