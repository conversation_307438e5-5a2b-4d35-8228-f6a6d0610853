import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // جلب المدفوعات مع معلومات العملاء
    const result = await query(`
      SELECT 
        p.*,
        e.name as entity_name,
        c.name as client_name,
        SUM(CASE WHEN pl.type = 1 THEN at.amount ELSE 0 END) as total_amount
      FROM payment p
      LEFT JOIN entity e ON p.entity_id = e.id
      LEFT JOIN company c ON e.id = c.entity_id
      LEFT JOIN payment_links pl ON p.id = pl.payment_id
      LEFT JOIN acc_trans at ON pl.entry_id = at.entry_id
      GROUP BY p.id, e.name, c.name
      ORDER BY p.payment_date DESC, p.id DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset])

    // عدد المدفوعات الإجمالي
    const countResult = await query('SELECT COUNT(*) as total FROM payment')
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      data: result.rows.map(row => ({
        ...row,
        amount: parseFloat(row.total_amount || 0)
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب المدفوعات' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      reference,
      payment_date,
      payment_class,
      entity_id,
      currency = 'SAR',
      amount,
      notes,
      employee_id,
      department_id
    } = body

    // التحقق من البيانات المطلوبة
    if (!reference || !payment_date || !payment_class || !amount) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة ناقصة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم المرجع
    const existingPayment = await query(
      'SELECT id FROM payment WHERE reference = $1',
      [reference]
    )

    if (existingPayment.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم المرجع موجود مسبقاً' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      // إنشاء قيد محاسبي للمدفوعة
      const glResult = await query(`
        INSERT INTO gl (reference, description, transdate, notes, approved)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
      `, [
        reference, 
        `${payment_class === 1 ? 'مقبوضات' : 'مدفوعات'} - ${reference}`,
        payment_date,
        notes,
        false
      ])

      const glId = glResult.rows[0].id

      // إنشاء المدفوعة
      const paymentResult = await query(`
        INSERT INTO payment (
          reference, payment_date, payment_class, entity_id, 
          currency, notes, employee_id, department_id, gl_id, approved
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `, [
        reference, payment_date, payment_class, entity_id,
        currency, notes, employee_id, department_id, glId, false
      ])

      const paymentId = paymentResult.rows[0].id

      // إنشاء القيود المحاسبية
      // حساب النقدية (1110) والعميل أو المورد
      const cashAccountId = await query(
        "SELECT id FROM chart_of_accounts WHERE accno = '1110'"
      )

      const clientAccountId = await query(
        "SELECT id FROM chart_of_accounts WHERE accno = '1210'"
      )

      if (cashAccountId.rows.length > 0 && clientAccountId.rows.length > 0) {
        if (payment_class === 1) {
          // مقبوضات: مدين النقدية، دائن العميل
          const debitEntry = await query(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `, [glId, cashAccountId.rows[0].id, amount, payment_date, `مقبوضات من العميل - ${reference}`, false])

          const creditEntry = await query(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `, [glId, clientAccountId.rows[0].id, -amount, payment_date, `مقبوضات من العميل - ${reference}`, false])

          // ربط المدفوعة بالقيود
          await query(`
            INSERT INTO payment_links (payment_id, entry_id, type)
            VALUES ($1, $2, 1), ($1, $3, 2)
          `, [paymentId, debitEntry.rows[0].entry_id, creditEntry.rows[0].entry_id])
        } else {
          // مدفوعات: دائن النقدية، مدين المورد
          const creditEntry = await query(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `, [glId, cashAccountId.rows[0].id, -amount, payment_date, `مدفوعات للمورد - ${reference}`, false])

          const debitEntry = await query(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `, [glId, clientAccountId.rows[0].id, amount, payment_date, `مدفوعات للمورد - ${reference}`, false])

          // ربط المدفوعة بالقيود
          await query(`
            INSERT INTO payment_links (payment_id, entry_id, type)
            VALUES ($1, $2, 1), ($1, $3, 2)
          `, [paymentId, debitEntry.rows[0].entry_id, creditEntry.rows[0].entry_id])
        }
      }

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم إنشاء المدفوعة بنجاح',
        data: paymentResult.rows[0]
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء المدفوعة' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      reference,
      payment_date,
      payment_class,
      entity_id,
      currency,
      notes,
      approved
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المدفوعة مطلوب' },
        { status: 400 }
      )
    }

    // تحديث المدفوعة
    const result = await query(`
      UPDATE payment 
      SET 
        reference = COALESCE($2, reference),
        payment_date = COALESCE($3, payment_date),
        payment_class = COALESCE($4, payment_class),
        entity_id = COALESCE($5, entity_id),
        currency = COALESCE($6, currency),
        notes = COALESCE($7, notes),
        approved = COALESCE($8, approved)
      WHERE id = $1
      RETURNING *
    `, [id, reference, payment_date, payment_class, entity_id, currency, notes, approved])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المدفوعة غير موجودة' },
        { status: 404 }
      )
    }

    // إذا تم اعتماد المدفوعة، اعتمد القيود المرتبطة
    if (approved) {
      await query(`
        UPDATE gl 
        SET approved = true, approved_at = CURRENT_TIMESTAMP 
        WHERE id = (SELECT gl_id FROM payment WHERE id = $1)
      `, [id])

      await query(`
        UPDATE acc_trans 
        SET approved = true 
        WHERE trans_id = (SELECT gl_id FROM payment WHERE id = $1)
      `, [id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المدفوعة بنجاح',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating payment:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المدفوعة' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المدفوعة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من أن المدفوعة غير معتمدة
    const paymentCheck = await query(
      'SELECT approved, gl_id FROM payment WHERE id = $1',
      [id]
    )

    if (paymentCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المدفوعة غير موجودة' },
        { status: 404 }
      )
    }

    if (paymentCheck.rows[0].approved) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف مدفوعة معتمدة' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      const glId = paymentCheck.rows[0].gl_id

      // حذف روابط المدفوعة
      await query('DELETE FROM payment_links WHERE payment_id = $1', [id])
      
      // حذف القيود المحاسبية
      if (glId) {
        await query('DELETE FROM acc_trans WHERE trans_id = $1', [glId])
        await query('DELETE FROM gl WHERE id = $1', [glId])
      }
      
      // حذف المدفوعة
      await query('DELETE FROM payment WHERE id = $1', [id])

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حذف المدفوعة بنجاح'
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error deleting payment:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المدفوعة' },
      { status: 500 }
    )
  }
}
