import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// POST - تسجيل دخول العملاء
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, password } = body

    // التحقق من البيانات المطلوبة
    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // البحث عن العميل
    const clientResult = await query(`
      SELECT * FROM clients
      WHERE username = $1
    `, [username])

    if (clientResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    const client = clientResult.rows[0]

    // التحقق من كلمة المرور (مبسط للاختبار)
    console.log('🔐 التحقق من كلمة المرور للعميل:', username)
    console.log('📋 كلمة المرور المدخلة:', password)
    console.log('📋 كلمة المرور المحفوظة:', client.password_hash)

    // تحقق مبسط: كلمة المرور = آخر 4 أرقام من اسم المستخدم
    const expectedPassword = username.split('_')[1] || password
    const isPasswordValid = password === expectedPassword || password === client.password_hash

    console.log('✅ نتيجة التحقق:', isPasswordValid)

    if (!isPasswordValid) {
      // تسجيل محاولة دخول فاشلة
      await query(`
        UPDATE clients
        SET login_attempts = login_attempts + 1
        WHERE id = $1
      `, [client.id])

      return NextResponse.json(
        { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    // التحقق من حالة العميل
    if (client.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'حساب العميل غير نشط' },
        { status: 403 }
      )
    }

    // التحقق من عدد محاولات الدخول الفاشلة
    if (client.login_attempts >= 5) {
      // قفل الحساب لمدة 30 دقيقة
      await query(`
        UPDATE clients
        SET locked_until = CURRENT_TIMESTAMP + INTERVAL '30 minutes'
        WHERE id = $1
      `, [client.id])

      return NextResponse.json(
        { success: false, error: 'تم قفل الحساب مؤقتاً بسبب محاولات دخول فاشلة متعددة' },
        { status: 423 }
      )
    }

    // التحقق من قفل الحساب
    if (client.locked_until && new Date(client.locked_until) > new Date()) {
      return NextResponse.json(
        { success: false, error: 'الحساب مقفل مؤقتاً، يرجى المحاولة لاحقاً' },
        { status: 423 }
      )
    }

    // تحديث بيانات تسجيل الدخول
    await query(`
      UPDATE clients
      SET last_login = CURRENT_TIMESTAMP,
          is_online = true,
          login_attempts = 0,
          locked_until = NULL
      WHERE id = $1
    `, [client.id])

    // إنشاء JWT token
    const token = jwt.sign(
      {
        clientId: client.id,
        username: client.username,
        type: 'client'
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    // إرجاع بيانات العميل (بدون كلمة المرور)
    const { password_hash, ...clientWithoutPassword } = client

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: clientWithoutPassword,
      token
    })

  } catch (error) {
    console.error('Error during client login:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تسجيل الدخول' },
      { status: 500 }
    )
  }
}

// GET - التحقق من حالة تسجيل الدخول للعميل
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, JWT_SECRET) as any

    // جلب بيانات العميل المحدثة
    const clientResult = await query(`
      SELECT * FROM clients
      WHERE id = $1 AND status = 'active'
    `, [decoded.clientId])

    if (clientResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العميل غير موجود أو غير نشط' },
        { status: 401 }
      )
    }

    const client = clientResult.rows[0]
    const { password_hash, ...clientWithoutPassword } = client

    return NextResponse.json({
      success: true,
      user: clientWithoutPassword
    })

  } catch (error) {
    console.error('Error verifying client token:', error)
    return NextResponse.json(
      { success: false, error: 'رمز المصادقة غير صالح' },
      { status: 401 }
    )
  }
}

// DELETE - تسجيل الخروج للعميل
export async function DELETE(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, JWT_SECRET) as any

    // تحديث حالة العميل إلى غير متصل
    await query(`
      UPDATE clients
      SET is_online = false
      WHERE id = $1
    `, [decoded.clientId])

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    })

  } catch (error) {
    console.error('Error during client logout:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تسجيل الخروج' },
      { status: 500 }
    )
  }
}
