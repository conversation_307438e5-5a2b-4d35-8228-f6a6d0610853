const { exec } = require('child_process');

console.log('🔄 إنهاء العمليات المشغولة للمنفذ 7443 وإعادة التشغيل');
console.log('⏰ الوقت:', new Date().toLocaleString('ar-YE'));
console.log('');

async function main() {
  console.log('🔍 البحث عن العمليات المشغولة للمنفذ 7443...');
  
  exec('netstat -ano | findstr :7443', (error, stdout, stderr) => {
    if (stdout) {
      console.log('📋 العمليات الموجودة:');
      console.log(stdout);
      
      // استخراج PIDs
      const lines = stdout.split('\n').filter(line => line.includes(':7443'));
      const pids = new Set();
      
      lines.forEach(line => {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 5) {
          const pid = parts[parts.length - 1];
          if (pid && pid !== '0' && !isNaN(pid)) {
            pids.add(pid);
          }
        }
      });
      
      if (pids.size > 0) {
        console.log('🔄 إنهاء العمليات:', Array.from(pids).join(', '));
        
        // إنهاء كل PID
        pids.forEach(pid => {
          exec(`taskkill /PID ${pid} /F`, (killError, killStdout, killStderr) => {
            if (killError) {
              console.log(`❌ فشل في إنهاء العملية ${pid}: ${killError.message}`);
            } else {
              console.log(`✅ تم إنهاء العملية ${pid}`);
            }
          });
        });
        
        // انتظار قليل ثم تشغيل Next.js
        setTimeout(() => {
          console.log('');
          console.log('🚀 تشغيل Next.js على المنفذ 7443...');
          
          const nextProcess = exec('npx next dev -p 7443', { cwd: process.cwd() });
          
          nextProcess.stdout.on('data', (data) => {
            console.log(data.toString());
          });
          
          nextProcess.stderr.on('data', (data) => {
            console.log(data.toString());
          });
          
          nextProcess.on('close', (code) => {
            console.log(`🛑 Next.js توقف برمز: ${code}`);
          });
          
        }, 2000);
        
      } else {
        startNextJS();
      }
    } else {
      startNextJS();
    }
  });
}

function startNextJS() {
  console.log('ℹ️ لم يتم العثور على عمليات للإنهاء');
  
  console.log('');
  console.log('🚀 تشغيل Next.js على المنفذ 7443...');
  
  const nextProcess = exec('npx next dev -p 7443', { cwd: process.cwd() });
  
  nextProcess.stdout.on('data', (data) => {
    console.log(data.toString());
  });
  
  nextProcess.stderr.on('data', (data) => {
    console.log(data.toString());
  });
  
  nextProcess.on('close', (code) => {
    console.log(`🛑 Next.js توقف برمز: ${code}`);
  });
}

main().catch(console.error);
