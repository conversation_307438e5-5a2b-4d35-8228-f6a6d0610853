// تحديث هيكل الجداول حسب المتطلبات الجديدة
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function updateTablesStructure() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. تحديث جدول الموظفين
    console.log('🔄 جاري تحديث جدول الموظفين...');
    
    // إضافة الأعمدة الجديدة للموظفين
    const employeeColumns = [
      'ALTER TABLE employees ADD COLUMN IF NOT EXISTS department_id INTEGER REFERENCES courts(id)',
      'ALTER TABLE employees ADD COLUMN IF NOT EXISTS branch_id INTEGER REFERENCES branches(id) DEFAULT 1',
      'ALTER TABLE employees ADD COLUMN IF NOT EXISTS governorate_id INTEGER REFERENCES governorates(id) DEFAULT 1',
      'ALTER TABLE employees ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT \'نشط\'',
      'ALTER TABLE employees ADD COLUMN IF NOT EXISTS employee_number VARCHAR(50)'
    ];

    for (const sql of employeeColumns) {
      try {
        await client.query(sql);
      } catch (error) {
        console.log(`تم تخطي: ${error.message}`);
      }
    }

    // تحديث بيانات الموظفين مع الوظائف الجديدة
    await client.query('TRUNCATE TABLE employees RESTART IDENTITY CASCADE');
    
    const employeesData = [
      { 
        name: 'ماجد أحمد علي', 
        position: 'مدير عام', 
        phone: '777123456', 
        email: '<EMAIL>', 
        salary: 250000, 
        hire_date: '2020-01-15',
        department_id: 1,
        branch_id: 1,
        governorate_id: 1,
        status: 'نشط',
        employee_number: 'EMP001'
      },
      { 
        name: 'يحيى علي محمد', 
        position: 'محامي رسمي', 
        phone: '777234567', 
        email: '<EMAIL>', 
        salary: 180000, 
        hire_date: '2021-03-10',
        department_id: 2,
        branch_id: 1,
        governorate_id: 1,
        status: 'نشط',
        employee_number: 'EMP002'
      },
      { 
        name: 'أحمد صالح حسن', 
        position: 'استشاري', 
        phone: '777345678', 
        email: '<EMAIL>', 
        salary: 200000, 
        hire_date: '2022-06-20',
        department_id: 3,
        branch_id: 2,
        governorate_id: 2,
        status: 'نشط',
        employee_number: 'EMP003'
      },
      { 
        name: 'محمد صالح عبدالله', 
        position: 'محاسب', 
        phone: '777456789', 
        email: '<EMAIL>', 
        salary: 120000, 
        hire_date: '2023-01-05',
        department_id: 4,
        branch_id: 1,
        governorate_id: 1,
        status: 'نشط',
        employee_number: 'EMP004'
      },
      { 
        name: 'فاطمة علي أحمد', 
        position: 'سكرتارية', 
        phone: '777567890', 
        email: '<EMAIL>', 
        salary: 80000, 
        hire_date: '2023-05-15',
        department_id: 1,
        branch_id: 1,
        governorate_id: 1,
        status: 'نشط',
        employee_number: 'EMP005'
      }
    ];

    for (const emp of employeesData) {
      await client.query(`
        INSERT INTO employees (name, position, phone, email, salary, hire_date, department_id, branch_id, governorate_id, status, employee_number)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `, [emp.name, emp.position, emp.phone, emp.email, emp.salary, emp.hire_date, emp.department_id, emp.branch_id, emp.governorate_id, emp.status, emp.employee_number]);
    }
    console.log(`✅ تم تحديث ${employeesData.length} موظف`);

    // 2. تحديث جدول المستخدمين
    console.log('🔄 جاري تحديث جدول المستخدمين...');
    
    // إضافة الأعمدة الجديدة للمستخدمين
    const userColumns = [
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS device_id VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP',
      'ALTER TABLE users DROP COLUMN IF EXISTS role'
    ];

    for (const sql of userColumns) {
      try {
        await client.query(sql);
      } catch (error) {
        console.log(`تم تخطي: ${error.message}`);
      }
    }

    // تحديث بيانات المستخدمين
    await client.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE');
    
    const usersData = [
      { 
        username: 'admin', 
        full_name: 'مدير النظام', 
        employee_id: 1, 
        password_hash: 'hashed_password_123',
        device_id: 'DEVICE_001',
        last_login: '2024-01-15 10:30:00',
        is_active: true 
      },
      { 
        username: 'majed.ahmed', 
        full_name: 'ماجد أحمد علي', 
        employee_id: 1, 
        password_hash: 'hashed_password_456',
        device_id: 'DEVICE_002',
        last_login: '2024-01-14 09:15:00',
        is_active: true 
      },
      { 
        username: 'yahya.ali', 
        full_name: 'يحيى علي محمد', 
        employee_id: 2, 
        password_hash: 'hashed_password_789',
        device_id: 'DEVICE_003',
        last_login: '2024-01-13 14:20:00',
        is_active: true 
      },
      { 
        username: 'ahmed.saleh', 
        full_name: 'أحمد صالح حسن', 
        employee_id: 3, 
        password_hash: 'hashed_password_101',
        device_id: 'DEVICE_004',
        last_login: '2024-01-12 11:45:00',
        is_active: true 
      }
    ];

    for (const user of usersData) {
      await client.query(`
        INSERT INTO users (username, full_name, employee_id, password_hash, device_id, last_login, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [user.username, user.full_name, user.employee_id, user.password_hash, user.device_id, user.last_login, user.is_active]);
    }
    console.log(`✅ تم تحديث ${usersData.length} مستخدم`);

    // 3. تحديث جدول الموكلين
    console.log('🔄 جاري تحديث جدول الموكلين...');
    
    // إضافة الأعمدة الجديدة للموكلين
    const clientColumns = [
      'ALTER TABLE clients ADD COLUMN IF NOT EXISTS username VARCHAR(100)',
      'ALTER TABLE clients ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255)',
      'ALTER TABLE clients ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT \'نشط\'',
      'ALTER TABLE clients ADD COLUMN IF NOT EXISTS client_number VARCHAR(50)'
    ];

    for (const sql of clientColumns) {
      try {
        await client.query(sql);
      } catch (error) {
        console.log(`تم تخطي: ${error.message}`);
      }
    }

    // تحديث بيانات الموكلين
    await client.query('TRUNCATE TABLE clients RESTART IDENTITY CASCADE');
    
    const clientsData = [
      { 
        name: 'أحمد محمد سالم', 
        phone: '777111222', 
        email: '<EMAIL>', 
        address: 'صنعاء - شارع الستين', 
        id_number: '12345678901', 
        client_type: 'فرد',
        username: 'ahmed.salem',
        password_hash: 'client_password_123',
        status: 'نشط',
        client_number: 'CLI001'
      },
      { 
        name: 'شركة النور للتجارة', 
        phone: '*********', 
        email: '<EMAIL>', 
        address: 'عدن - كريتر', 
        id_number: '98765432109', 
        client_type: 'شركة',
        username: 'alnoor.company',
        password_hash: 'client_password_456',
        status: 'نشط',
        client_number: 'CLI002'
      },
      { 
        name: 'فاطمة علي أحمد', 
        phone: '*********', 
        email: '<EMAIL>', 
        address: 'تعز - شارع جمال', 
        id_number: '11122233344', 
        client_type: 'فرد',
        username: 'fatima.ali',
        password_hash: 'client_password_789',
        status: 'نشط',
        client_number: 'CLI003'
      },
      { 
        name: 'مؤسسة الأمل', 
        phone: '*********', 
        email: '<EMAIL>', 
        address: 'الحديدة - الكورنيش', 
        id_number: '55566677788', 
        client_type: 'مؤسسة',
        username: 'alamal.org',
        password_hash: 'client_password_101',
        status: 'نشط',
        client_number: 'CLI004'
      }
    ];

    for (const client_data of clientsData) {
      await client.query(`
        INSERT INTO clients (name, phone, email, address, id_number, client_type, username, password_hash, status, client_number)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [client_data.name, client_data.phone, client_data.email, client_data.address, client_data.id_number, client_data.client_type, client_data.username, client_data.password_hash, client_data.status, client_data.client_number]);
    }
    console.log(`✅ تم تحديث ${clientsData.length} موكل`);

    // 4. إصلاح جدول القضايا
    console.log('🔄 جاري إصلاح جدول القضايا...');
    
    // تحديث بيانات القضايا مع إصلاح المشاكل
    await client.query('TRUNCATE TABLE issues RESTART IDENTITY CASCADE');
    
    const issuesData = [
      { 
        title: 'قضية تجارية - شركة الأمل', 
        case_number: 'C2024001', 
        client_id: 2, 
        issue_type_id: 1, 
        court_id: 1, 
        case_amount: 500000.00, 
        status: 'جارية', 
        start_date: '2024-01-15',
        description: 'نزاع تجاري حول عقد توريد'
      },
      { 
        title: 'قضية عقارية - النزاع العقاري', 
        case_number: 'C2024002', 
        client_id: 1, 
        issue_type_id: 1, 
        court_id: 2, 
        case_amount: 750000.00, 
        status: 'جارية', 
        start_date: '2024-02-10',
        description: 'نزاع حول ملكية عقار'
      },
      { 
        title: 'قضية أحوال شخصية - طلاق', 
        case_number: 'C2024003', 
        client_id: 3, 
        issue_type_id: 3, 
        court_id: 4, 
        case_amount: 50000.00, 
        status: 'مكتملة', 
        start_date: '2024-01-20',
        description: 'دعوى طلاق للضرر'
      },
      { 
        title: 'قضية عمالية - حقوق عامل', 
        case_number: 'C2024004', 
        client_id: 4, 
        issue_type_id: 4, 
        court_id: 3, 
        case_amount: 100000.00, 
        status: 'جارية', 
        start_date: '2024-03-05',
        description: 'مطالبة بحقوق عمالية'
      }
    ];

    for (const issue of issuesData) {
      await client.query(`
        INSERT INTO issues (title, case_number, client_id, issue_type_id, court_id, case_amount, status, start_date, description)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [issue.title, issue.case_number, issue.client_id, issue.issue_type_id, issue.court_id, issue.case_amount, issue.status, issue.start_date, issue.description]);
    }
    console.log(`✅ تم تحديث ${issuesData.length} قضية`);

    // التحقق من النتائج
    console.log('🔄 جاري التحقق من النتائج...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues')
    ]);

    console.log('📋 ملخص التحديثات:');
    console.log(`   ✅ الموظفين: ${results[0].rows[0].count} سجل`);
    console.log(`   ✅ المستخدمين: ${results[1].rows[0].count} سجل`);
    console.log(`   ✅ الموكلين: ${results[2].rows[0].count} سجل`);
    console.log(`   ✅ القضايا: ${results[3].rows[0].count} سجل`);

    console.log('🎉 تم تحديث هيكل الجداول بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في تحديث الجداول:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
updateTablesStructure();
