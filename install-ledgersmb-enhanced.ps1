# Enhanced LedgerSMB Installation Script
# تحسين سكريبت تثبيت LedgerSMB

Write-Host "🚀 بدء تثبيت LedgerSMB..." -ForegroundColor Green

# 1. التحقق من Chocolatey
Write-Host "📦 التحقق من Chocolatey..." -ForegroundColor Yellow
if (-not (Test-Path "$env:ProgramData\chocolatey\choco.exe")) {
    Write-Host "تثبيت Chocolatey..." -ForegroundColor Cyan
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Host "✅ تم تثبيت Chocolatey بنجاح" -ForegroundColor Green
    } catch {
        Write-Host "❌ فشل في تثبيت Chocolatey: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Chocolatey مثبت مسبقاً" -ForegroundColor Green
}

# إضافة Chocolatey للمسار
$env:Path += ";$env:ProgramData\chocolatey\bin"

# 2. تثبيت Git إذا لم يكن موجوداً
Write-Host "🔧 التحقق من Git..." -ForegroundColor Yellow
try {
    git --version | Out-Null
    Write-Host "✅ Git موجود" -ForegroundColor Green
} catch {
    Write-Host "تثبيت Git..." -ForegroundColor Cyan
    choco install git -y
    # تحديث المسار
    $env:Path += ";C:\Program Files\Git\bin"
}

# 3. تثبيت PostgreSQL
Write-Host "🐘 تثبيت PostgreSQL..." -ForegroundColor Yellow
try {
    choco install postgresql --version=15.8.0 -y
    Write-Host "✅ تم تثبيت PostgreSQL" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: مشكلة في تثبيت PostgreSQL" -ForegroundColor Yellow
}

# 4. تثبيت Strawberry Perl
Write-Host "🍓 تثبيت Strawberry Perl..." -ForegroundColor Yellow
try {
    choco install strawberryperl -y
    Write-Host "✅ تم تثبيت Strawberry Perl" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: مشكلة في تثبيت Strawberry Perl" -ForegroundColor Yellow
}

# 5. تحديث متغيرات البيئة
Write-Host "🔄 تحديث متغيرات البيئة..." -ForegroundColor Yellow
$machinePath = [Microsoft.Win32.Registry]::GetValue('HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment','Path', $null)
$userPath = [Microsoft.Win32.Registry]::GetValue('HKEY_CURRENT_USER\Environment','Path', $null)
$env:Path = "$machinePath;$userPath"
$env:Path += ";C:\ProgramData\chocolatey\bin;C:\Strawberry\perl\bin;C:\Strawberry\c\bin"

# 6. تثبيت cpanm
Write-Host "📦 تثبيت cpanm..." -ForegroundColor Yellow
try {
    perl -MCPAN -e "install App::cpanminus"
    Write-Host "✅ تم تثبيت cpanm" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: مشكلة في تثبيت cpanm" -ForegroundColor Yellow
}

# 7. إنشاء مجلد للمشروع
$projectDir = "E:\mohammi\ledgersmb-system"
Write-Host "📁 إنشاء مجلد المشروع: $projectDir" -ForegroundColor Yellow
if (-not (Test-Path $projectDir)) {
    New-Item -ItemType Directory -Path $projectDir -Force
}
Set-Location $projectDir

# 8. تحميل LedgerSMB
Write-Host "⬇️ تحميل LedgerSMB..." -ForegroundColor Yellow
if (Test-Path "LedgerSMB") {
    Write-Host "مجلد LedgerSMB موجود، سيتم حذفه وإعادة التحميل..." -ForegroundColor Cyan
    Remove-Item -Recurse -Force "LedgerSMB"
}

try {
    git clone https://github.com/ledgersmb/LedgerSMB.git
    Write-Host "✅ تم تحميل LedgerSMB بنجاح" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في تحميل LedgerSMB: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Set-Location "LedgerSMB"

# 9. تثبيت التبعيات
Write-Host "📦 تثبيت تبعيات Perl..." -ForegroundColor Yellow
try {
    cpanm --installdeps . --notest
    Write-Host "✅ تم تثبيت التبعيات" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: مشكلة في تثبيت بعض التبعيات" -ForegroundColor Yellow
}

# 10. إنشاء ملف الإعداد
Write-Host "⚙️ إنشاء ملف الإعداد..." -ForegroundColor Yellow
if (Test-Path "conf/ledgersmb.conf.default") {
    Copy-Item "conf/ledgersmb.conf.default" "ledgersmb.conf"
    Write-Host "✅ تم إنشاء ملف الإعداد" -ForegroundColor Green
} else {
    Write-Host "⚠️ ملف الإعداد الافتراضي غير موجود" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 تم الانتهاء من التثبيت!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 الخطوات التالية:" -ForegroundColor Cyan
Write-Host "1. إعداد قاعدة بيانات PostgreSQL" -ForegroundColor White
Write-Host "2. تحرير ملف ledgersmb.conf" -ForegroundColor White
Write-Host "3. تشغيل الخادم: plackup -Ilib -r" -ForegroundColor White
Write-Host ""
Write-Host "📍 مسار التثبيت: $projectDir\LedgerSMB" -ForegroundColor Yellow
