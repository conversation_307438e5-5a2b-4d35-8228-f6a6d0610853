'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Building2, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface CostCenter {
  id: number
  center_code: string
  center_name: string
  parent_id?: number
  center_level: number
  is_active: boolean
  description?: string
  created_date: string
  updated_date: string
  parent_name?: string
}

export default function CostCentersPage() {
  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [loading, setLoading] = useState(true)
  const [showDialog, setShowDialog] = useState(false)
  const [editingCenter, setEditingCenter] = useState<CostCenter | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  const [formData, setFormData] = useState({
    center_code: '',
    center_name: '',
    parent_id: '',
    description: '',
    is_active: true
  })

  // جلب مراكز التكلفة
  const fetchCostCenters = async () => {
    try {
      const response = await fetch('/api/cost-centers')
      if (response.ok) {
        const data = await response.json()
        setCostCenters(data.centers || [])
      } else {
        console.error('فشل في جلب مراكز التكلفة')
      }
    } catch (error) {
      console.error('خطأ في جلب مراكز التكلفة:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCostCenters()
  }, [])

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      center_code: '',
      center_name: '',
      parent_id: '',
      description: '',
      is_active: true
    })
    setEditingCenter(null)
  }

  // فتح نموذج الإضافة
  const handleAdd = () => {
    resetForm()
    setShowDialog(true)
  }

  // فتح نموذج التعديل
  const handleEdit = (center: CostCenter) => {
    setFormData({
      center_code: center.center_code,
      center_name: center.center_name,
      parent_id: center.parent_id?.toString() || '',
      description: center.description || '',
      is_active: center.is_active
    })
    setEditingCenter(center)
    setShowDialog(true)
  }

  // حفظ مركز التكلفة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = editingCenter ? '/api/cost-centers' : '/api/cost-centers'
      const method = editingCenter ? 'PUT' : 'POST'
      const body = editingCenter 
        ? { ...formData, id: editingCenter.id }
        : formData

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      const result = await response.json()

      if (result.success) {
        alert(result.message)
        setShowDialog(false)
        resetForm()
        fetchCostCenters()
      } else {
        alert(result.error)
      }
    } catch (error) {
      console.error('خطأ في حفظ مركز التكلفة:', error)
      alert('حدث خطأ أثناء حفظ مركز التكلفة')
    }
  }

  // حذف مركز التكلفة
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف مركز التكلفة؟')) return

    try {
      const response = await fetch(`/api/cost-centers?id=${id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        alert(result.message)
        fetchCostCenters()
      } else {
        alert(result.error)
      }
    } catch (error) {
      console.error('خطأ في حذف مركز التكلفة:', error)
      alert('حدث خطأ أثناء حذف مركز التكلفة')
    }
  }

  // تصفية مراكز التكلفة
  const filteredCenters = costCenters.filter(center =>
    center.center_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    center.center_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // الحصول على المراكز الرئيسية للاختيار كمراكز أب
  const parentCenters = costCenters.filter(center => center.center_level < 3)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل مراكز التكلفة...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Link href="/settings" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة إلى الإعدادات
          </Link>
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Building2 className="h-8 w-8 mr-3 text-blue-600" />
                مراكز التكلفة
              </h1>
              <p className="text-gray-600 mt-2">إدارة مراكز التكلفة والأقسام</p>
            </div>
            <Button onClick={handleAdd} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              إضافة مركز تكلفة
            </Button>
          </div>
        </div>

        {/* البحث */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="البحث في مراكز التكلفة..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول مراكز التكلفة */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة مراكز التكلفة ({filteredCenters.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredCenters.length === 0 ? (
              <div className="text-center py-12">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد مراكز تكلفة</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">الرمز</th>
                      <th className="text-right p-3 font-semibold">اسم المركز</th>
                      <th className="text-right p-3 font-semibold">المركز الأب</th>
                      <th className="text-center p-3 font-semibold">المستوى</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-right p-3 font-semibold">الوصف</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCenters.map((center) => (
                      <tr key={center.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-mono text-blue-600">{center.center_code}</td>
                        <td className="p-3 font-medium">
                          {'  '.repeat(center.center_level - 1)}
                          {center.center_name}
                        </td>
                        <td className="p-3 text-gray-600">{center.parent_name || '-'}</td>
                        <td className="p-3 text-center">
                          <Badge variant="outline">المستوى {center.center_level}</Badge>
                        </td>
                        <td className="p-3 text-center">
                          <Badge className={center.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {center.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                        </td>
                        <td className="p-3 text-gray-600">{center.description || '-'}</td>
                        <td className="p-3 text-center">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(center)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(center.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل */}
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingCenter ? 'تعديل مركز التكلفة' : 'إضافة مركز تكلفة جديد'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="center_code">رمز المركز *</Label>
                <Input
                  id="center_code"
                  value={formData.center_code}
                  onChange={(e) => setFormData({...formData, center_code: e.target.value})}
                  placeholder="مثال: CC001"
                  required
                />
              </div>

              <div>
                <Label htmlFor="center_name">اسم المركز *</Label>
                <Input
                  id="center_name"
                  value={formData.center_name}
                  onChange={(e) => setFormData({...formData, center_name: e.target.value})}
                  placeholder="أدخل اسم المركز"
                  required
                />
              </div>

              <div>
                <Label htmlFor="parent_id">المركز الأب</Label>
                <Select value={formData.parent_id} onValueChange={(value) => setFormData({...formData, parent_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المركز الأب (اختياري)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">بدون مركز أب</SelectItem>
                    {parentCenters.map((center) => (
                      <SelectItem key={center.id} value={center.id.toString()}>
                        {center.center_code} - {center.center_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="وصف مختصر لمركز التكلفة"
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>
                  إلغاء
                </Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                  {editingCenter ? 'تحديث' : 'إضافة'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
