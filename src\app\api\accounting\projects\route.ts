import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // جلب المشاريع مع معلومات العملاء والقضايا
    const result = await query(`
      SELECT 
        p.*,
        c.name as customer_name,
        cs.title as case_title,
        cs.id as case_id,
        COALESCE(
          (SELECT SUM(qty) FROM timecard WHERE project_id = p.id), 
          0
        ) as total_hours,
        COALESCE(
          (SELECT SUM(qty * sellprice) FROM timecard WHERE project_id = p.id), 
          0
        ) as total_cost,
        COALESCE(
          (SELECT SUM(amount) FROM ar WHERE entity_id = p.customer_id), 
          0
        ) as total_revenue,
        CASE 
          WHEN p.completed >= p.production AND p.production > 0 THEN 'completed'
          WHEN p.completed > 0 THEN 'in_progress'
          ELSE 'not_started'
        END as status
      FROM project p
      LEFT JOIN entity e ON p.customer_id = e.id
      LEFT JOIN company c ON e.id = c.entity_id
      LEFT JOIN cases cs ON cs.client_id = (
        SELECT client_id FROM clients WHERE entity_id = p.customer_id LIMIT 1
      )
      ORDER BY p.created_date DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset])

    // عدد المشاريع الإجمالي
    const countResult = await query('SELECT COUNT(*) as total FROM project')
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      data: result.rows.map(row => ({
        ...row,
        total_hours: parseFloat(row.total_hours || 0),
        total_cost: parseFloat(row.total_cost || 0),
        total_revenue: parseFloat(row.total_revenue || 0),
        profit_margin: row.total_revenue > 0 ? 
          ((row.total_revenue - row.total_cost) / row.total_revenue) * 100 : 0
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching projects:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب المشاريع' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      projectnumber,
      description,
      startdate,
      enddate,
      customer_id,
      case_id,
      production = 0,
      completed = 0
    } = body

    // التحقق من البيانات المطلوبة
    if (!projectnumber || !description || !startdate) {
      return NextResponse.json(
        { success: false, error: 'رقم المشروع والوصف وتاريخ البداية مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم المشروع
    const existingProject = await query(
      'SELECT id FROM project WHERE projectnumber = $1',
      [projectnumber]
    )

    if (existingProject.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم المشروع موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إنشاء المشروع
    const result = await query(`
      INSERT INTO project (
        projectnumber, description, startdate, enddate, 
        customer_id, production, completed
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      projectnumber, description, startdate, 
      enddate || null, customer_id || null, production, completed
    ])

    // ربط المشروع بالقضية إذا تم تحديدها
    if (case_id) {
      await query(`
        UPDATE cases 
        SET project_id = $1 
        WHERE id = $2
      `, [result.rows[0].id, case_id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء المشروع بنجاح',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error creating project:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء المشروع' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      projectnumber,
      description,
      startdate,
      enddate,
      customer_id,
      production,
      completed
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المشروع مطلوب' },
        { status: 400 }
      )
    }

    // تحديث المشروع
    const result = await query(`
      UPDATE project 
      SET 
        projectnumber = COALESCE($2, projectnumber),
        description = COALESCE($3, description),
        startdate = COALESCE($4, startdate),
        enddate = $5,
        customer_id = COALESCE($6, customer_id),
        production = COALESCE($7, production),
        completed = COALESCE($8, completed)
      WHERE id = $1
      RETURNING *
    `, [id, projectnumber, description, startdate, enddate, customer_id, production, completed])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المشروع غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المشروع بنجاح',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating project:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المشروع' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المشروع مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود إدخالات وقت مرتبطة
    const timecardCheck = await query(
      'SELECT COUNT(*) as count FROM timecard WHERE project_id = $1',
      [id]
    )

    if (parseInt(timecardCheck.rows[0].count) > 0) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف مشروع يحتوي على إدخالات وقت' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      // إلغاء ربط القضايا بالمشروع
      await query('UPDATE cases SET project_id = NULL WHERE project_id = $1', [id])
      
      // حذف المشروع
      const result = await query('DELETE FROM project WHERE id = $1 RETURNING *', [id])

      if (result.rows.length === 0) {
        await query('ROLLBACK')
        return NextResponse.json(
          { success: false, error: 'المشروع غير موجود' },
          { status: 404 }
        )
      }

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حذف المشروع بنجاح'
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error deleting project:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المشروع' },
      { status: 500 }
    )
  }
}
