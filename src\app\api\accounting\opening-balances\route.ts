import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الأرصدة الافتتاحية
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        ob.id,
        ob.account_id,
        coa.account_code,
        coa.account_name,
        ob.debit_balance,
        ob.credit_balance,
        ob.balance_date,
        ob.created_date
      FROM opening_balances ob
      LEFT JOIN chart_of_accounts coa ON ob.account_id = coa.id
      ORDER BY coa.account_code
    `)

    return NextResponse.json({
      success: true,
      data: result.rows,
      total: result.rows.length,
      message: 'تم جلب الأرصدة الافتتاحية بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الأرصدة الافتتاحية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الأرصدة الافتتاحية',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة رصيد افتتاحي جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      account_id,
      debit_balance = 0,
      credit_balance = 0,
      balance_date
    } = body

    // التحقق من صحة البيانات
    if (!account_id || !balance_date) {
      return NextResponse.json({
        success: false,
        error: 'الحساب وتاريخ الرصيد مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم وجود رصيد افتتاحي للحساب مسبقاً
    const existingBalance = await query(
      'SELECT id FROM opening_balances WHERE account_id = $1',
      [account_id]
    )

    if (existingBalance.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'يوجد رصيد افتتاحي لهذا الحساب مسبقاً'
      }, { status: 400 })
    }

    // التحقق من أن أحد الرصيدين على الأقل أكبر من صفر
    if (debit_balance <= 0 && credit_balance <= 0) {
      return NextResponse.json({
        success: false,
        error: 'يجب أن يكون أحد الرصيدين على الأقل أكبر من صفر'
      }, { status: 400 })
    }

    // التحقق من أن الرصيدين ليسا أكبر من صفر في نفس الوقت
    if (debit_balance > 0 && credit_balance > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن أن يكون للحساب رصيد مدين ودائن في نفس الوقت'
      }, { status: 400 })
    }

    const result = await query(`
      INSERT INTO opening_balances (account_id, debit_balance, credit_balance, balance_date)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [account_id, debit_balance, credit_balance, balance_date])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الرصيد الافتتاحي بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الرصيد الافتتاحي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الرصيد الافتتاحي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث رصيد افتتاحي
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      account_id,
      debit_balance = 0,
      credit_balance = 0,
      balance_date
    } = body

    if (!id || !account_id || !balance_date) {
      return NextResponse.json({
        success: false,
        error: 'المعرف والحساب وتاريخ الرصيد مطلوبان'
      }, { status: 400 })
    }

    // التحقق من أن أحد الرصيدين على الأقل أكبر من صفر
    if (debit_balance <= 0 && credit_balance <= 0) {
      return NextResponse.json({
        success: false,
        error: 'يجب أن يكون أحد الرصيدين على الأقل أكبر من صفر'
      }, { status: 400 })
    }

    // التحقق من أن الرصيدين ليسا أكبر من صفر في نفس الوقت
    if (debit_balance > 0 && credit_balance > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن أن يكون للحساب رصيد مدين ودائن في نفس الوقت'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE opening_balances 
      SET account_id = $1, debit_balance = $2, credit_balance = $3, balance_date = $4
      WHERE id = $5
      RETURNING *
    `, [account_id, debit_balance, credit_balance, balance_date, id])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الرصيد الافتتاحي غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الرصيد الافتتاحي بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الرصيد الافتتاحي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الرصيد الافتتاحي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف رصيد افتتاحي
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الرصيد الافتتاحي مطلوب'
      }, { status: 400 })
    }

    const result = await query(
      'DELETE FROM opening_balances WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الرصيد الافتتاحي غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الرصيد الافتتاحي بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الرصيد الافتتاحي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف الرصيد الافتتاحي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
