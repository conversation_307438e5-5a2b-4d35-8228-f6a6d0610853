import { NextRequest, NextResponse } from 'next/server'

// GET - جلب جميع سندات الدفع
export async function GET() {
  try {
    // محاكاة بيانات سندات الدفع
    const sampleData = [
      {
        id: 1,
        voucher_number: 'PAY-001',
        date: '2024-02-15',
        account_id: 'fin_2001001',
        account_name: 'الموردون',
        account_type: 'financial',
        amount: 15000,
        description: 'دفع مستحقات موردين',
        reference: 'INV-001',
        payment_method: 'cash',
        status: 'approved',
        created_by: 'أحمد المحامي',
        created_date: '2024-02-15'
      },
      {
        id: 2,
        voucher_number: 'PAY-002',
        date: '2024-02-14',
        account_id: 'fin_5002',
        account_name: 'مصروفات الإيجار',
        account_type: 'financial',
        amount: 8000,
        description: 'دفع إيجار المكتب لشهر فبراير',
        reference: 'RENT-FEB-2024',
        payment_method: 'bank_transfer',
        status: 'approved',
        created_by: 'سارة المحامية',
        created_date: '2024-02-14'
      },
      {
        id: 3,
        voucher_number: 'PAY-003',
        date: '2024-02-13',
        account_id: 'employee_1',
        account_name: 'محمد أحمد',
        account_type: 'employee',
        amount: 12000,
        description: 'دفع راتب شهر فبراير',
        reference: 'SALARY-FEB-2024',
        payment_method: 'bank_transfer',
        status: 'pending',
        created_by: 'فاطمة الإدارية',
        created_date: '2024-02-13'
      },
      {
        id: 4,
        voucher_number: 'PAY-004',
        date: '2024-02-12',
        account_id: 'fin_5003',
        account_name: 'مصروفات الكهرباء',
        account_type: 'financial',
        amount: 2500,
        description: 'دفع فاتورة الكهرباء',
        reference: 'ELEC-001',
        payment_method: 'cash',
        status: 'approved',
        created_by: 'خالد المحامي',
        created_date: '2024-02-12'
      },
      {
        id: 5,
        voucher_number: 'PAY-005',
        date: '2024-02-11',
        account_id: 'fin_5004',
        account_name: 'مصروفات الاتصالات',
        account_type: 'financial',
        amount: 1800,
        description: 'دفع فاتورة الإنترنت والهاتف',
        reference: 'COMM-001',
        payment_method: 'bank_transfer',
        status: 'approved',
        created_by: 'نور المحامية',
        created_date: '2024-02-11'
      }
    ]
    
    return NextResponse.json({
      success: true,
      data: sampleData
    })
  } catch (error) {
    console.error('Error fetching payment vouchers:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات سندات الدفع' },
      { status: 500 }
    )
  }
}

// POST - إضافة سند دفع جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      voucher_number, 
      date, 
      account_id, 
      account_name,
      amount, 
      description, 
      reference, 
      payment_method,
      status = 'pending',
      created_by 
    } = body

    if (!voucher_number || !date || !account_id || !amount) {
      return NextResponse.json(
        { success: false, error: 'رقم السند والتاريخ والحساب والمبلغ مطلوبة' },
        { status: 400 }
      )
    }

    // محاكاة إضافة سند دفع جديد
    const newVoucher = {
      id: Date.now(),
      voucher_number,
      date,
      account_id,
      account_name: account_name || 'حساب جديد',
      account_type: account_id.startsWith('client_') ? 'client' : 
                   account_id.startsWith('employee_') ? 'employee' : 'financial',
      amount: Number(amount),
      description: description || '',
      reference: reference || '',
      payment_method: payment_method || 'cash',
      status,
      created_by: created_by || '',
      created_date: new Date().toISOString().split('T')[0]
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة سند الدفع بنجاح',
      data: newVoucher
    })
  } catch (error) {
    console.error('Error creating payment voucher:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة سند الدفع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث سند دفع
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      id, 
      voucher_number, 
      date, 
      account_id, 
      account_name,
      amount, 
      description, 
      reference, 
      payment_method,
      status,
      created_by 
    } = body

    if (!id || !voucher_number || !date || !account_id || !amount) {
      return NextResponse.json(
        { success: false, error: 'المعرف ورقم السند والتاريخ والحساب والمبلغ مطلوبة' },
        { status: 400 }
      )
    }

    // محاكاة تحديث سند الدفع
    return NextResponse.json({
      success: true,
      message: 'تم تحديث سند الدفع بنجاح'
    })
  } catch (error) {
    console.error('Error updating payment voucher:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث سند الدفع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف سند دفع
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف سند الدفع مطلوب' },
        { status: 400 }
      )
    }

    // محاكاة حذف سند الدفع
    return NextResponse.json({
      success: true,
      message: 'تم حذف سند الدفع بنجاح'
    })
  } catch (error) {
    console.error('Error deleting payment voucher:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف سند الدفع' },
      { status: 500 }
    )
  }
}
