const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function addClientRelationship() {
  
  try {
    console.log('🔄 جاري إضافة العلاقة بين جدول العملاء وجدول القضايا...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من الأعمدة الموجودة في جدول القضايا
    const existingColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'issues'
    `);
    
    const columnNames = existingColumns.rows.map(row => row.column_name);
    console.log('📋 الأعمدة الموجودة في جدول القضايا:', columnNames);

    // إضافة عمود client_id إذا لم يكن موجوداً
    if (!columnNames.includes('client_id')) {
      await client.query(`
        ALTER TABLE issues 
        ADD COLUMN client_id INTEGER
      `);
      console.log('✅ تم إضافة عمود client_id');
    } else {
      console.log('⚠️  عمود client_id موجود بالفعل');
    }

    // إضافة Foreign Key Constraint
    try {
      await client.query(`
        ALTER TABLE issues 
        ADD CONSTRAINT fk_issues_client_id 
        FOREIGN KEY (client_id) REFERENCES clients(id) 
        ON DELETE SET NULL ON UPDATE CASCADE
      `);
      console.log('✅ تم إضافة Foreign Key Constraint');
    } catch (error) {
      if (error.code === '42710') {
        console.log('⚠️  Foreign Key Constraint موجود بالفعل');
      } else {
        console.log('⚠️  خطأ في إضافة Foreign Key:', error.message);
      }
    }

    // ربط القضايا الموجودة بالعملاء بناءً على الاسم
    console.log('🔄 جاري ربط القضايا الموجودة بالعملاء...');
    
    const updateResult = await client.query(`
      UPDATE issues 
      SET client_id = clients.id
      FROM clients 
      WHERE issues.client_name = clients.name 
      AND issues.client_id IS NULL
    `);
    
    console.log(`✅ تم ربط ${updateResult.rowCount} قضية بالعملاء`);

    // إنشاء فهرس لتحسين الأداء
    try {
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_issues_client_id 
        ON issues(client_id)
      `);
      console.log('✅ تم إنشاء فهرس client_id');
    } catch (error) {
      console.log('⚠️  خطأ في إنشاء الفهرس:', error.message);
    }

    // عرض إحصائيات
    const stats = await client.query(`
      SELECT 
        COUNT(*) as total_issues,
        COUNT(client_id) as linked_issues,
        COUNT(*) - COUNT(client_id) as unlinked_issues
      FROM issues
    `);

    const clientStats = await client.query(`
      SELECT 
        c.name as client_name,
        COUNT(i.id) as issues_count
      FROM clients c
      LEFT JOIN issues i ON c.id = i.client_id
      GROUP BY c.id, c.name
      ORDER BY issues_count DESC
      LIMIT 10
    `);

    console.log('\n📊 إحصائيات القضايا:');
    console.log(`   - إجمالي القضايا: ${stats.rows[0].total_issues}`);
    console.log(`   - القضايا المربوطة: ${stats.rows[0].linked_issues}`);
    console.log(`   - القضايا غير المربوطة: ${stats.rows[0].unlinked_issues}`);

    console.log('\n👥 أكثر العملاء نشاطاً:');
    clientStats.rows.forEach(row => {
      console.log(`   - ${row.client_name}: ${row.issues_count} قضية`);
    });

    // عرض هيكل الجدول المحدث
    const updatedStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'issues'
      ORDER BY ordinal_position
    `);

    console.log('\n📊 هيكل جدول القضايا المحدث:');
    updatedStructure.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(مطلوب)' : '(اختياري)'}`);
    });

    console.log('\n🎉 تم إنشاء العلاقة بين العملاء والقضايا بنجاح!');
    console.log('\n📋 الآن يمكن:');
    console.log('   ✅ ربط كل قضية بعميل محدد');
    console.log('   ✅ عرض جميع قضايا عميل معين');
    console.log('   ✅ ضمان تكامل البيانات');
    console.log('   ✅ تجنب تكرار أسماء العملاء');

  } catch (error) {
    console.error('❌ خطأ في إضافة العلاقة:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
addClientRelationship();
