#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 8444

class LedgerSMBHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html_content = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LedgerSMB - النظام المحاسبي</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header { 
            text-align: center; 
            margin-bottom: 40px; 
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }
        .logo { 
            font-size: 3em; 
            color: #2c5aa0; 
            margin-bottom: 10px; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .subtitle { 
            color: #666; 
            font-size: 1.3em; 
            font-weight: 300;
        }
        .status { 
            background: linear-gradient(45deg, #4CAF50, #45a049); 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 30px 0; 
            text-align: center;
            font-size: 1.1em;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        .features { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 25px; 
            margin: 40px 0; 
        }
        .feature { 
            background: linear-gradient(145deg, #f8f9fa, #e9ecef); 
            padding: 25px; 
            border-radius: 12px; 
            border-right: 5px solid #2c5aa0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .feature h3 { 
            color: #2c5aa0; 
            margin-top: 0; 
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature p {
            color: #555;
            line-height: 1.6;
        }
        .demo-section { 
            background: linear-gradient(145deg, #e3f2fd, #bbdefb); 
            padding: 30px; 
            border-radius: 12px; 
            margin: 30px 0; 
            text-align: center;
        }
        .demo-section h3 {
            color: #1976d2;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        .btn { 
            background: linear-gradient(45deg, #2c5aa0, #1e3d72); 
            color: white; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 16px; 
            text-decoration: none; 
            display: inline-block; 
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
        }
        .btn:hover { 
            background: linear-gradient(45deg, #1e3d72, #2c5aa0); 
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-top: 4px solid #2c5aa0;
        }
        .info-card h4 {
            color: #2c5aa0;
            margin-top: 0;
        }
        .footer {
            text-align: center; 
            margin-top: 40px; 
            padding-top: 20px;
            border-top: 2px solid #eee;
            color: #666;
        }
        .footer a {
            color: #2c5aa0;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo pulse">📊 LedgerSMB</div>
            <div class="subtitle">نظام المحاسبة المتقدم مفتوح المصدر</div>
        </div>
        
        <div class="status">
            ✅ خادم LedgerSMB يعمل بنجاح على المنفذ 8444
            <br>
            🌐 الوقت: <span id="current-time"></span>
        </div>
        
        <div class="demo-section">
            <h3>🚀 مرحباً بك في LedgerSMB</h3>
            <p>هذا عرض توضيحي لنظام LedgerSMB المحاسبي. النظام يعمل بنجاح على الخادم المحلي.</p>
            <button class="btn" onclick="showDemo()">🎯 عرض توضيحي</button>
            <button class="btn" onclick="showFeatures()">📋 الميزات</button>
            <button class="btn" onclick="showInfo()">ℹ️ معلومات النظام</button>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>📋 المحاسبة العامة</h3>
                <p>دليل الحسابات الشامل، القيود اليومية، ميزان المراجعة، والتقارير المالية الأساسية</p>
            </div>
            <div class="feature">
                <h3>💰 الحسابات المدينة</h3>
                <p>إدارة العملاء، إصدار الفواتير، تتبع المقبوضات، وإدارة الديون المستحقة</p>
            </div>
            <div class="feature">
                <h3>💳 الحسابات الدائنة</h3>
                <p>إدارة الموردين، معالجة الفواتير، جدولة المدفوعات، وتتبع الالتزامات</p>
            </div>
            <div class="feature">
                <h3>📦 إدارة المخزون</h3>
                <p>تتبع المنتجات، إدارة الكميات، تقييم المخزون، وتقارير الحركة</p>
            </div>
            <div class="feature">
                <h3>👥 كشوف الرواتب</h3>
                <p>إدارة بيانات الموظفين، حساب الرواتب، الخصومات، والمزايا</p>
            </div>
            <div class="feature">
                <h3>📊 التقارير المالية</h3>
                <p>قائمة الدخل، الميزانية العمومية، قائمة التدفق النقدي، والتحليلات المالية</p>
            </div>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h4>🌐 معلومات الخادم</h4>
                <p><strong>المنفذ:</strong> 8444</p>
                <p><strong>الحالة:</strong> يعمل</p>
                <p><strong>النوع:</strong> خادم تطوير</p>
            </div>
            <div class="info-card">
                <h4>🔧 التقنيات المستخدمة</h4>
                <p><strong>اللغة:</strong> Python</p>
                <p><strong>الخادم:</strong> HTTP Server</p>
                <p><strong>الواجهة:</strong> HTML5/CSS3</p>
            </div>
            <div class="info-card">
                <h4>📋 معلومات النظام</h4>
                <p><strong>الإصدار:</strong> Demo 1.0</p>
                <p><strong>الترخيص:</strong> مفتوح المصدر</p>
                <p><strong>الدعم:</strong> متوفر</p>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>LedgerSMB</strong> - نظام محاسبي مفتوح المصدر قوي ومرن</p>
            <p>للمزيد من المعلومات: <a href="https://ledgersmb.org" target="_blank">ledgersmb.org</a></p>
            <p>تم التطوير بواسطة مجتمع LedgerSMB العالمي</p>
        </div>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('ar-SA');
        }
        
        function showDemo() {
            alert('🎯 مرحباً بك في LedgerSMB!\\n\\nهذا عرض توضيحي للنظام المحاسبي.\\n\\nالميزات المتوفرة:\\n• دليل الحسابات\\n• القيود اليومية\\n• التقارير المالية\\n• إدارة العملاء والموردين\\n• إدارة المخزون\\n• كشوف الرواتب');
        }
        
        function showFeatures() {
            alert('📋 ميزات LedgerSMB:\\n\\n✅ محاسبة مزدوجة القيد\\n✅ إدارة متعددة العملات\\n✅ تقارير مالية شاملة\\n✅ إدارة المشاريع\\n✅ نقاط البيع\\n✅ التكامل مع البنوك\\n✅ أمان متقدم\\n✅ واجهة ويب حديثة');
        }
        
        function showInfo() {
            alert('ℹ️ معلومات النظام:\\n\\n🌐 الخادم: localhost:8444\\n🔧 الحالة: يعمل بنجاح\\n📊 النوع: خادم تطوير\\n🛠️ التقنية: Python HTTP Server\\n📋 الإصدار: Demo 1.0\\n🔗 المصدر: https://ledgersmb.org');
        }
        
        // تحديث الوقت كل ثانية
        updateTime();
        setInterval(updateTime, 1000);
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                setTimeout(() => {
                    feature.style.opacity = '0';
                    feature.style.transform = 'translateY(20px)';
                    feature.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        feature.style.opacity = '1';
                        feature.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>'''
            
            self.wfile.write(html_content.encode('utf-8'))
        else:
            # إرجاع 404 للمسارات الأخرى
            self.send_response(404)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(b'<h1>404 - Page Not Found</h1>')

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == "__main__":
    Handler = LedgerSMBHandler
    
    print("=" * 50)
    print("🚀 بدء تشغيل خادم LedgerSMB")
    print("=" * 50)
    print(f"🌐 المنفذ: {PORT}")
    print(f"🔗 الرابط المحلي: http://localhost:{PORT}")
    print(f"🔗 الرابط الخارجي: http://[your-ip]:{PORT}")
    print("⏹️ لإيقاف الخادم، اضغط Ctrl+C")
    print("=" * 50)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"✅ الخادم يعمل على http://localhost:{PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except OSError as e:
        if e.errno == 10048:  # Address already in use
            print(f"❌ المنفذ {PORT} مستخدم بالفعل")
            print("💡 جرب منفذ آخر أو أوقف الخدمة المستخدمة للمنفذ")
        else:
            print(f"❌ خطأ في تشغيل الخادم: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
    finally:
        print("🔚 تم إنهاء الخادم")
