'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BarChart3, FileText, TrendingUp, PieChart, Activity, Calculator } from 'lucide-react'

export default function FinancialReportsPage() {
  const reports = [
    {
      id: 'income-statement',
      title: 'قائمة الدخل',
      description: 'تقرير الأرباح والخسائر للفترة المحددة',
      icon: TrendingUp,
      color: 'bg-green-500',
      href: '/accounting/reports'
    },
    {
      id: 'balance-sheet',
      title: 'الميزانية العمومية',
      description: 'المركز المالي للشركة في تاريخ محدد',
      icon: PieChart,
      color: 'bg-blue-500',
      href: '/accounting/reports'
    },
    {
      id: 'cash-flow',
      title: 'قائمة التدفقات النقدية',
      description: 'حركة النقدية من الأنشطة المختلفة',
      icon: Activity,
      color: 'bg-purple-500',
      href: '/accounting/reports'
    },
    {
      id: 'trial-balance',
      title: 'ميزان المراجعة',
      description: 'أرصدة جميع الحسابات في تاريخ محدد',
      icon: Calculator,
      color: 'bg-orange-500',
      href: '/accounting/reports'
    },
    {
      id: 'account-statement',
      title: 'كشف حساب',
      description: 'تفاصيل حركة حساب محدد مع الرصيد الجاري',
      icon: FileText,
      color: 'bg-cyan-500',
      href: '/accounting/reports/account-statement'
    },
    {
      id: 'vouchers-summary',
      title: 'ملخص السندات',
      description: 'إجماليات سندات الصرف والقبض',
      icon: BarChart3,
      color: 'bg-indigo-500',
      href: '/accounting/reports'
    }
  ]

  const handleReportClick = (href: string) => {
    window.location.href = href
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <BarChart3 className="h-8 w-8 text-rose-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">التقارير المالية</h1>
            <p className="text-gray-600">تقارير مالية شاملة وتحليلات متقدمة</p>
          </div>
        </div>

        {/* التقارير المتاحة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reports.map((report) => (
            <Card 
              key={report.id} 
              className="cursor-pointer transition-all hover:shadow-lg hover:scale-105"
              onClick={() => handleReportClick(report.href)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className={`p-3 rounded-lg ${report.color} text-white`}>
                    <report.icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{report.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">{report.description}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleReportClick(report.href)
                  }}
                >
                  عرض التقرير
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* معلومات إضافية */}
        <Card>
          <CardHeader>
            <CardTitle>معلومات مهمة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-900 mb-2">📊 التقارير المحاسبية</h3>
                <p className="text-sm text-blue-700">
                  جميع التقارير المحاسبية متاحة في قسم المحاسبة مع إمكانية التصدير لـ PDF و Excel
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium text-green-900 mb-2">🔍 كشف الحساب</h3>
                <p className="text-sm text-green-700">
                  يمكنك عرض تفاصيل أي حساب مع الرصيد الجاري وجميع المعاملات
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
