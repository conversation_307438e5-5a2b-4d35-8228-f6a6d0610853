const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function verifyAccountRelationships() {
  try {
    console.log('🔍 التحقق من العلاقات المحاسبية...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. التحقق من بنية جدول account_linking_settings
    console.log('\n📋 بنية جدول account_linking_settings:');
    const settingsStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'account_linking_settings'
      ORDER BY ordinal_position
    `);
    
    settingsStructure.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : ''}`);
    });

    // 2. التحقق من بنية جدول العملاء
    console.log('\n📋 بنية جدول العملاء (clients):');
    const clientsStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'clients' AND column_name = 'account_id'
    `);
    
    if (clientsStructure.rows.length > 0) {
      clientsStructure.rows.forEach(col => {
        console.log(`   ✅ ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : ''}`);
      });
    } else {
      console.log('   ❌ عمود account_id غير موجود في جدول العملاء');
    }

    // 3. التحقق من بنية جدول الموظفين
    console.log('\n📋 بنية جدول الموظفين (employees):');
    const employeesStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'employees' AND column_name = 'account_id'
    `);
    
    if (employeesStructure.rows.length > 0) {
      employeesStructure.rows.forEach(col => {
        console.log(`   ✅ ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : ''}`);
      });
    } else {
      console.log('   ❌ عمود account_id غير موجود في جدول الموظفين');
    }

    // 4. التحقق من المفاتيح الخارجية
    console.log('\n🔗 المفاتيح الخارجية:');
    const foreignKeys = await client.query(`
      SELECT 
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        tc.constraint_name
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND (tc.table_name = 'clients' OR tc.table_name = 'employees')
        AND kcu.column_name = 'account_id'
    `);
    
    if (foreignKeys.rows.length > 0) {
      foreignKeys.rows.forEach(fk => {
        console.log(`   ✅ ${fk.table_name}.${fk.column_name} → ${fk.foreign_table_name}.${fk.foreign_column_name}`);
      });
    } else {
      console.log('   ❌ لم يتم العثور على مفاتيح خارجية للعمود account_id');
    }

    // 5. عرض إعدادات الربط المتاحة
    console.log('\n⚙️ إعدادات الربط المتاحة:');
    const linkingSettings = await client.query(`
      SELECT id, table_name, table_display_name, is_enabled, account_prefix
      FROM account_linking_settings
      ORDER BY id
    `);
    
    linkingSettings.rows.forEach(setting => {
      console.log(`   ${setting.id}: ${setting.table_display_name} (${setting.table_name}) - ${setting.is_enabled ? 'مفعل' : 'معطل'} - البادئة: ${setting.account_prefix || 'غير محدد'}`);
    });

    // 6. عرض العملاء المربوطين
    console.log('\n👥 العملاء المربوطين:');
    const linkedClients = await client.query(`
      SELECT 
        c.id,
        c.name,
        c.account_id,
        als.table_display_name,
        als.account_prefix
      FROM clients c
      LEFT JOIN account_linking_settings als ON c.account_id = als.id
      ORDER BY c.id
    `);
    
    linkedClients.rows.forEach(client => {
      const linkStatus = client.account_id ? `مربوط بـ ${client.table_display_name} (${client.account_prefix})` : 'غير مربوط';
      console.log(`   ${client.id}: ${client.name} - ${linkStatus}`);
    });

    // 7. عرض الموظفين المربوطين
    console.log('\n👨‍💼 الموظفين المربوطين:');
    const linkedEmployees = await client.query(`
      SELECT 
        e.id,
        e.name,
        e.account_id,
        als.table_display_name,
        als.account_prefix
      FROM employees e
      LEFT JOIN account_linking_settings als ON e.account_id = als.id
      ORDER BY e.id
    `);
    
    linkedEmployees.rows.forEach(employee => {
      const linkStatus = employee.account_id ? `مربوط بـ ${employee.table_display_name} (${employee.account_prefix})` : 'غير مربوط';
      console.log(`   ${employee.id}: ${employee.name} - ${linkStatus}`);
    });

    // 8. التحقق من المحفزات (Triggers)
    console.log('\n🔄 المحفزات المتاحة:');
    const triggers = await client.query(`
      SELECT 
        trigger_name,
        event_object_table,
        action_timing,
        event_manipulation
      FROM information_schema.triggers
      WHERE trigger_name LIKE '%auto_link%'
      ORDER BY event_object_table, trigger_name
    `);
    
    if (triggers.rows.length > 0) {
      triggers.rows.forEach(trigger => {
        console.log(`   ✅ ${trigger.trigger_name} على جدول ${trigger.event_object_table} (${trigger.action_timing} ${trigger.event_manipulation})`);
      });
    } else {
      console.log('   ❌ لم يتم العثور على محفزات الربط التلقائي');
    }

    // 9. إحصائيات شاملة
    console.log('\n📊 إحصائيات شاملة:');
    const stats = await client.query(`
      SELECT 
        'العملاء' as entity_type,
        COUNT(*) as total_count,
        COUNT(account_id) as linked_count,
        COUNT(*) - COUNT(account_id) as unlinked_count
      FROM clients
      UNION ALL
      SELECT 
        'الموظفين' as entity_type,
        COUNT(*) as total_count,
        COUNT(account_id) as linked_count,
        COUNT(*) - COUNT(account_id) as unlinked_count
      FROM employees
    `);
    
    stats.rows.forEach(stat => {
      console.log(`   ${stat.entity_type}: ${stat.total_count} إجمالي، ${stat.linked_count} مربوط، ${stat.unlinked_count} غير مربوط`);
    });

    console.log('\n✅ تم التحقق من العلاقات المحاسبية بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في التحقق:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  verifyAccountRelationships()
    .then(() => {
      console.log('🎉 تم إنجاز التحقق بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في التحقق:', error);
      process.exit(1);
    });
}

module.exports = { verifyAccountRelationships };
