'use client'

import { useState } from 'react'
import { ChevronRight, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TreeNodeData {
  id: string | number
  label: string
  children?: TreeNodeData[]
  icon?: React.ReactNode
  badge?: React.ReactNode
  actions?: React.ReactNode
  className?: string
}

interface TreeNodeProps {
  node: TreeNodeData
  level?: number
  onNodeClick?: (node: TreeNodeData) => void
  expandedNodes?: Set<string | number>
  onToggleExpand?: (nodeId: string | number) => void
}

export function TreeNode({ 
  node, 
  level = 0, 
  onNodeClick, 
  expandedNodes = new Set(),
  onToggleExpand 
}: TreeNodeProps) {
  const hasChildren = node.children && node.children.length > 0
  const isExpanded = expandedNodes.has(node.id)
  const paddingLeft = level * 24

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (hasChildren && onToggleExpand) {
      onToggleExpand(node.id)
    }
  }

  const handleNodeClick = () => {
    if (onNodeClick) {
      onNodeClick(node)
    }
  }

  return (
    <div className="select-none">
      <div
        className={cn(
          "flex items-center py-2 px-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors",
          node.className
        )}
        style={{ paddingRight: `${paddingLeft + 8}px` }}
        onClick={handleNodeClick}
      >
        {/* أيقونة التوسيع/الطي */}
        <div
          className="flex items-center justify-center w-4 h-4 ml-2"
          onClick={handleToggle}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="h-3 w-3 text-gray-500" />
            ) : (
              <ChevronRight className="h-3 w-3 text-gray-500" />
            )
          ) : (
            <div className="w-3 h-3" />
          )}
        </div>

        {/* أيقونة العقدة */}
        {node.icon && (
          <div className="flex items-center justify-center w-4 h-4 ml-2">
            {node.icon}
          </div>
        )}

        {/* نص العقدة */}
        <span className="flex-1 text-sm font-medium text-gray-900">
          {node.label}
        </span>

        {/* شارة */}
        {node.badge && (
          <div className="ml-2">
            {node.badge}
          </div>
        )}

        {/* إجراءات */}
        {node.actions && (
          <div className="ml-2" onClick={(e) => e.stopPropagation()}>
            {node.actions}
          </div>
        )}
      </div>

      {/* العقد الفرعية */}
      {hasChildren && isExpanded && (
        <div>
          {node.children!.map((child) => (
            <TreeNode
              key={child.id}
              node={child}
              level={level + 1}
              onNodeClick={onNodeClick}
              expandedNodes={expandedNodes}
              onToggleExpand={onToggleExpand}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface TreeProps {
  data: TreeNodeData[]
  onNodeClick?: (node: TreeNodeData) => void
  className?: string
  defaultExpandedNodes?: (string | number)[]
}

export function Tree({ 
  data, 
  onNodeClick, 
  className,
  defaultExpandedNodes = []
}: TreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string | number>>(
    new Set(defaultExpandedNodes)
  )

  const handleToggleExpand = (nodeId: string | number) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  return (
    <div className={cn("space-y-1", className)} dir="rtl">
      {data.map((node) => (
        <TreeNode
          key={node.id}
          node={node}
          onNodeClick={onNodeClick}
          expandedNodes={expandedNodes}
          onToggleExpand={handleToggleExpand}
        />
      ))}
    </div>
  )
}
