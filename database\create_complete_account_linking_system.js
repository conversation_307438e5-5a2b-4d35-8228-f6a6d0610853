// إنشاء نظام ربط الحسابات المتكامل - مثل الأنظمة المحاسبية المحترفة
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function createCompleteAccountLinkingSystem() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 إنشاء نظام ربط الحسابات المتكامل...');
    await client.connect();

    // 1. تحديث جدول دليل الحسابات ليكون مثل الأنظمة المحترفة
    await client.query(`
      DROP TABLE IF EXISTS chart_of_accounts CASCADE;
      CREATE TABLE chart_of_accounts (
        id SERIAL PRIMARY KEY,
        account_code VARCHAR(20) UNIQUE NOT NULL,
        account_name VARCHAR(255) NOT NULL,
        account_name_en VARCHAR(255),
        account_type VARCHAR(50) NOT NULL,
        parent_id INTEGER REFERENCES chart_of_accounts(id),
        account_level INTEGER DEFAULT 1,
        is_main_account BOOLEAN DEFAULT FALSE,
        is_sub_account BOOLEAN DEFAULT FALSE,
        is_control_account BOOLEAN DEFAULT FALSE,
        linked_table VARCHAR(100),
        auto_create_sub_accounts BOOLEAN DEFAULT FALSE,
        sub_account_prefix VARCHAR(10),
        account_nature VARCHAR(20) DEFAULT 'مدين',
        opening_balance DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        allow_posting BOOLEAN DEFAULT TRUE,
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 2. إنشاء جدول ربط الحسابات الفرعية
    await client.query(`
      CREATE TABLE account_sub_links (
        id SERIAL PRIMARY KEY,
        main_account_id INTEGER REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
        linked_table VARCHAR(100) NOT NULL,
        linked_record_id INTEGER NOT NULL,
        sub_account_code VARCHAR(50) UNIQUE NOT NULL,
        sub_account_name VARCHAR(255) NOT NULL,
        opening_balance DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(100),
        notes TEXT,
        UNIQUE(main_account_id, linked_table, linked_record_id)
      );
    `);

    // 3. إنشاء جدول إعدادات الربط
    await client.query(`
      CREATE TABLE account_linking_settings (
        id SERIAL PRIMARY KEY,
        table_name VARCHAR(100) UNIQUE NOT NULL,
        table_display_name VARCHAR(255) NOT NULL,
        table_description TEXT,
        is_enabled BOOLEAN DEFAULT TRUE,
        auto_create_on_insert BOOLEAN DEFAULT TRUE,
        account_prefix VARCHAR(10),
        name_field VARCHAR(100) DEFAULT 'name',
        id_field VARCHAR(100) DEFAULT 'id',
        default_main_account_id INTEGER REFERENCES chart_of_accounts(id),
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 4. إدراج البيانات الأساسية لدليل الحسابات
    const accountsData = [
      // الأصول
      { code: '1', name: 'الأصول', type: 'أصول', level: 1, is_main: true, nature: 'مدين' },
      { code: '11', name: 'الأصول المتداولة', type: 'أصول', parent: '1', level: 2, nature: 'مدين' },
      { code: '111', name: 'النقدية والبنوك', type: 'أصول', parent: '11', level: 3, nature: 'مدين' },
      { code: '1111', name: 'الصندوق', type: 'أصول', parent: '111', level: 4, nature: 'مدين', allow_posting: true },
      { code: '1112', name: 'البنك', type: 'أصول', parent: '111', level: 4, nature: 'مدين', allow_posting: true },
      
      { code: '112', name: 'المدينون', type: 'أصول', parent: '11', level: 3, nature: 'مدين', is_control: true },
      { code: '1121', name: 'حسابات الموكلين', type: 'أصول', parent: '112', level: 4, nature: 'مدين', 
        linked_table: 'clients', auto_create: true, prefix: 'CLI', is_control: true },
      { code: '1122', name: 'حسابات الموظفين', type: 'أصول', parent: '112', level: 4, nature: 'مدين',
        linked_table: 'employees', auto_create: true, prefix: 'EMP', is_control: true },
      
      // الخصوم
      { code: '2', name: 'الخصوم', type: 'خصوم', level: 1, is_main: true, nature: 'دائن' },
      { code: '21', name: 'الخصوم المتداولة', type: 'خصوم', parent: '2', level: 2, nature: 'دائن' },
      { code: '211', name: 'الدائنون', type: 'خصوم', parent: '21', level: 3, nature: 'دائن' },
      
      // حقوق الملكية
      { code: '3', name: 'حقوق الملكية', type: 'حقوق ملكية', level: 1, is_main: true, nature: 'دائن' },
      { code: '31', name: 'رأس المال', type: 'حقوق ملكية', parent: '3', level: 2, nature: 'دائن', allow_posting: true },
      
      // الإيرادات
      { code: '4', name: 'الإيرادات', type: 'إيرادات', level: 1, is_main: true, nature: 'دائن' },
      { code: '41', name: 'إيرادات التشغيل', type: 'إيرادات', parent: '4', level: 2, nature: 'دائن' },
      { code: '411', name: 'إيرادات القضايا', type: 'إيرادات', parent: '41', level: 3, nature: 'دائن',
        linked_table: 'issues', auto_create: true, prefix: 'CASE', is_control: true },
      
      // المصروفات
      { code: '5', name: 'المصروفات', type: 'مصروفات', level: 1, is_main: true, nature: 'مدين' },
      { code: '51', name: 'مصروفات التشغيل', type: 'مصروفات', parent: '5', level: 2, nature: 'مدين' },
      { code: '511', name: 'مصروفات المحاكم', type: 'مصروفات', parent: '51', level: 3, nature: 'مدين',
        linked_table: 'courts', auto_create: true, prefix: 'CRT', is_control: true },
      { code: '512', name: 'مصروفات الفروع', type: 'مصروفات', parent: '51', level: 3, nature: 'مدين',
        linked_table: 'branches', auto_create: true, prefix: 'BR', is_control: true },
      { code: '513', name: 'مراكز التكلفة', type: 'مصروفات', parent: '51', level: 3, nature: 'مدين',
        linked_table: 'cost_centers', auto_create: true, prefix: 'CC', is_control: true }
    ];

    // إدراج الحسابات
    for (const account of accountsData) {
      let parentId = null;
      if (account.parent) {
        const parentResult = await client.query('SELECT id FROM chart_of_accounts WHERE account_code = $1', [account.parent]);
        if (parentResult.rows.length > 0) {
          parentId = parentResult.rows[0].id;
        }
      }

      await client.query(`
        INSERT INTO chart_of_accounts 
        (account_code, account_name, account_type, parent_id, account_level, 
         is_main_account, is_sub_account, is_control_account, linked_table, 
         auto_create_sub_accounts, sub_account_prefix, account_nature, allow_posting)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      `, [
        account.code, account.name, account.type, parentId, account.level,
        account.is_main || false, !account.is_main, account.is_control || false,
        account.linked_table || null, account.auto_create || false,
        account.prefix || null, account.nature, account.allow_posting !== false
      ]);
    }

    // 5. إدراج إعدادات الربط
    const linkingSettings = [
      {
        table: 'clients', display: 'الموكلين', description: 'ربط حسابات الموكلين تلقائياً',
        prefix: 'CLI', name_field: 'name', account_code: '1121'
      },
      {
        table: 'employees', display: 'الموظفين', description: 'ربط حسابات الموظفين تلقائياً',
        prefix: 'EMP', name_field: 'name', account_code: '1122'
      },
      {
        table: 'issues', display: 'القضايا', description: 'ربط حسابات القضايا تلقائياً',
        prefix: 'CASE', name_field: 'title', account_code: '411'
      },
      {
        table: 'courts', display: 'المحاكم', description: 'ربط حسابات المحاكم تلقائياً',
        prefix: 'CRT', name_field: 'name', account_code: '511'
      },
      {
        table: 'branches', display: 'الفروع', description: 'ربط حسابات الفروع تلقائياً',
        prefix: 'BR', name_field: 'name', account_code: '512'
      },
      {
        table: 'cost_centers', display: 'مراكز التكلفة', description: 'ربط مراكز التكلفة تلقائياً',
        prefix: 'CC', name_field: 'name', account_code: '513'
      }
    ];

    for (const setting of linkingSettings) {
      const accountResult = await client.query('SELECT id FROM chart_of_accounts WHERE account_code = $1', [setting.account_code]);
      const accountId = accountResult.rows.length > 0 ? accountResult.rows[0].id : null;

      await client.query(`
        INSERT INTO account_linking_settings 
        (table_name, table_display_name, table_description, account_prefix, name_field, default_main_account_id)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (table_name) DO UPDATE SET
        table_display_name = $2, table_description = $3, account_prefix = $4, 
        name_field = $5, default_main_account_id = $6
      `, [setting.table, setting.display, setting.description, setting.prefix, setting.name_field, accountId]);
    }

    // 6. إنشاء الحسابات الفرعية للبيانات الموجودة
    console.log('🔗 إنشاء الحسابات الفرعية...');
    
    // ربط الموكلين
    const clientsAccount = await client.query('SELECT id FROM chart_of_accounts WHERE account_code = $1', ['1121']);
    if (clientsAccount.rows.length > 0) {
      const clients = await client.query('SELECT id, name FROM clients WHERE is_active = true');
      for (const client of clients.rows) {
        const subCode = `1121-${client.id.toString().padStart(4, '0')}`;
        await client.query(`
          INSERT INTO account_sub_links 
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
          ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING
        `, [clientsAccount.rows[0].id, 'clients', client.id, subCode, `حساب الموكل: ${client.name}`, 'النظام']);
      }
    }

    // ربط الموظفين
    const employeesAccount = await client.query('SELECT id FROM chart_of_accounts WHERE account_code = $1', ['1122']);
    if (employeesAccount.rows.length > 0) {
      const employees = await client.query('SELECT id, name FROM employees WHERE is_active = true');
      for (const employee of employees.rows) {
        const subCode = `1122-${employee.id.toString().padStart(4, '0')}`;
        await client.query(`
          INSERT INTO account_sub_links 
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
          ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING
        `, [employeesAccount.rows[0].id, 'employees', employee.id, subCode, `حساب الموظف: ${employee.name}`, 'النظام']);
      }
    }

    // 7. إنشاء الفهارس للأداء
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_chart_accounts_parent ON chart_of_accounts(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_chart_accounts_type ON chart_of_accounts(account_type)',
      'CREATE INDEX IF NOT EXISTS idx_chart_accounts_linked_table ON chart_of_accounts(linked_table)',
      'CREATE INDEX IF NOT EXISTS idx_sub_links_main_account ON account_sub_links(main_account_id)',
      'CREATE INDEX IF NOT EXISTS idx_sub_links_table ON account_sub_links(linked_table)',
      'CREATE INDEX IF NOT EXISTS idx_sub_links_record ON account_sub_links(linked_record_id)'
    ];

    for (const index of indexes) {
      await client.query(index);
    }

    // 8. إنشاء دوال مساعدة
    await client.query(`
      CREATE OR REPLACE FUNCTION get_account_balance(account_code VARCHAR)
      RETURNS DECIMAL(15,2) AS $$
      BEGIN
        RETURN (SELECT COALESCE(current_balance, 0) FROM chart_of_accounts WHERE account_code = $1);
      END;
      $$ LANGUAGE plpgsql;
    `);

    await client.query(`
      CREATE OR REPLACE FUNCTION create_sub_account(
        p_main_account_id INTEGER,
        p_table_name VARCHAR,
        p_record_id INTEGER,
        p_record_name VARCHAR
      ) RETURNS VARCHAR AS $$
      DECLARE
        v_prefix VARCHAR;
        v_sub_code VARCHAR;
        v_sub_name VARCHAR;
      BEGIN
        SELECT sub_account_prefix INTO v_prefix 
        FROM chart_of_accounts 
        WHERE id = p_main_account_id;
        
        v_sub_code := (SELECT account_code FROM chart_of_accounts WHERE id = p_main_account_id) 
                     || '-' || LPAD(p_record_id::TEXT, 4, '0');
        v_sub_name := 'حساب ' || p_record_name;
        
        INSERT INTO account_sub_links 
        (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
        VALUES (p_main_account_id, p_table_name, p_record_id, v_sub_code, v_sub_name, 'النظام')
        ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING;
        
        RETURN v_sub_code;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // عرض النتائج
    const summary = await client.query(`
      SELECT 
        COUNT(*) as total_accounts,
        COUNT(CASE WHEN is_main_account THEN 1 END) as main_accounts,
        COUNT(CASE WHEN is_control_account THEN 1 END) as control_accounts,
        COUNT(CASE WHEN linked_table IS NOT NULL THEN 1 END) as linked_accounts
      FROM chart_of_accounts
    `);

    const subLinks = await client.query('SELECT COUNT(*) as sub_accounts FROM account_sub_links');

    console.log('✅ تم إنشاء نظام ربط الحسابات المتكامل بنجاح!');
    console.log('📊 الإحصائيات:');
    console.log(`   - إجمالي الحسابات: ${summary.rows[0].total_accounts}`);
    console.log(`   - الحسابات الرئيسية: ${summary.rows[0].main_accounts}`);
    console.log(`   - الحسابات المراقبة: ${summary.rows[0].control_accounts}`);
    console.log(`   - الحسابات المربوطة: ${summary.rows[0].linked_accounts}`);
    console.log(`   - الحسابات الفرعية: ${subLinks.rows[0].sub_accounts}`);

    console.log('🎯 الميزات المتاحة:');
    console.log('   ✅ هيكل شجري متكامل للحسابات');
    console.log('   ✅ ربط تلقائي بالجداول');
    console.log('   ✅ حسابات مراقبة للتحكم');
    console.log('   ✅ أرصدة افتتاحية وجارية');
    console.log('   ✅ دوال مساعدة للعمليات');
    console.log('   ✅ فهارس محسنة للأداء');

  } catch (error) {
    console.error('❌ خطأ في إنشاء النظام:', error.message);
  } finally {
    await client.end();
  }
}

createCompleteAccountLinkingSystem();
