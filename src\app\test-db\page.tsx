'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Database,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Server,
  Key,
  Globe
} from 'lucide-react'

interface TestResult {
  test: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: string
}

export default function TestDatabasePage() {
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])

  const runDatabaseTests = async () => {
    setIsLoading(true)
    setTestResults([])

    try {
      // اختبار 1: اختبار الاتصال الأساسي
      const connectionTest = await fetch('/api/test-connection')
      const connectionResult = await connectionTest.json()
      
      setTestResults(prev => [...prev, {
        test: 'اختبار الاتصال الأساسي',
        status: connectionResult.success ? 'success' : 'error',
        message: connectionResult.message || connectionResult.error,
        details: connectionResult.details
      }])

      // اختبار 2: اختبار وجود قاعدة البيانات
      const dbExistsTest = await fetch('/api/test-database-exists')
      const dbExistsResult = await dbExistsTest.json()
      
      setTestResults(prev => [...prev, {
        test: 'اختبار وجود قاعدة البيانات',
        status: dbExistsResult.success ? 'success' : 'error',
        message: dbExistsResult.message || dbExistsResult.error,
        details: dbExistsResult.details
      }])

      // اختبار 3: اختبار الجداول
      const tablesTest = await fetch('/api/test-tables')
      const tablesResult = await tablesTest.json()
      
      setTestResults(prev => [...prev, {
        test: 'اختبار الجداول',
        status: tablesResult.success ? 'success' : 'warning',
        message: tablesResult.message || tablesResult.error,
        details: tablesResult.details
      }])

      // اختبار 4: اختبار العمليات
      const operationsTest = await fetch('/api/test-operations')
      const operationsResult = await operationsTest.json()
      
      setTestResults(prev => [...prev, {
        test: 'اختبار العمليات',
        status: operationsResult.success ? 'success' : 'error',
        message: operationsResult.message || operationsResult.error,
        details: operationsResult.details
      }])

    } catch (error) {
      setTestResults(prev => [...prev, {
        test: 'خطأ في الشبكة',
        status: 'error',
        message: 'فشل في الاتصال بالخادم',
        details: (error as Error).message
      }])
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Database className="h-8 w-8 mr-3 text-blue-600" />
              اختبار قاعدة البيانات
            </h1>
            <p className="text-gray-600 mt-1">فحص شامل لاتصال وحالة قاعدة البيانات</p>
          </div>
          
          <Button 
            onClick={runDatabaseTests}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                جاري الفحص...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                بدء الفحص
              </>
            )}
          </Button>
        </div>

        {/* معلومات قاعدة البيانات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Server className="h-5 w-5 mr-2" />
              معلومات قاعدة البيانات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Globe className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm text-gray-600">الخادم</div>
                  <div className="font-medium">localhost:5432</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Database className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm text-gray-600">قاعدة البيانات</div>
                  <div className="font-medium">mohammi</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Key className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm text-gray-600">المستخدم</div>
                  <div className="font-medium">postgres</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Server className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm text-gray-600">النوع</div>
                  <div className="font-medium">PostgreSQL</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* نتائج الاختبارات */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2" />
                نتائج الاختبارات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        {getStatusIcon(result.status)}
                        <h3 className="font-medium">{result.test}</h3>
                      </div>
                      <Badge className={getStatusColor(result.status)}>
                        {result.status === 'success' && 'نجح'}
                        {result.status === 'error' && 'فشل'}
                        {result.status === 'warning' && 'تحذير'}
                      </Badge>
                    </div>
                    <p className="text-gray-700 mb-2">{result.message}</p>
                    {result.details && (
                      <div className="bg-gray-50 rounded p-3 text-sm">
                        <strong>التفاصيل:</strong>
                        <pre className="mt-1 whitespace-pre-wrap">{result.details}</pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* إرشادات الإصلاح */}
        <Card>
          <CardHeader>
            <CardTitle>إرشادات الإصلاح</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">1. التأكد من تشغيل PostgreSQL</h4>
                <p className="text-gray-600 text-sm">تأكد من أن خدمة PostgreSQL تعمل على المنفذ 5432</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">2. التأكد من وجود قاعدة البيانات</h4>
                <p className="text-gray-600 text-sm">تأكد من وجود قاعدة بيانات بالاسم "mohammi"</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">3. التأكد من بيانات الاعتماد</h4>
                <p className="text-gray-600 text-sm">تأكد من صحة اسم المستخدم "postgres" وكلمة المرور "yemen123"</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">4. التأكد من الصلاحيات</h4>
                <p className="text-gray-600 text-sm">تأكد من أن المستخدم يملك صلاحيات الوصول لقاعدة البيانات</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
