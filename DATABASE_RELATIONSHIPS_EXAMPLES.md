# 🔗 أمثلة عملية على العلاقات في قاعدة البيانات

## 📋 نظرة عامة
هذا الملف يوضح كيفية استخدام العلاقات المختلفة في النظام القانوني من خلال أمثلة عملية واستعلامات SQL.

---

## 🎯 العلاقات الأساسية مع أمثلة

### 1. **العلاقة: clients → issues (One-to-Many)**

**الوصف**: كل عميل يمكن أن يكون له عدة قضايا

```sql
-- جلب جميع قضايا عميل محدد
SELECT 
    c.name as client_name,
    i.case_number,
    i.title,
    i.status,
    i.amount
FROM clients c
JOIN issues i ON c.id = i.client_id
WHERE c.id = 1;

-- إحصائيات القضايا لكل عميل
SELECT 
    c.name,
    COUNT(i.id) as total_cases,
    SUM(i.amount) as total_amount,
    COUNT(CASE WHEN i.status = 'active' THEN 1 END) as active_cases
FROM clients c
LEFT JOIN issues i ON c.id = i.client_id
GROUP BY c.id, c.name;
```

### 2. **العلاقة: employees → branches → governorates (Many-to-One Chain)**

**الوصف**: كل موظف ينتمي لفرع، وكل فرع ينتمي لمحافظة

```sql
-- جلب الموظفين مع معلومات الفرع والمحافظة
SELECT 
    e.name as employee_name,
    e.position,
    b.name as branch_name,
    g.name as governorate_name
FROM employees e
JOIN branches b ON e.branch_id = b.id
JOIN governorates g ON b.governorate_id = g.id
ORDER BY g.name, b.name, e.name;

-- إحصائيات الموظفين حسب المحافظة
SELECT 
    g.name as governorate,
    COUNT(e.id) as employee_count,
    AVG(e.salary) as avg_salary
FROM governorates g
JOIN branches b ON g.id = b.governorate_id
JOIN employees e ON b.id = e.branch_id
GROUP BY g.id, g.name;
```

### 3. **العلاقة المحاسبية: account_linking_settings → clients/employees**

**الوصف**: ربط العملاء والموظفين بإعدادات الحسابات المحاسبية

```sql
-- جلب العملاء مع معلومات الحساب المحاسبي
SELECT 
    c.name as client_name,
    als.table_display_name,
    als.account_prefix,
    asl.sub_account_code,
    asl.current_balance
FROM clients c
JOIN account_linking_settings als ON c.account_id = als.id
LEFT JOIN account_sub_links asl ON (
    asl.main_account_id = als.default_main_account_id 
    AND asl.linked_table = 'clients' 
    AND asl.linked_record_id = c.id
);

-- إجمالي الأرصدة للعملاء
SELECT 
    als.table_display_name,
    COUNT(c.id) as client_count,
    SUM(asl.current_balance) as total_balance
FROM account_linking_settings als
JOIN clients c ON als.id = c.account_id
LEFT JOIN account_sub_links asl ON (
    asl.linked_table = 'clients' 
    AND asl.linked_record_id = c.id
)
WHERE als.table_name = 'clients'
GROUP BY als.id, als.table_display_name;
```

### 4. **نظام المحادثات: clients ↔ users ↔ conversations ↔ messages**

**الوصف**: نظام محادثات متكامل بين العملاء والمستخدمين

```sql
-- جلب المحادثات مع آخر رسالة
SELECT 
    c.name as client_name,
    u.username as user_name,
    conv.title,
    conv.last_message_at,
    (
        SELECT m.message_text 
        FROM messages m 
        WHERE m.conversation_id = conv.id 
        ORDER BY m.created_at DESC 
        LIMIT 1
    ) as last_message
FROM conversations conv
JOIN clients c ON conv.client_id = c.id
JOIN users u ON conv.user_id = u.id
ORDER BY conv.last_message_at DESC;

-- إحصائيات الرسائل غير المقروءة
SELECT 
    c.name as client_name,
    COUNT(m.id) as unread_messages
FROM clients c
JOIN conversations conv ON c.id = conv.client_id
JOIN messages m ON conv.id = m.conversation_id
WHERE m.is_read = false AND m.sender_type = 'client'
GROUP BY c.id, c.name
HAVING COUNT(m.id) > 0;
```

---

## 🔄 العلاقات المعقدة

### 5. **نظام توزيع الأرباح المتعدد المستويات**

```sql
-- سلسلة التوزيع الكاملة: lineages → case_distribution → service_distributions
SELECT 
    l.name as lineage_name,
    l.admin_percentage,
    cd.admin_amount,
    cd.remaining_amount,
    s.name as service_name,
    sd.percentage as service_percentage,
    sd.amount as service_amount,
    e.name as lawyer_name
FROM lineages l
JOIN case_distribution cd ON l.id = cd.lineage_id
JOIN service_distributions sd ON cd.id = sd.case_distribution_id
JOIN services s ON sd.service_id = s.id
LEFT JOIN employees e ON sd.lawyer_id = e.id
ORDER BY l.name, s.name;

-- إجمالي الأرباح لكل محامي
SELECT 
    e.name as lawyer_name,
    COUNT(sd.id) as service_count,
    SUM(sd.amount) as total_earnings
FROM employees e
JOIN service_distributions sd ON e.id = sd.lawyer_id
GROUP BY e.id, e.name
ORDER BY total_earnings DESC;
```

### 6. **تقرير شامل للقضايا مع جميع التفاصيل**

```sql
-- تقرير متكامل يجمع معلومات من عدة جداول
SELECT 
    i.case_number,
    i.title,
    c.name as client_name,
    c.phone as client_phone,
    it.name as issue_type,
    i.status,
    i.amount,
    i.next_hearing,
    -- معلومات المتابعة
    COUNT(f.id) as follow_count,
    -- معلومات الحركات المالية
    SUM(CASE WHEN m.movement_type = 'income' THEN m.amount ELSE 0 END) as total_income,
    SUM(CASE WHEN m.movement_type = 'expense' THEN m.amount ELSE 0 END) as total_expense,
    -- الرصيد الصافي
    SUM(CASE WHEN m.movement_type = 'income' THEN m.amount ELSE -m.amount END) as net_balance
FROM issues i
JOIN clients c ON i.client_id = c.id
LEFT JOIN issue_types it ON i.issue_type_id = it.id
LEFT JOIN follows f ON i.id = f.case_id
LEFT JOIN movements m ON i.id = m.case_id
GROUP BY i.id, i.case_number, i.title, c.name, c.phone, it.name, i.status, i.amount, i.next_hearing
ORDER BY i.created_date DESC;
```

---

## 🎨 استعلامات متقدمة للتقارير

### 7. **تقرير الأداء الشامل**

```sql
-- تقرير أداء شامل للمكتب
WITH client_stats AS (
    SELECT 
        COUNT(*) as total_clients,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_clients
    FROM clients
),
case_stats AS (
    SELECT 
        COUNT(*) as total_cases,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cases,
        SUM(amount) as total_case_value
    FROM issues
),
financial_stats AS (
    SELECT 
        SUM(CASE WHEN movement_type = 'income' THEN amount ELSE 0 END) as total_income,
        SUM(CASE WHEN movement_type = 'expense' THEN amount ELSE 0 END) as total_expense
    FROM movements
)
SELECT 
    cs.total_clients,
    cs.active_clients,
    cas.total_cases,
    cas.active_cases,
    cas.total_case_value,
    fs.total_income,
    fs.total_expense,
    (fs.total_income - fs.total_expense) as net_profit
FROM client_stats cs, case_stats cas, financial_stats fs;
```

### 8. **تقرير التوزيع الجغرافي**

```sql
-- توزيع الموظفين والعملاء جغرافياً
SELECT 
    g.name as governorate,
    COUNT(DISTINCT b.id) as branch_count,
    COUNT(DISTINCT e.id) as employee_count,
    COUNT(DISTINCT c.id) as client_count,
    AVG(e.salary) as avg_salary
FROM governorates g
LEFT JOIN branches b ON g.id = b.governorate_id
LEFT JOIN employees e ON b.id = e.branch_id
LEFT JOIN clients c ON g.name = SUBSTRING(c.address FROM 1 FOR POSITION(' ' IN c.address) - 1)
GROUP BY g.id, g.name
ORDER BY employee_count DESC;
```

---

## 🔍 استعلامات البحث والفلترة

### 9. **البحث المتقدم في القضايا**

```sql
-- بحث متقدم مع فلاتر متعددة
SELECT DISTINCT
    i.case_number,
    i.title,
    c.name as client_name,
    it.name as issue_type,
    i.status,
    i.amount
FROM issues i
JOIN clients c ON i.client_id = c.id
LEFT JOIN issue_types it ON i.issue_type_id = it.id
LEFT JOIN follows f ON i.id = f.case_id
WHERE 
    (i.title ILIKE '%عقار%' OR i.description ILIKE '%عقار%')
    AND i.status IN ('active', 'pending')
    AND i.amount > 100000
    AND i.created_date >= '2024-01-01'
ORDER BY i.amount DESC;
```

### 10. **تقرير المحادثات النشطة**

```sql
-- المحادثات النشطة مع إحصائيات
SELECT 
    conv.id,
    c.name as client_name,
    u.username as user_name,
    conv.title,
    COUNT(m.id) as message_count,
    COUNT(CASE WHEN m.is_read = false AND m.sender_type = 'client' THEN 1 END) as unread_from_client,
    COUNT(CASE WHEN m.is_read = false AND m.sender_type = 'user' THEN 1 END) as unread_from_user,
    MAX(m.created_at) as last_message_time
FROM conversations conv
JOIN clients c ON conv.client_id = c.id
JOIN users u ON conv.user_id = u.id
LEFT JOIN messages m ON conv.id = m.conversation_id
WHERE conv.status = 'active'
GROUP BY conv.id, c.name, u.username, conv.title
HAVING COUNT(m.id) > 0
ORDER BY last_message_time DESC;
```

---

## 🎯 نصائح لاستخدام العلاقات بكفاءة

### **1. استخدام الفهارس:**
```sql
-- تأكد من وجود فهارس على المفاتيح الخارجية
CREATE INDEX IF NOT EXISTS idx_issues_client_id ON issues(client_id);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
```

### **2. تجنب N+1 Problem:**
```sql
-- بدلاً من استعلامات متعددة، استخدم JOIN
-- ❌ سيء
SELECT * FROM clients;
-- ثم لكل عميل: SELECT * FROM issues WHERE client_id = ?

-- ✅ جيد  
SELECT c.*, i.* FROM clients c LEFT JOIN issues i ON c.id = i.client_id;
```

### **3. استخدام CTE للاستعلامات المعقدة:**
```sql
WITH client_case_summary AS (
    SELECT 
        client_id,
        COUNT(*) as case_count,
        SUM(amount) as total_amount
    FROM issues 
    GROUP BY client_id
)
SELECT 
    c.name,
    ccs.case_count,
    ccs.total_amount
FROM clients c
JOIN client_case_summary ccs ON c.id = ccs.client_id;
```

---

**📝 ملاحظة**: هذه الأمثلة تُظهر القوة الحقيقية للعلاقات في قاعدة البيانات وكيفية استخدامها لإنتاج تقارير وإحصائيات مفيدة للنظام القانوني.

**📅 آخر تحديث**: 2024-12-19  
**🔗 مرتبط بـ**: DATABASE_SCHEMA_DIAGRAM.md
