# بيانات تسجيل الدخول - نظام الإدارة القانونية

## 🔐 بيانات المستخدمين (Users)

### المستخدم الرئيسي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin`
- **نوع الدخول**: مستخدم
- **معرف الجهاز**: اختياري (يمكن تركه فارغاً)

### مستخدمين إضافيين:
1. **اسم المستخدم**: `majed.manager`
   - **كلمة المرور**: `majed.manager`

2. **اسم المستخدم**: `yahya.lawyer`
   - **كلمة المرور**: `yahya.lawyer`

3. **اسم المستخدم**: `ahmed.consultant`
   - **كلمة المرور**: `ahmed.consultant`

4. **اسم المستخدم**: `ahmed.mobile`
   - **كلمة المرور**: `ahmed.mobile`

**ملاحظة مهمة**: كلمة المرور = اسم المستخدم (للاختبار)
- مثال: المستخدم `admin` كلمة المرور `admin`
- مثال: المستخدم `majed.manager` كلمة المرور `majed.manager`

---

## 👥 بيانات العملاء (Clients)

### العملاء التجريبيين:
1. **أحمد محمد سالم**
   - **اسم المستخدم**: `client_8901`
   - **كلمة المرور**: `8901`

2. **شركة النور للتجارة**
   - **اسم المستخدم**: `client_2109`
   - **كلمة المرور**: `2109`

3. **فاطمة علي أحمد**
   - **اسم المستخدم**: `client_3344`
   - **كلمة المرور**: `3344`

4. **مؤسسة الأمل**
   - **اسم المستخدم**: `client_7788`
   - **كلمة المرور**: `7788`

---

## 🌐 روابط النظام

### الصفحات الرئيسية:
- **الصفحة الرئيسية**: http://localhost:7443
- **صفحة تسجيل الدخول**: http://localhost:7443/login
- **لوحة تحكم المستخدمين**: http://localhost:7443/dashboard
- **بوابة العملاء**: http://localhost:7443/client-portal

### صفحات إدارية:
- **إدارة الموكلين**: http://localhost:7443/clients
- **إدارة المستخدمين**: http://localhost:7443/users
- **إدارة القضايا**: http://localhost:7443/cases
- **القوائم المالية**: http://localhost:7443/financial-lists

---

## 📝 تعليمات تسجيل الدخول

### للمستخدمين:
1. اذهب إلى صفحة تسجيل الدخول
2. اختر "دخول مستخدم"
3. أدخل اسم المستخدم وكلمة المرور
4. معرف الجهاز اختياري (يمكن تركه فارغاً أو توليد معرف جديد)
5. اضغط "تسجيل دخول المستخدم"

### للعملاء:
1. اذهب إلى صفحة تسجيل الدخول
2. اختر "دخول عميل"
3. أدخل اسم المستخدم وكلمة المرور
4. اضغط "تسجيل دخول المستخدم"

---

## ⚙️ إعدادات النظام

### قاعدة البيانات:
- **النوع**: PostgreSQL
- **اسم قاعدة البيانات**: `mohammi`
- **المستخدم**: `postgres`
- **كلمة المرور**: `yemen123`
- **المنفذ**: `5432`

### الخادم:
- **المنفذ**: `7443`
- **الرابط المحلي**: http://localhost:7443

---

## 🔒 ملاحظات أمنية

1. **تشفير كلمات المرور**: جميع كلمات المرور مشفرة باستخدام bcrypt
2. **الجلسات الآمنة**: تستخدم JWT tokens مع انتهاء صلاحية 24 ساعة
3. **التحقق من الجهاز**: مُعطل مؤقتاً للتطوير (يمكن تفعيله لاحقاً)
4. **حماية من الهجمات**: نظام محاولات الدخول الفاشلة وقفل الحسابات

---

## 🚀 اختبار المميزات

### نظام المحادثات:
1. سجل دخول كمستخدم أو عميل
2. اضغط على زر المحادثات العائم (أسفل يمين الشاشة)
3. اختبر إرسال الرسائل والتحديث الفوري

### إدارة الموكلين:
1. سجل دخول كمستخدم
2. اذهب إلى صفحة الموكلين
3. اختبر إضافة/تعديل/عرض/حذف الموكلين
4. تأكد من ظهور حقول اسم الدخول وكلمة المرور

### بوابة العملاء:
1. سجل دخول كعميل
2. اعرض لوحة التحكم والإحصائيات
3. اختبر قائمة القضايا والإشعارات

---

## 📞 الدعم الفني

في حالة وجود مشاكل:
1. تأكد من تشغيل خادم PostgreSQL
2. تأكد من تشغيل خادم Next.js على المنفذ 7443
3. تحقق من اتصال قاعدة البيانات
4. راجع console المتصفح للأخطاء

---

**تاريخ آخر تحديث**: 2024-07-14
**إصدار النظام**: 1.0.0
