'use client'

import { useState, useEffect } from 'react'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { AnnouncementBanner } from './announcement-banner'
import { ChatWidget } from '@/components/chat/chat-widget'

interface MainLayoutProps {
  children: React.ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const [userSession, setUserSession] = useState<any>(null)

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed)
  }

  // جلب بيانات المستخدم من localStorage
  useEffect(() => {
    const session = localStorage.getItem('userSession')
    if (session) {
      setUserSession(JSON.parse(session))
    }
  }, [])

  return (
    <div className="flex h-screen bg-gray-100" dir="rtl">
      <Sidebar isCollapsed={isSidebarCollapsed} onToggle={toggleSidebar} />
      <div className="flex-1 flex flex-col overflow-hidden transition-all duration-300">
        <Header />
        <AnnouncementBanner />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>

      {/* مكون المحادثات */}
      <ChatWidget
        userType={userSession?.type || 'user'}
        userId={userSession?.id || 1}
        userName={userSession?.name || 'مستخدم'}
      />
    </div>
  )
}
