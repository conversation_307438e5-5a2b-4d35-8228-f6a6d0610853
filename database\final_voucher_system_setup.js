const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function finalVoucherSystemSetup() {
  try {
    console.log('🏗️ الإعداد النهائي لنظام السندات والقيود...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إصلاح جدول مراكز التكلفة
    console.log('\n🔧 إصلاح جدول مراكز التكلفة...');
    
    // حذف الجدول القديم وإنشاء جديد
    await client.query(`DROP TABLE IF EXISTS cost_centers CASCADE;`);
    
    await client.query(`
      CREATE TABLE cost_centers (
        id SERIAL PRIMARY KEY,
        center_code VARCHAR(20) UNIQUE NOT NULL,
        center_name VARCHAR(255) NOT NULL,
        parent_id INTEGER REFERENCES cost_centers(id),
        center_level INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        description TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول cost_centers جديد');

    // إضافة بيانات مراكز التكلفة
    await client.query(`
      INSERT INTO cost_centers (center_code, center_name, center_level, description) 
      VALUES 
        ('CC001', 'الإدارة العامة', 1, 'مركز تكلفة الإدارة العامة'),
        ('CC002', 'القسم القانوني', 1, 'مركز تكلفة القسم القانوني'),
        ('CC003', 'المحاسبة والمالية', 1, 'مركز تكلفة المحاسبة والمالية'),
        ('CC004', 'الموارد البشرية', 1, 'مركز تكلفة الموارد البشرية'),
        ('CC005', 'الخدمات العامة', 1, 'مركز تكلفة الخدمات العامة')
    `);
    console.log('   ✅ تم إضافة بيانات مراكز التكلفة');

    // 2. إنشاء الجدول الأب للسندات والقيود
    console.log('\n📋 إنشاء الجدول الأب للسندات والقيود...');
    
    await client.query(`DROP TABLE IF EXISTS financial_transactions CASCADE;`);
    await client.query(`DROP TABLE IF EXISTS vouchers_master CASCADE;`);
    
    await client.query(`
      CREATE TABLE vouchers_master (
        id SERIAL PRIMARY KEY,
        voucher_number VARCHAR(50) UNIQUE NOT NULL,
        voucher_date DATE NOT NULL,
        voucher_type VARCHAR(20) NOT NULL CHECK (voucher_type IN ('سند صرف', 'سند قبض', 'قيد يومي')),
        cost_center_id INTEGER REFERENCES cost_centers(id),
        user_id INTEGER,
        description TEXT,
        total_amount DECIMAL(15,2) DEFAULT 0,
        status VARCHAR(20) DEFAULT 'مسودة' CHECK (status IN ('مسودة', 'معتمد', 'ملغي')),
        reference_number VARCHAR(100),
        notes TEXT,
        created_by INTEGER,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_by INTEGER,
        approved_date TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول vouchers_master');

    // 3. إنشاء جدول العمليات المالية
    console.log('\n💰 إنشاء جدول العمليات المالية...');
    
    await client.query(`
      CREATE TABLE financial_transactions (
        id SERIAL PRIMARY KEY,
        voucher_id INTEGER NOT NULL REFERENCES vouchers_master(id) ON DELETE CASCADE,
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        debit_account_id INTEGER REFERENCES chart_of_accounts(id),
        credit_account_id INTEGER REFERENCES chart_of_accounts(id),
        debit_account_code VARCHAR(20),
        credit_account_code VARCHAR(20),
        debit_description TEXT,
        credit_description TEXT,
        transaction_date DATE,
        cost_center_id INTEGER REFERENCES cost_centers(id),
        line_number INTEGER DEFAULT 1,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول financial_transactions');

    // 4. إنشاء الفهارس
    console.log('\n🔍 إنشاء الفهارس...');
    
    const indexes = [
      'CREATE INDEX idx_vouchers_master_date ON vouchers_master(voucher_date);',
      'CREATE INDEX idx_vouchers_master_type ON vouchers_master(voucher_type);',
      'CREATE INDEX idx_vouchers_master_status ON vouchers_master(status);',
      'CREATE INDEX idx_vouchers_master_cost_center ON vouchers_master(cost_center_id);',
      'CREATE INDEX idx_financial_transactions_voucher ON financial_transactions(voucher_id);',
      'CREATE INDEX idx_financial_transactions_date ON financial_transactions(transaction_date);',
      'CREATE INDEX idx_financial_transactions_debit_account ON financial_transactions(debit_account_code);',
      'CREATE INDEX idx_financial_transactions_credit_account ON financial_transactions(credit_account_code);'
    ];

    for (const indexQuery of indexes) {
      await client.query(indexQuery);
    }
    console.log('   ✅ تم إنشاء جميع الفهارس');

    // 5. إنشاء الدوال المساعدة
    console.log('\n⚙️ إنشاء الدوال المساعدة...');
    
    // دالة توليد رقم السند
    await client.query(`
      CREATE OR REPLACE FUNCTION generate_voucher_number(v_type VARCHAR(20))
      RETURNS VARCHAR(50) AS $$
      DECLARE
        prefix VARCHAR(10);
        next_number INTEGER;
        current_year VARCHAR(4);
      BEGIN
        current_year := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;
        
        CASE v_type
          WHEN 'سند صرف' THEN prefix := 'PAY';
          WHEN 'سند قبض' THEN prefix := 'REC';
          WHEN 'قيد يومي' THEN prefix := 'JE';
          ELSE prefix := 'VOC';
        END CASE;
        
        SELECT COALESCE(MAX(
          CAST(SUBSTRING(voucher_number FROM LENGTH(prefix || current_year) + 1) AS INTEGER)
        ), 0) + 1
        INTO next_number
        FROM vouchers_master 
        WHERE voucher_type = v_type 
          AND voucher_number LIKE prefix || current_year || '%';
        
        RETURN prefix || current_year || LPAD(next_number::VARCHAR, 4, '0');
      END;
      $$ LANGUAGE plpgsql;
    `);
    console.log('   ✅ تم إنشاء دالة generate_voucher_number');

    // دالة حساب إجمالي السند
    await client.query(`
      CREATE OR REPLACE FUNCTION calculate_voucher_total(v_id INTEGER)
      RETURNS DECIMAL(15,2) AS $$
      DECLARE
        total_debit DECIMAL(15,2);
        total_credit DECIMAL(15,2);
        final_total DECIMAL(15,2);
      BEGIN
        SELECT 
          COALESCE(SUM(debit_amount), 0),
          COALESCE(SUM(credit_amount), 0)
        INTO total_debit, total_credit
        FROM financial_transactions
        WHERE voucher_id = v_id;
        
        final_total := GREATEST(total_debit, total_credit);
        
        UPDATE vouchers_master 
        SET total_amount = final_total,
            updated_date = CURRENT_TIMESTAMP
        WHERE id = v_id;
        
        RETURN final_total;
      END;
      $$ LANGUAGE plpgsql;
    `);
    console.log('   ✅ تم إنشاء دالة calculate_voucher_total');

    // 6. إضافة بيانات تجريبية
    console.log('\n📝 إضافة بيانات تجريبية...');
    
    // سند قبض
    const recVoucherNumber = await client.query(`SELECT generate_voucher_number('سند قبض') as number`);
    const recVoucher = await client.query(`
      INSERT INTO vouchers_master (
        voucher_number, voucher_date, voucher_type, 
        cost_center_id, description, created_by, status
      ) VALUES ($1, CURRENT_DATE, 'سند قبض', 2, 'قبض أتعاب قضية رقم 2024/001', 1, 'معتمد')
      RETURNING id
    `, [recVoucherNumber.rows[0].number]);

    await client.query(`
      INSERT INTO financial_transactions (
        voucher_id, debit_amount, credit_amount,
        debit_account_code, credit_account_code,
        debit_description, credit_description,
        transaction_date, line_number, cost_center_id
      ) VALUES 
      ($1, 5000, 0, '1111', '', 'قبض نقدي من العميل', '', CURRENT_DATE, 1, 2),
      ($1, 0, 5000, '', '411', '', 'أتعاب قانونية', CURRENT_DATE, 2, 2)
    `, [recVoucher.rows[0].id]);

    console.log(`   ✅ تم إضافة سند قبض: ${recVoucherNumber.rows[0].number}`);

    // سند صرف
    const payVoucherNumber = await client.query(`SELECT generate_voucher_number('سند صرف') as number`);
    const payVoucher = await client.query(`
      INSERT INTO vouchers_master (
        voucher_number, voucher_date, voucher_type, 
        cost_center_id, description, created_by, status
      ) VALUES ($1, CURRENT_DATE, 'سند صرف', 2, 'صرف مصروفات محكمة', 1, 'معتمد')
      RETURNING id
    `, [payVoucherNumber.rows[0].number]);

    await client.query(`
      INSERT INTO financial_transactions (
        voucher_id, debit_amount, credit_amount,
        debit_account_code, credit_account_code,
        debit_description, credit_description,
        transaction_date, line_number, cost_center_id
      ) VALUES 
      ($1, 500, 0, '511', '', 'مصروفات محكمة', '', CURRENT_DATE, 1, 2),
      ($1, 0, 500, '', '1111', '', 'دفع نقدي', CURRENT_DATE, 2, 2)
    `, [payVoucher.rows[0].id]);

    console.log(`   ✅ تم إضافة سند صرف: ${payVoucherNumber.rows[0].number}`);

    // قيد يومي
    const jeVoucherNumber = await client.query(`SELECT generate_voucher_number('قيد يومي') as number`);
    const jeVoucher = await client.query(`
      INSERT INTO vouchers_master (
        voucher_number, voucher_date, voucher_type, 
        cost_center_id, description, created_by, status
      ) VALUES ($1, CURRENT_DATE, 'قيد يومي', 3, 'قيد تسوية نهاية الشهر', 1, 'مسودة')
      RETURNING id
    `, [jeVoucherNumber.rows[0].number]);

    await client.query(`
      INSERT INTO financial_transactions (
        voucher_id, debit_amount, credit_amount,
        debit_account_code, credit_account_code,
        debit_description, credit_description,
        transaction_date, line_number, cost_center_id
      ) VALUES 
      ($1, 1000, 0, '512', '', 'مصروفات فروع', '', CURRENT_DATE, 1, 3),
      ($1, 0, 1000, '', '1112', '', 'من حساب البنك', CURRENT_DATE, 2, 3)
    `, [jeVoucher.rows[0].id]);

    console.log(`   ✅ تم إضافة قيد يومي: ${jeVoucherNumber.rows[0].number}`);

    // 7. تحديث الإجماليات
    console.log('\n🔄 تحديث إجماليات السندات...');
    
    const vouchers = await client.query(`SELECT id FROM vouchers_master`);
    for (const voucher of vouchers.rows) {
      await client.query(`SELECT calculate_voucher_total($1)`, [voucher.id]);
    }
    console.log('   ✅ تم تحديث جميع الإجماليات');

    // 8. عرض ملخص النظام
    console.log('\n📊 ملخص النظام الجديد:');
    
    const summary = await client.query(`
      SELECT 
        voucher_type,
        COUNT(*) as count,
        SUM(total_amount) as total,
        status
      FROM vouchers_master
      GROUP BY voucher_type, status
      ORDER BY voucher_type, status
    `);

    summary.rows.forEach(row => {
      console.log(`   ${row.voucher_type} (${row.status}): ${row.count} سند، إجمالي: ${parseFloat(row.total || 0).toLocaleString()}`);
    });

    const transactionCount = await client.query(`SELECT COUNT(*) as count FROM financial_transactions`);
    const costCenterCount = await client.query(`SELECT COUNT(*) as count FROM cost_centers`);
    
    console.log(`\n   📋 إجمالي السندات: ${vouchers.rows.length}`);
    console.log(`   💰 إجمالي العمليات المالية: ${transactionCount.rows[0].count}`);
    console.log(`   🏢 مراكز التكلفة: ${costCenterCount.rows[0].count}`);

    // 9. عرض مراكز التكلفة
    console.log('\n🏢 مراكز التكلفة المتاحة:');
    const costCenters = await client.query(`
      SELECT id, center_code, center_name, description 
      FROM cost_centers 
      ORDER BY center_code
    `);
    
    costCenters.rows.forEach(center => {
      console.log(`   ${center.id}: ${center.center_code} - ${center.center_name}`);
    });

    console.log('\n✅ تم إنشاء النظام الموحد للسندات والقيود بنجاح!');
    console.log('🎯 الجداول الجاهزة:');
    console.log('   - vouchers_master (الجدول الأب للسندات والقيود)');
    console.log('   - financial_transactions (جدول العمليات المالية)');
    console.log('   - cost_centers (جدول مراكز التكلفة)');

  } catch (error) {
    console.error('❌ خطأ في الإعداد:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  finalVoucherSystemSetup()
    .then(() => {
      console.log('🎉 تم إنجاز الإعداد بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في الإعداد:', error);
      process.exit(1);
    });
}

module.exports = { finalVoucherSystemSetup };
