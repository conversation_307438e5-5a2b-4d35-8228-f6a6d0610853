import { NextRequest, NextResponse } from 'next/server'

// GET - جلب توزيعات القضايا
export async function GET() {
  try {
    // محاكاة بيانات توزيع القضايا
    const sampleData = [
      {
        id: 1,
        issue_id: 1,
        issue_title: 'قضية تجارية - شركة الأمل',
        case_number: 'TC-2024-001',
        case_amount: 120000,
        lineage_id: 1,
        lineage_name: 'نسب القضايا التجارية',
        admin_percentage: 20,
        admin_amount: 24000, // 120000 * 20%
        remaining_amount: 96000, // 120000 - 24000
        service_distributions: [
          {
            service_id: 1,
            service_name: 'اعداد',
            percentage: 30,
            amount: 28800, // 96000 * 30%
            lawyer_id: 1,
            lawyer_name: 'ماجد أحمد'
          },
          {
            service_id: 2,
            service_name: 'جلسة',
            percentage: 25,
            amount: 24000, // 96000 * 25%
            lawyer_id: 2,
            lawyer_name: 'يحي<PERSON> علي'
          },
          {
            service_id: 3,
            service_name: 'متابعة',
            percentage: 25,
            amount: 24000, // 96000 * 25%
            lawyer_id: 3,
            lawyer_name: 'أحم<PERSON> صالح'
          },
          {
            service_id: 4,
            service_name: 'اشراف',
            percentage: 20,
            amount: 19200, // 96000 * 20%
            lawyer_id: 4,
            lawyer_name: 'محمد صالح'
          }
        ],
        created_date: '2024-01-15'
      },
      {
        id: 2,
        issue_id: 2,
        issue_title: 'قضية عقارية - النزاع العقاري',
        case_number: 'RE-2024-002',
        case_amount: 200000,
        lineage_id: 2,
        lineage_name: 'نسب القضايا العقارية',
        admin_percentage: 18,
        admin_amount: 36000, // 200000 * 18%
        remaining_amount: 164000, // 200000 - 36000
        service_distributions: [
          {
            service_id: 1,
            service_name: 'اعداد',
            percentage: 30,
            amount: 49200, // 164000 * 30%
            lawyer_id: 1,
            lawyer_name: 'ماجد أحمد'
          },
          {
            service_id: 2,
            service_name: 'جلسة',
            percentage: 25,
            amount: 41000, // 164000 * 25%
            lawyer_id: 2,
            lawyer_name: 'يحيى علي'
          },
          {
            service_id: 3,
            service_name: 'متابعة',
            percentage: 25,
            amount: 41000, // 164000 * 25%
            lawyer_id: 3,
            lawyer_name: 'أحمد صالح'
          },
          {
            service_id: 4,
            service_name: 'اشراف',
            percentage: 20,
            amount: 32800, // 164000 * 20%
            lawyer_id: 4,
            lawyer_name: 'محمد صالح'
          }
        ],
        created_date: '2024-01-20'
      }
    ]

    return NextResponse.json({
      success: true,
      data: sampleData
    })
  } catch (error) {
    console.error('Error fetching case distributions:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات توزيع القضايا' },
      { status: 500 }
    )
  }
}

// POST - إضافة توزيع قضية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      issue_id,
      lineage_id,
      service_distributions
    } = body

    if (!issue_id || !lineage_id || !service_distributions) {
      return NextResponse.json(
        { success: false, error: 'القضية ومجموعة النسب وتوزيع الخدمات مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من أن مجموع نسب الخدمات يساوي 100%
    const totalPercentage = service_distributions.reduce((sum: number, dist: any) => sum + (dist.percentage || 0), 0)
    if (totalPercentage !== 100) {
      return NextResponse.json(
        { success: false, error: `مجموع نسب الخدمات يجب أن يساوي 100%. المجموع الحالي: ${totalPercentage}%` },
        { status: 400 }
      )
    }

    // محاكاة إضافة توزيع جديد
    const newDistribution = {
      id: Date.now(),
      issue_id,
      lineage_id,
      service_distributions,
      created_date: new Date().toISOString().split('T')[0]
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة توزيع القضية بنجاح',
      data: newDistribution
    })
  } catch (error) {
    console.error('Error creating case distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة توزيع القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث توزيع قضية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      issue_id,
      lineage_id,
      service_distributions
    } = body

    if (!id || !issue_id || !lineage_id || !service_distributions) {
      return NextResponse.json(
        { success: false, error: 'المعرف والقضية ومجموعة النسب وتوزيع الخدمات مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من أن مجموع نسب الخدمات يساوي 100%
    const totalPercentage = service_distributions.reduce((sum: number, dist: any) => sum + (dist.percentage || 0), 0)
    if (totalPercentage !== 100) {
      return NextResponse.json(
        { success: false, error: `مجموع نسب الخدمات يجب أن يساوي 100%. المجموع الحالي: ${totalPercentage}%` },
        { status: 400 }
      )
    }

    // محاكاة تحديث التوزيع
    return NextResponse.json({
      success: true,
      message: 'تم تحديث توزيع القضية بنجاح'
    })
  } catch (error) {
    console.error('Error updating case distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث توزيع القضية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف توزيع قضية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف توزيع القضية مطلوب' },
        { status: 400 }
      )
    }

    // محاكاة حذف التوزيع
    return NextResponse.json({
      success: true,
      message: 'تم حذف توزيع القضية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting case distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف توزيع القضية' },
      { status: 500 }
    )
  }
}
