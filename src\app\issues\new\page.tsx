'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  FileText,
  Save,
  ArrowRight,
  User,
  Building,
  Calendar,
  DollarSign,
  Scale,
  ArrowLeft
} from 'lucide-react'

export default function NewIssuePage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    case_number: '',
    title: '',
    description: '',
    client_name: '',
    client_phone: '',
    client_email: '',
    court_name: '',
    issue_type: '',
    status: 'pending',
    amount: '',
    next_hearing: '',
    notes: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // محاكاة إرسال البيانات
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      alert('تم إنشاء القضية بنجاح!')
      router.push('/issues')
    } catch (error) {
      alert('حدث خطأ أثناء إنشاء القضية')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <FileText className="h-8 w-8 mr-3 text-blue-600" />
              إضافة قضية جديدة
            </h1>
            <p className="text-gray-600 mt-1">إنشاء قضية قانونية جديدة في النظام</p>
          </div>
          
          <Link href="/issues">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة للقائمة
            </Button>
          </Link>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* معلومات القضية الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Scale className="h-5 w-5 mr-2" />
                معلومات القضية الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="case_number">رقم القضية *</Label>
                  <Input
                    id="case_number"
                    name="case_number"
                    value={formData.case_number}
                    onChange={handleInputChange}
                    placeholder="مثال: 2024-001"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="issue_type">نوع القضية *</Label>
                  <select
                    id="issue_type"
                    name="issue_type"
                    value={formData.issue_type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">اختر نوع القضية</option>
                    <option value="تجاري">تجاري</option>
                    <option value="مدني">مدني</option>
                    <option value="جنائي">جنائي</option>
                    <option value="عمالي">عمالي</option>
                    <option value="عقاري">عقاري</option>
                    <option value="أحوال شخصية">أحوال شخصية</option>
                    <option value="إداري">إداري</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="title">عنوان القضية *</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="أدخل عنوان القضية"
                  required
                />
              </div>

              <div>
                <Label htmlFor="description">وصف القضية</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="أدخل وصف تفصيلي للقضية"
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* معلومات الموكل */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                معلومات الموكل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="client_name">اسم الموكل *</Label>
                  <Input
                    id="client_name"
                    name="client_name"
                    value={formData.client_name}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم الموكل"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="client_phone">رقم الهاتف</Label>
                  <Input
                    id="client_phone"
                    name="client_phone"
                    value={formData.client_phone}
                    onChange={handleInputChange}
                    placeholder="أدخل رقم الهاتف"
                    type="tel"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="client_email">البريد الإلكتروني</Label>
                <Input
                  id="client_email"
                  name="client_email"
                  value={formData.client_email}
                  onChange={handleInputChange}
                  placeholder="أدخل البريد الإلكتروني"
                  type="email"
                />
              </div>
            </CardContent>
          </Card>

          {/* معلومات المحكمة والجلسات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="h-5 w-5 mr-2" />
                معلومات المحكمة والجلسات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="court_name">اسم المحكمة *</Label>
                  <Input
                    id="court_name"
                    name="court_name"
                    value={formData.court_name}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم المحكمة"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="next_hearing">تاريخ الجلسة القادمة</Label>
                  <Input
                    id="next_hearing"
                    name="next_hearing"
                    value={formData.next_hearing}
                    onChange={handleInputChange}
                    type="date"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="status">حالة القضية</Label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="pending">معلقة</option>
                    <option value="in_progress">قيد المعالجة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="amount">قيمة القضية (ريال)</Label>
                  <Input
                    id="amount"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    placeholder="أدخل قيمة القضية"
                    type="number"
                    min="0"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ملاحظات إضافية */}
          <Card>
            <CardHeader>
              <CardTitle>ملاحظات إضافية</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="أدخل أي ملاحظات إضافية"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* أزرار الحفظ */}
          <div className="flex justify-end space-x-4 space-x-reverse">
            <Link href="/issues">
              <Button type="button" variant="outline">
                إلغاء
              </Button>
            </Link>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                'جاري الحفظ...'
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ القضية
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </MainLayout>
  )
}
