@font-face {
  font-family: '<PERSON><PERSON><PERSON>';
  src: url('/fonts/<PERSON>-Art-bold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>-Art-Bold';
  src: url('/fonts/<PERSON>-Art-bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* تطبيق الخط على جميع العناصر */
* {
  font-family: '<PERSON>-<PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تطبيق الخط العريض على العناوين */
h1, h2, h3, h4, h5, h6,
.font-bold,
.font-semibold,
.font-medium,
th,
.sidebar-title,
.menu-title {
  font-family: 'Khalid-Art-Bold', '<PERSON>-<PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: bold !important;
}

/* تطبيق الخط على القوائم */
.sidebar,
.menu-item,
.nav-item,
.dropdown-item {
  font-family: '<PERSON>-Art-Bold', '<PERSON>-<PERSON>', 'Segoe UI', Tahoma, <PERSON>, Verdana, sans-serif !important;
}

/* تطبيق الخط على النماذج */
input,
textarea,
select,
button,
.form-control,
.btn {
  font-family: '<PERSON>-Art', 'Khalid-Art-Bold', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تطبيق الخط على الجداول */
table,
td,
th,
.table {
  font-family: 'Khalid-Art', 'Khalid-Art-Bold', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تطبيق الخط على النوافذ المنبثقة */
.modal,
.dialog,
.popup,
.card {
  font-family: 'Khalid-Art', 'Khalid-Art-Bold', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}
