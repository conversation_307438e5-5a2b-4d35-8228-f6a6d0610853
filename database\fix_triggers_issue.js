const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function fixTriggersIssue() {
  try {
    console.log('🔧 إصلاح مشكلة المحفزات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. حذف المحفزات المسببة للمشكلة
    console.log('\n🗑️ حذف المحفزات المسببة للحلقة اللا نهائية...');

    await client.query(`
      DROP TRIGGER IF EXISTS trigger_update_main_balance_on_insert ON chart_of_accounts;
    `);
    console.log('   ✅ تم حذف محفز INSERT');

    await client.query(`
      DROP TRIGGER IF EXISTS trigger_update_main_balance_on_update ON chart_of_accounts;
    `);
    console.log('   ✅ تم حذف محفز UPDATE');

    // 2. حذف الدوال المسببة للمشكلة
    await client.query(`
      DROP FUNCTION IF EXISTS trigger_update_main_account_balance();
    `);
    console.log('   ✅ تم حذف دالة trigger_update_main_account_balance');

    await client.query(`
      DROP FUNCTION IF EXISTS calculate_main_account_balance(integer);
    `);
    console.log('   ✅ تم حذف دالة calculate_main_account_balance القديمة');

    // 3. إنشاء دالة الحساب اليدوي الجديدة
    console.log('\n🔧 إنشاء دالة حساب الأرصدة اليدوية...');

    await client.query(`
      CREATE OR REPLACE FUNCTION calculate_main_account_balance(main_account_id INTEGER)
      RETURNS TABLE(
        opening_balance DECIMAL(15,2),
        current_balance DECIMAL(15,2),
        sub_accounts_count INTEGER
      ) AS $$
      DECLARE
        total_opening DECIMAL(15,2) := 0;
        total_current DECIMAL(15,2) := 0;
        sub_count INTEGER := 0;
      BEGIN
        -- حساب مجموع الأرصدة من جميع الحسابات الفرعية
        WITH RECURSIVE sub_accounts AS (
          -- الحسابات الفرعية المباشرة
          SELECT id, opening_balance, current_balance
          FROM chart_of_accounts
          WHERE parent_id = main_account_id

          UNION ALL

          -- الحسابات الفرعية للحسابات الفرعية
          SELECT c.id, c.opening_balance, c.current_balance
          FROM chart_of_accounts c
          INNER JOIN sub_accounts sa ON c.parent_id = sa.id
        )
        SELECT
          COALESCE(SUM(sa.opening_balance), 0),
          COALESCE(SUM(sa.current_balance), 0),
          COUNT(*)::INTEGER
        INTO total_opening, total_current, sub_count
        FROM sub_accounts sa;

        -- إرجاع النتائج بدون تحديث تلقائي
        RETURN QUERY SELECT total_opening, total_current, sub_count;
      END;
      $$ LANGUAGE plpgsql;
    `);

    console.log('   ✅ تم إنشاء دالة calculate_main_account_balance (بدون تحديث تلقائي)');

    // 4. إنشاء دالة لتحديث جميع الحسابات الرئيسية
    await client.query(`
      CREATE OR REPLACE FUNCTION update_all_main_account_balances()
      RETURNS TABLE(
        acc_code VARCHAR(20),
        acc_name VARCHAR(255),
        old_opening DECIMAL(15,2),
        new_opening DECIMAL(15,2),
        old_current DECIMAL(15,2),
        new_current DECIMAL(15,2),
        sub_count INTEGER
      ) AS $$
      DECLARE
        main_acc RECORD;
        calc_result RECORD;
      BEGIN
        -- تحديث جميع الحسابات الرئيسية
        FOR main_acc IN
          SELECT id, c.account_code, c.account_name, c.opening_balance, c.current_balance
          FROM chart_of_accounts c
          WHERE c.is_main_account = TRUE
          ORDER BY c.account_code
        LOOP
          -- حساب الأرصدة الجديدة
          SELECT * INTO calc_result
          FROM calculate_main_account_balance(main_acc.id);

          -- تحديث الحساب الرئيسي
          UPDATE chart_of_accounts
          SET
            opening_balance = calc_result.opening_balance,
            current_balance = calc_result.current_balance,
            updated_date = CURRENT_TIMESTAMP
          WHERE id = main_acc.id;

          -- إرجاع النتائج
          RETURN QUERY SELECT
            main_acc.account_code,
            main_acc.account_name,
            main_acc.opening_balance,
            calc_result.opening_balance,
            main_acc.current_balance,
            calc_result.current_balance,
            calc_result.sub_accounts_count;
        END LOOP;
      END;
      $$ LANGUAGE plpgsql;
    `);

    console.log('   ✅ تم إنشاء دالة update_all_main_account_balances');

    // 5. تشغيل تحديث الأرصدة
    console.log('\n🔄 تحديث أرصدة الحسابات الرئيسية...');

    const updateResults = await client.query(`
      SELECT * FROM update_all_main_account_balances();
    `);

    updateResults.rows.forEach(result => {
      console.log(`   ✅ ${result.account_code}: ${result.account_name}`);
      console.log(`      └── ${result.sub_count} حساب فرعي`);
      console.log(`      └── رصيد افتتاحي: ${parseFloat(result.old_opening).toLocaleString()} → ${parseFloat(result.new_opening).toLocaleString()}`);
      console.log(`      └── رصيد حالي: ${parseFloat(result.old_current).toLocaleString()} → ${parseFloat(result.new_current).toLocaleString()}`);
    });

    // 6. إضافة بعض البيانات التجريبية للاختبار
    console.log('\n💰 إضافة بيانات تجريبية...');

    const testData = [
      { code: '1111', opening: 50000, current: 75000 },
      { code: '1112', opening: 200000, current: 180000 },
      { code: '1121', opening: 30000, current: 45000 },
      { code: '211', opening: 25000, current: 30000 },
      { code: '31', opening: 100000, current: 100000 }
    ];

    for (const data of testData) {
      await client.query(`
        UPDATE chart_of_accounts
        SET opening_balance = $1, current_balance = $2
        WHERE account_code = $3
      `, [data.opening, data.current, data.code]);

      console.log(`   ✅ ${data.code}: افتتاحي ${data.opening.toLocaleString()}, حالي ${data.current.toLocaleString()}`);
    }

    // 7. إعادة حساب الأرصدة بعد إضافة البيانات
    console.log('\n🔄 إعادة حساب الأرصدة بعد إضافة البيانات...');

    const finalResults = await client.query(`
      SELECT * FROM update_all_main_account_balances();
    `);

    finalResults.rows.forEach(result => {
      console.log(`   📊 ${result.account_code}: ${result.account_name}`);
      console.log(`      └── رصيد افتتاحي: ${parseFloat(result.new_opening).toLocaleString()}`);
      console.log(`      └── رصيد حالي: ${parseFloat(result.new_current).toLocaleString()}`);
    });

    // 8. عرض الميزانية النهائية
    console.log('\n📈 الميزانية النهائية:');

    const finalBalance = await client.query(`
      SELECT
        account_type,
        SUM(current_balance) as total_balance
      FROM chart_of_accounts
      WHERE is_main_account = TRUE
      GROUP BY account_type
      ORDER BY account_type
    `);

    let totalAssets = 0, totalLiabilities = 0, totalEquity = 0, totalRevenue = 0, totalExpenses = 0;

    finalBalance.rows.forEach(row => {
      const balance = parseFloat(row.total_balance);
      console.log(`   ${row.account_type}: ${balance.toLocaleString()}`);

      switch(row.account_type) {
        case 'أصول': totalAssets = balance; break;
        case 'خصوم': totalLiabilities = balance; break;
        case 'حقوق ملكية': totalEquity = balance; break;
        case 'إيرادات': totalRevenue = balance; break;
        case 'مصروفات': totalExpenses = balance; break;
      }
    });

    console.log(`\n   ⚖️ معادلة الميزانية:`);
    console.log(`   الأصول (${totalAssets.toLocaleString()}) = الخصوم (${totalLiabilities.toLocaleString()}) + حقوق الملكية (${totalEquity.toLocaleString()})`);
    console.log(`   الفرق: ${(totalAssets - totalLiabilities - totalEquity).toLocaleString()}`);
    console.log(`   صافي الدخل: ${(totalRevenue - totalExpenses).toLocaleString()}`);

    console.log('\n✅ تم إصلاح مشكلة المحفزات بنجاح!');
    console.log('📝 ملاحظة: الآن يجب تحديث أرصدة الحسابات الرئيسية يدوياً باستخدام:');
    console.log('   SELECT * FROM update_all_main_account_balances();');

  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  fixTriggersIssue()
    .then(() => {
      console.log('🎉 تم إنجاز الإصلاح بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في الإصلاح:', error);
      process.exit(1);
    });
}

module.exports = { fixTriggersIssue };
