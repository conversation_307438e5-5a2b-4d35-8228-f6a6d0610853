# تشغيل LedgerSMB بسيط
# Simple LedgerSMB Runner

param(
    [string]$InstallPath = "E:\mohammi\ledgersmb-install",
    [int]$Port = 8444
)

Write-Host "🚀 تشغيل LedgerSMB المبسط..." -ForegroundColor Green

# التحقق من وجود Docker
try {
    docker --version | Out-Null
    Write-Host "✅ Docker موجود - سنستخدم Docker" -ForegroundColor Green
    
    # تشغيل LedgerSMB باستخدام Docker
    Write-Host "📦 تشغيل LedgerSMB باستخدام Docker..." -ForegroundColor Cyan
    
    # إيقاف أي حاويات موجودة
    docker stop ledgersmb-app 2>$null
    docker stop ledgersmb-db 2>$null
    docker rm ledgersmb-app 2>$null
    docker rm ledgersmb-db 2>$null
    
    # تشغيل قاعدة البيانات
    Write-Host "🗄️ تشغيل قاعدة بيانات PostgreSQL..." -ForegroundColor Cyan
    docker run -d --name ledgersmb-db `
        -e POSTGRES_DB=ledgersmb `
        -e POSTGRES_USER=postgres `
        -e POSTGRES_PASSWORD=postgres123 `
        -p 5433:5432 `
        postgres:13
    
    Start-Sleep -Seconds 10
    
    # تشغيل LedgerSMB
    Write-Host "💼 تشغيل تطبيق LedgerSMB..." -ForegroundColor Cyan
    docker run -d --name ledgersmb-app `
        --link ledgersmb-db:postgres `
        -e POSTGRES_HOST=postgres `
        -e POSTGRES_PORT=5432 `
        -e POSTGRES_DB=ledgersmb `
        -e POSTGRES_USER=postgres `
        -e POSTGRES_PASSWORD=postgres123 `
        -p ${Port}:5762 `
        ledgersmb/ledgersmb:latest
    
    Write-Host "✅ تم تشغيل LedgerSMB باستخدام Docker" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️ Docker غير متوفر - سنستخدم التثبيت المحلي" -ForegroundColor Yellow
    
    # التحقق من وجود التثبيت المحلي
    $ledgerSMBPath = "$InstallPath\LedgerSMB"
    
    if (-not (Test-Path $ledgerSMBPath)) {
        Write-Host "❌ LedgerSMB غير مثبت محلياً" -ForegroundColor Red
        Write-Host "🔧 يرجى تشغيل سكريبت التثبيت أولاً:" -ForegroundColor Yellow
        Write-Host "   PowerShell -ExecutionPolicy Bypass -File `"$InstallPath\download-and-install-ledgersmb.ps1`"" -ForegroundColor White
        exit 1
    }
    
    # تشغيل LedgerSMB محلياً
    Write-Host "💼 تشغيل LedgerSMB محلياً..." -ForegroundColor Cyan
    Set-Location $ledgerSMBPath
    
    # إعداد متغيرات البيئة
    $env:PGUSER = "postgres"
    $env:PGPASSWORD = "postgres123"
    $env:PGDATABASE = "ledgersmb_standalone"
    $env:PGHOST = "localhost"
    $env:PGPORT = "5432"
    
    # بدء خدمة PostgreSQL
    try {
        Start-Service postgresql-x64-15 -ErrorAction SilentlyContinue
        Write-Host "✅ تم بدء خدمة PostgreSQL" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ تعذر بدء خدمة PostgreSQL" -ForegroundColor Yellow
    }
    
    # تشغيل LedgerSMB
    Write-Host "🚀 تشغيل خادم LedgerSMB..." -ForegroundColor Cyan
    perl -Ilib starman --port $Port --host 0.0.0.0 --workers 2 bin/ledgersmb-server.psgi
}

Write-Host ""
Write-Host "🌐 LedgerSMB متاح على:" -ForegroundColor Cyan
Write-Host "   المحلي: http://localhost:$Port" -ForegroundColor Yellow
Write-Host "   الخارجي: http://[your-ip]:$Port" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 معلومات تسجيل الدخول الافتراضية:" -ForegroundColor Cyan
Write-Host "   المستخدم: admin" -ForegroundColor White
Write-Host "   كلمة المرور: admin" -ForegroundColor White
Write-Host ""

# فتح المتصفح
$response = Read-Host "هل تريد فتح LedgerSMB في المتصفح؟ (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Start-Process "http://localhost:$Port"
}

Write-Host "✅ LedgerSMB يعمل الآن!" -ForegroundColor Green
