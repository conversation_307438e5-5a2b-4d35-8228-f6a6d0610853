// إنشاء جداول نظام المحادثات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function createChatTables() {
  const client = new Client(dbConfig);

  try {
    console.log('🔄 جاري إنشاء جداول نظام المحادثات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // جدول المحادثات (conversations)
    await client.query(`
      CREATE TABLE IF NOT EXISTS conversations (
        id SERIAL PRIMARY KEY,
        client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255),
        status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_message_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول المحادثات (conversations)');

    // جدول الرسائل (messages)
    await client.query(`
      CREATE TABLE IF NOT EXISTS messages (
        id SERIAL PRIMARY KEY,
        conversation_id INTEGER REFERENCES conversations(id) ON DELETE CASCADE,
        sender_type VARCHAR(10) NOT NULL CHECK (sender_type IN ('user', 'client')),
        sender_id INTEGER NOT NULL,
        message_text TEXT,
        message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'reply')),
        file_url VARCHAR(500),
        file_name VARCHAR(255),
        file_size INTEGER,
        reply_to_message_id INTEGER REFERENCES messages(id) ON DELETE SET NULL,
        is_read BOOLEAN DEFAULT FALSE,
        is_edited BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول الرسائل (messages)');

    // جدول حالة قراءة الرسائل (message_read_status)
    await client.query(`
      CREATE TABLE IF NOT EXISTS message_read_status (
        id SERIAL PRIMARY KEY,
        message_id INTEGER REFERENCES messages(id) ON DELETE CASCADE,
        reader_type VARCHAR(10) NOT NULL CHECK (reader_type IN ('user', 'client')),
        reader_id INTEGER NOT NULL,
        read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(message_id, reader_type, reader_id)
      )
    `);
    console.log('✅ تم إنشاء جدول حالة قراءة الرسائل (message_read_status)');

    // حذف جدول الإشعارات إذا كان موجوداً مع خطأ
    await client.query(`DROP TABLE IF EXISTS notifications CASCADE`);

    // جدول الإشعارات (notifications)
    await client.query(`
      CREATE TABLE notifications (
        id SERIAL PRIMARY KEY,
        recipient_type VARCHAR(10) NOT NULL CHECK (recipient_type IN ('user', 'client')),
        recipient_id INTEGER NOT NULL,
        sender_type VARCHAR(10) NOT NULL CHECK (sender_type IN ('user', 'client')),
        sender_id INTEGER NOT NULL,
        notification_type VARCHAR(20) DEFAULT 'message' CHECK (notification_type IN ('message', 'mention', 'reply')),
        title VARCHAR(255),
        content TEXT,
        related_id INTEGER, -- معرف الرسالة أو المحادثة المرتبطة
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول الإشعارات (notifications)');

    // إضافة فهارس للأداء
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_conversations_client_id ON conversations(client_id)',
      'CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status)',
      'CREATE INDEX IF NOT EXISTS idx_conversations_last_message ON conversations(last_message_at DESC)',

      'CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id)',
      'CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender_type, sender_id)',
      'CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_messages_is_read ON messages(is_read)',
      'CREATE INDEX IF NOT EXISTS idx_messages_reply_to ON messages(reply_to_message_id)',

      'CREATE INDEX IF NOT EXISTS idx_message_read_status_message ON message_read_status(message_id)',
      'CREATE INDEX IF NOT EXISTS idx_message_read_status_reader ON message_read_status(reader_type, reader_id)',

      'CREATE INDEX IF NOT EXISTS idx_notifications_recipient ON notifications(recipient_type, recipient_id)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC)'
    ];

    for (const index of indexes) {
      await client.query(index);
    }
    console.log('✅ تم إضافة الفهارس');

    // إضافة triggers لتحديث last_message_at في المحادثات
    await client.query(`
      CREATE OR REPLACE FUNCTION update_conversation_last_message()
      RETURNS TRIGGER AS $$
      BEGIN
        UPDATE conversations
        SET last_message_at = NEW.created_at,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.conversation_id;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await client.query(`
      DROP TRIGGER IF EXISTS trigger_update_conversation_last_message ON messages;
      CREATE TRIGGER trigger_update_conversation_last_message
        AFTER INSERT ON messages
        FOR EACH ROW
        EXECUTE FUNCTION update_conversation_last_message();
    `);
    console.log('✅ تم إضافة المحفزات (triggers)');

    // إدراج بيانات تجريبية
    console.log('📋 إدراج بيانات تجريبية...');

    // إنشاء محادثة تجريبية
    const conversationResult = await client.query(`
      INSERT INTO conversations (client_id, user_id, title, status)
      SELECT 1, 1, 'استشارة قانونية - أحمد محمد سالم', 'active'
      WHERE EXISTS (SELECT 1 FROM clients WHERE id = 1)
      AND EXISTS (SELECT 1 FROM users WHERE id = 1)
      RETURNING id
    `);

    if (conversationResult.rows.length > 0) {
      const conversationId = conversationResult.rows[0].id;

      // إدراج رسائل تجريبية
      const sampleMessages = [
        {
          sender_type: 'client',
          sender_id: 1,
          message_text: 'السلام عليكم، أحتاج استشارة قانونية بخصوص قضية عمالية',
          message_type: 'text'
        },
        {
          sender_type: 'user',
          sender_id: 1,
          message_text: 'وعليكم السلام ورحمة الله وبركاته، أهلاً وسهلاً بك. يمكنني مساعدتك في القضية العمالية. ما هي تفاصيل القضية؟',
          message_type: 'text'
        },
        {
          sender_type: 'client',
          sender_id: 1,
          message_text: 'تم فصلي من العمل بدون مبرر واضح، وأريد معرفة حقوقي القانونية',
          message_type: 'text'
        }
      ];

      for (const message of sampleMessages) {
        await client.query(`
          INSERT INTO messages (conversation_id, sender_type, sender_id, message_text, message_type)
          VALUES ($1, $2, $3, $4, $5)
        `, [conversationId, message.sender_type, message.sender_id, message.message_text, message.message_type]);
      }

      console.log(`   ✅ تم إنشاء محادثة تجريبية برقم ${conversationId} مع 3 رسائل`);
    }

    // عرض إحصائيات
    const stats = await client.query(`
      SELECT
        (SELECT COUNT(*) FROM conversations) as total_conversations,
        (SELECT COUNT(*) FROM messages) as total_messages,
        (SELECT COUNT(*) FROM notifications) as total_notifications
    `);

    console.log('📊 إحصائيات نظام المحادثات:');
    console.log(`   - إجمالي المحادثات: ${stats.rows[0].total_conversations}`);
    console.log(`   - إجمالي الرسائل: ${stats.rows[0].total_messages}`);
    console.log(`   - إجمالي الإشعارات: ${stats.rows[0].total_notifications}`);

    console.log('🎉 تم إنشاء نظام المحادثات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء جداول المحادثات:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

createChatTables();
