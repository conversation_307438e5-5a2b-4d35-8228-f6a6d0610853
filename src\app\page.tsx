'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'
import {
  BarChart3,
  Users,
  FileText,
  DollarSign,
  Activity,
  CheckCircle,
  ArrowUp,
  Scale,
  Target,
  Calculator
} from 'lucide-react'

// بيانات الإحصائيات
const stats = [
  {
    title: 'إجمالي القضايا',
    value: '156',
    change: '+12%',
    icon: Scale,
    color: 'from-blue-500 to-blue-600'
  },
  {
    title: 'الموكلين النشطين',
    value: '89',
    change: '+8%',
    icon: Users,
    color: 'from-green-500 to-green-600'
  },
  {
    title: 'المعاملات المالية',
    value: '2,340',
    change: '+15%',
    icon: DollarSign,
    color: 'from-purple-500 to-purple-600'
  },
  {
    title: 'القضايا المكتملة',
    value: '78',
    change: '+5%',
    icon: CheckCircle,
    color: 'from-orange-500 to-orange-600'
  }
]

export default function Dashboard() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان الرئيسي */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
            <p className="text-gray-600 mt-1">مرحباً بك في نظام إدارة القضايا القانونية</p>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <Calculator className="h-8 w-8 text-blue-600" />
            <span className="text-lg font-semibold text-blue-600">النظام الشامل</span>
          </div>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <Card key={index} className="shadow-lg border-0 overflow-hidden">
              <CardContent className={`p-6 bg-gradient-to-r ${stat.color} text-white`}>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <div className="text-sm opacity-90">{stat.title}</div>
                    <div className="flex items-center mt-2">
                      <ArrowUp className="h-4 w-4 mr-1" />
                      <span className="text-sm font-medium">{stat.change}</span>
                    </div>
                  </div>
                  <stat.icon className="h-12 w-12 opacity-80" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* ملخص سريع */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              ملخص النظام
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">156</div>
                <div className="text-sm text-gray-600">إجمالي القضايا</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">89</div>
                <div className="text-sm text-gray-600">الموكلين النشطين</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">2.3M</div>
                <div className="text-sm text-gray-600">إجمالي المعاملات المالية</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
