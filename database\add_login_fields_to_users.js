// إضافة حقول تسجيل الدخول لجدول المستخدمين
const { Client } = require('pg');
const bcrypt = require('bcrypt');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function addLoginFieldsToUsers() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري إضافة حقول تسجيل الدخول لجدول المستخدمين...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إضافة أعمدة تسجيل الدخول
    try {
      await client.query(`
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255),
        ADD COLUMN IF NOT EXISTS device_id VARCHAR(100),
        ADD COLUMN IF NOT EXISTS last_login TIMESTAMP,
        ADD COLUMN IF NOT EXISTS last_logout TIMESTAMP,
        ADD COLUMN IF NOT EXISTS last_failed_login TIMESTAMP,
        ADD COLUMN IF NOT EXISTS is_online BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0,
        ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP
      `);
      console.log('✅ تم إضافة أعمدة تسجيل الدخول');
    } catch (error) {
      console.log('⚠️  أعمدة تسجيل الدخول موجودة بالفعل');
    }

    // إنشاء كلمات مرور افتراضية للمستخدمين الموجودين
    const existingUsers = await client.query(`
      SELECT id, username, employee_id 
      FROM users 
      WHERE password_hash IS NULL
    `);

    console.log(`📋 تحديث ${existingUsers.rows.length} مستخدم بكلمات مرور افتراضية...`);

    for (const userRow of existingUsers.rows) {
      // كلمة مرور افتراضية (اسم المستخدم أو 123456)
      const defaultPassword = userRow.username || '123456';
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);

      await client.query(`
        UPDATE users 
        SET password_hash = $1 
        WHERE id = $2
      `, [hashedPassword, userRow.id]);
      
      console.log(`   ✅ تم تحديث المستخدم: ${userRow.username} - كلمة المرور: ${defaultPassword}`);
    }

    // إضافة فهارس للأداء
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_users_device_id ON users(device_id)',
      'CREATE INDEX IF NOT EXISTS idx_users_online ON users(is_online)',
      'CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login)'
    ];

    for (const index of indexes) {
      await client.query(index);
    }
    console.log('✅ تم إضافة الفهارس');

    // عرض النتائج
    const summary = await client.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN password_hash IS NOT NULL THEN 1 END) as users_with_password,
        COUNT(CASE WHEN is_online = true THEN 1 END) as online_users
      FROM users
    `);

    console.log('📊 ملخص المستخدمين:');
    console.log(`   - إجمالي المستخدمين: ${summary.rows[0].total_users}`);
    console.log(`   - المستخدمين مع كلمة مرور: ${summary.rows[0].users_with_password}`);
    console.log(`   - المستخدمين المتصلين: ${summary.rows[0].online_users}`);

    // عرض عينة من المستخدمين
    const sampleUsers = await client.query(`
      SELECT username, employee_id 
      FROM users 
      WHERE password_hash IS NOT NULL 
      LIMIT 5
    `);

    console.log('👥 عينة من المستخدمين:');
    sampleUsers.rows.forEach(row => {
      console.log(`   - ${row.username} (موظف: ${row.employee_id || 'غير محدد'})`);
    });

    console.log('🎉 تم إضافة حقول تسجيل الدخول للمستخدمين بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الحقول:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

addLoginFieldsToUsers();
