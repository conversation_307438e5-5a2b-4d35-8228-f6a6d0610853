import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع سندات القبض
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')

    let sql = `
      SELECT
        je.*,
        je.entry_number as voucher_number,
        je.entry_date as voucher_date,
        c.currency_code,
        c.symbol as currency_symbol,
        pm.method_name as payment_method_name,
        cc.center_name as cost_center_name,
        u.username as created_by_username,
        debit_acc.account_name as account_name,
        debit_acc.account_code as account_code,
        credit_acc.account_name as payer_account_name,
        credit_acc.account_code as payer_account_code
      FROM journal_entries je
      LEFT JOIN currencies c ON je.currency_id = c.id
      LEFT JOIN payment_methods pm ON je.payment_method_id = pm.id
      LEFT JOIN cost_centers cc ON je.cost_center_id = cc.id
      LEFT JOIN users u ON je.created_by_user_id = u.id
      -- جلب الحساب المدين من التفاصيل (السطر الأول)
      LEFT JOIN journal_entry_details jed_debit ON je.id = jed_debit.journal_entry_id
        AND jed_debit.line_number = 1 AND jed_debit.debit_amount > 0
      LEFT JOIN chart_of_accounts debit_acc ON jed_debit.account_id = debit_acc.id
      -- جلب الحساب الدائن من التفاصيل (السطر الثاني)
      LEFT JOIN journal_entry_details jed_credit ON je.id = jed_credit.journal_entry_id
        AND jed_credit.line_number = 2 AND jed_credit.credit_amount > 0
      LEFT JOIN chart_of_accounts credit_acc ON jed_credit.account_id = credit_acc.id
      WHERE je.voucher_type = 'receipt'
    `

    const params: any[] = []
    let paramIndex = 1

    // تصفية حسب الحالة
    if (status && status !== 'all') {
      sql += ` AND je.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // تصفية حسب التاريخ
    if (dateFrom) {
      sql += ` AND je.entry_date >= $${paramIndex}`
      params.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      sql += ` AND je.entry_date <= $${paramIndex}`
      params.push(dateTo)
      paramIndex++
    }

    sql += ` ORDER BY je.entry_date DESC, je.entry_number DESC`

    const result = await query(sql, params)

    return NextResponse.json({
      success: true,
      vouchers: result.rows,
      total: result.rows.length,
      message: 'تم جلب سندات القبض بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب سندات القبض:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب سندات القبض',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة سند قبض جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('📥 بيانات سند القبض المستلمة:', body)
    const {
      entry_date,
      voucher_date = entry_date, // للتوافق مع الواجهة الجديدة
      payer_name,
      payer_type = 'external',
      payer_id,
      debit_account_id,
      credit_account_id: rawCreditAccountId,
      amount,
      currency_id = 1,
      payment_method_id,
      cost_center_id,
      description,
      reference_number,
      case_id,
      case_number,
      created_by_user_id = 1, // TODO: الحصول على المستخدم الحالي
      created_by_name = 'النظام'
    } = body

    // معالجة credit_account_id إذا كان يحتوي على نص مثل "employees_1"
    let credit_account_id = rawCreditAccountId
    if (typeof rawCreditAccountId === 'string' && rawCreditAccountId.includes('_')) {
      const parts = rawCreditAccountId.split('_')
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        // هذا حساب مرتبط (عميل أو موظف)
        let accountCode = ''
        if (parts[0] === 'employees') {
          accountCode = `E${parts[1].padStart(6, '0')}` // E000001
        } else if (parts[0] === 'clients') {
          accountCode = `C${parts[1].padStart(6, '0')}` // C000001
        }

        if (accountCode) {
          const linkedAccountResult = await query(`
            SELECT id FROM chart_of_accounts
            WHERE account_code = $1 AND is_active = true
          `, [accountCode])

          if (linkedAccountResult.rows.length > 0) {
            credit_account_id = linkedAccountResult.rows[0].id
          } else {
            return NextResponse.json({
              success: false,
              error: `الحساب المرتبط غير موجود: ${accountCode}`
            }, { status: 400 })
          }
        }
      }
    } else if (typeof rawCreditAccountId === 'string') {
      credit_account_id = parseInt(rawCreditAccountId)
    }

    console.log('🔄 بعد معالجة الحسابات:', {
      debit_account_id,
      credit_account_id,
      rawCreditAccountId
    })

    console.log('🔍 التحقق من البيانات:', {
      voucher_date,
      payer_name,
      debit_account_id,
      rawCreditAccountId,
      amount,
      description
    })

    // التحقق من صحة البيانات
    if (!voucher_date || !debit_account_id || !rawCreditAccountId || !amount || !description) {
      console.log('❌ بيانات مفقودة')
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة (يجب تحديد الحساب المدين والدائن)'
      }, { status: 400 })
    }

    if (amount <= 0) {
      return NextResponse.json({
        success: false,
        error: 'المبلغ يجب أن يكون أكبر من صفر'
      }, { status: 400 })
    }

    // التحقق من وجود الحساب المدين (الصندوق/البنك)
    const debitAccountCheck = await query(
      'SELECT id, allow_transactions, account_name FROM chart_of_accounts WHERE id = $1 AND is_active = true',
      [debit_account_id]
    )

    if (debitAccountCheck.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب المدين (الصندوق/البنك) غير موجود أو غير نشط'
      }, { status: 400 })
    }

    if (!debitAccountCheck.rows[0].allow_transactions) {
      return NextResponse.json({
        success: false,
        error: 'الحساب المدين لا يقبل معاملات'
      }, { status: 400 })
    }

    // إنشاء رقم السند
    const voucherNumberResult = await query(`
      SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
      FROM journal_entries
      WHERE entry_number LIKE 'RV%'
    `)

    const nextNumber = voucherNumberResult.rows[0].next_number
    const voucher_number = `RV${nextNumber.toString().padStart(6, '0')}`

    // إدراج السند في journal_entries (بالأعمدة الموجودة فقط)
    const result = await query(`
      INSERT INTO journal_entries (
        entry_number, entry_date, description, total_debit, total_credit,
        currency_id, payment_method_id, cost_center_id, case_id,
        voucher_type, payer_name, payer_type, payer_id,
        amount, reference_number,
        created_by_user_id, created_by_name, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, 'draft')
      RETURNING *
    `, [
      voucher_number, voucher_date, description, amount, amount,
      currency_id, payment_method_id, cost_center_id, case_id,
      'receipt', payer_name, payer_type, payer_id,
      amount, reference_number,
      created_by_user_id, created_by_name
    ])

    const voucher = result.rows[0]

    // جلب أسماء الحسابات للبيان الذكي
    const debitAccountName = debitAccountCheck.rows[0].account_name
    const creditAccountResult = await query(
      'SELECT account_name FROM chart_of_accounts WHERE id = $1',
      [credit_account_id]
    )
    const creditAccountName = creditAccountResult.rows[0]?.account_name || 'حساب غير معروف'

    // إنشاء سطرين متوازنين بنفس المبلغ (طبيعة السندات)
    // السطر الأول: الحساب المدين (الصندوق/البنك) - يستلم المبلغ
    await query(`
      INSERT INTO journal_entry_details (
        journal_entry_id, line_number, account_id, debit_amount, credit_amount,
        currency_id, exchange_rate, cost_center_id, payment_method_id,
        description, reference_number
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    `, [
      voucher.id,
      1, // رقم السطر
      debit_account_id,
      amount, // نفس مبلغ السند في المدين
      0,      // صفر في الدائن
      currency_id,
      1, // سعر الصرف
      cost_center_id,
      payment_method_id,
      `عليكم من حساب ${creditAccountName} - ${description}`, // البيان الذكي للمدين
      reference_number
    ])

    // السطر الثاني: الحساب الدائن (الدافع) - يدفع المبلغ
    await query(`
      INSERT INTO journal_entry_details (
        journal_entry_id, line_number, account_id, debit_amount, credit_amount,
        currency_id, exchange_rate, cost_center_id, payment_method_id,
        description, reference_number
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    `, [
      voucher.id,
      2, // رقم السطر
      credit_account_id,
      0,      // صفر في المدين
      amount, // نفس مبلغ السند في الدائن
      currency_id,
      1, // سعر الصرف
      cost_center_id,
      payment_method_id,
      `لكم إلى حساب ${debitAccountName} - ${description}`, // البيان الذكي للدائن
      reference_number
    ])

    return NextResponse.json({
      success: true,
      voucher: voucher,
      message: 'تم إنشاء سند القبض بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء سند القبض:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء سند القبض',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث سند قبض
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      voucher_date,
      payer_name,
      payer_type,
      payer_id,
      account_id,
      amount,
      currency_id,
      payment_method_id,
      cost_center_id,
      description,
      reference_number,
      case_id,
      case_number,
      status
    } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف السند مطلوب'
      }, { status: 400 })
    }

    // التحقق من وجود السند
    const existingVoucher = await query(
      'SELECT id, status FROM receipt_vouchers WHERE id = $1',
      [id]
    )

    if (existingVoucher.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'السند غير موجود'
      }, { status: 404 })
    }

    // منع تعديل السندات المعتمدة
    if (existingVoucher.rows[0].status === 'approved') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن تعديل السند المعتمد'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE receipt_vouchers
      SET
        voucher_date = $2,
        payer_name = $3,
        payer_type = $4,
        payer_id = $5,
        account_id = $6,
        amount = $7,
        currency_id = $8,
        payment_method_id = $9,
        cost_center_id = $10,
        description = $11,
        reference_number = $12,
        case_id = $13,
        case_number = $14,
        status = COALESCE($15, status),
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      id, voucher_date, payer_name, payer_type, payer_id,
      account_id, amount, currency_id, payment_method_id, cost_center_id,
      description, reference_number, case_id, case_number, status
    ])

    return NextResponse.json({
      success: true,
      voucher: result.rows[0],
      message: 'تم تحديث سند القبض بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث سند القبض:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث سند القبض',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف سند قبض
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف السند مطلوب'
      }, { status: 400 })
    }

    // التحقق من وجود السند وحالته
    const existingVoucher = await query(
      'SELECT id, status FROM receipt_vouchers WHERE id = $1',
      [id]
    )

    if (existingVoucher.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'السند غير موجود'
      }, { status: 404 })
    }

    // منع حذف السندات المعتمدة
    if (existingVoucher.rows[0].status === 'approved') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف السند المعتمد'
      }, { status: 400 })
    }

    await query('DELETE FROM receipt_vouchers WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف سند القبض بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف سند القبض:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف سند القبض',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
