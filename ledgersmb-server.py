#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم LedgerSMB بسيط
Simple LedgerSMB Server
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse, parse_qs
import json

class LedgerSMBHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)

    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)

        if self.path == '/' or self.path == '/index.html':
            self.send_arabic_homepage()
        elif self.path == '/general-accounting':
            self.send_general_accounting_page()
        elif self.path == '/accounts-receivable':
            self.send_accounts_receivable_page()
        elif self.path == '/accounts-payable':
            self.send_accounts_payable_page()
        elif self.path == '/inventory':
            self.send_inventory_page()
        elif self.path == '/reports':
            self.send_reports_page()
        elif self.path == '/payroll':
            self.send_payroll_page()
        elif self.path == '/system-info':
            self.send_system_info()
        elif self.path == '/features':
            self.send_features_page()
        else:
            # Default handler
            super().do_GET()

    def send_arabic_homepage(self):
        """Send Arabic homepage"""
        html_content = """<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LedgerSMB - نظام المحاسبة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        .welcome-card h2 {
            color: #4a5568;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .welcome-card p {
            color: #718096;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .module-card-link {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .module-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-right: 5px solid #667eea;
            cursor: pointer;
            position: relative;
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-right: 5px solid #48bb78;
        }

        .card-action {
            position: absolute;
            bottom: 15px;
            left: 30px;
            color: #667eea;
            font-weight: bold;
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .module-card:hover .card-action {
            opacity: 1;
        }

        .module-card h3 {
            color: #2d3748;
            font-size: 1.4rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .module-card p {
            color: #4a5568;
            line-height: 1.6;
        }

        .icon {
            font-size: 1.5rem;
        }

        .status {
            background: #48bb78;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 20px;
            font-weight: bold;
        }

        .server-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            color: white;
            text-align: center;
        }

        .btn {
            background: #667eea;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 مرحباً بك في LedgerSMB</h1>
            <p>نظام المحاسبة المتقدم مفتوح المصدر</p>
        </div>

        <div class="welcome-card">
            <h2>🎯 نظام LedgerSMB يعمل بنجاح!</h2>
            <p>هذا هو نظام LedgerSMB المحاسبي المتكامل. النظام يعمل الآن على الخادم المحلي ويمكنك الوصول إلى جميع الميزات المحاسبية المتقدمة.</p>
            <div class="status">✅ الخادم يعمل بنجاح</div>
        </div>

        <div class="modules-grid">
            <a href="/general-accounting" class="module-card-link">
                <div class="module-card">
                    <h3><span class="icon">📊</span> المحاسبة العامة</h3>
                    <p>دليل الحسابات الشامل، القيود اليومية، ميزان المراجعة، والتقارير المالية الأساسية</p>
                    <div class="card-action">انقر للدخول ←</div>
                </div>
            </a>

            <a href="/accounts-receivable" class="module-card-link">
                <div class="module-card">
                    <h3><span class="icon">🛡️</span> الحسابات المدينة</h3>
                    <p>إدارة العملاء، إصدار الفواتير، تتبع المقبوضات، وإدارة الديون المستحقة</p>
                    <div class="card-action">انقر للدخول ←</div>
                </div>
            </a>

            <a href="/accounts-payable" class="module-card-link">
                <div class="module-card">
                    <h3><span class="icon">💳</span> الحسابات الدائنة</h3>
                    <p>إدارة الموردين، معالجة الفواتير، جدولة المدفوعات، وتتبع الالتزامات</p>
                    <div class="card-action">انقر للدخول ←</div>
                </div>
            </a>

            <a href="/inventory" class="module-card-link">
                <div class="module-card">
                    <h3><span class="icon">📦</span> إدارة المخزون</h3>
                    <p>تتبع المنتجات، إدارة الكميات، تقييم المخزون، وتقارير الحركة</p>
                    <div class="card-action">انقر للدخول ←</div>
                </div>
            </a>

            <a href="/reports" class="module-card-link">
                <div class="module-card">
                    <h3><span class="icon">📈</span> التقارير المالية</h3>
                    <p>قائمة الدخل، الميزانية العمومية، قائمة التدفق النقدي، والتحليلات المالية</p>
                    <div class="card-action">انقر للدخول ←</div>
                </div>
            </a>

            <a href="/payroll" class="module-card-link">
                <div class="module-card">
                    <h3><span class="icon">👥</span> كشوف الرواتب</h3>
                    <p>إدارة بيانات الموظفين، حساب الرواتب، الخصومات والمزايا</p>
                    <div class="card-action">انقر للدخول ←</div>
                </div>
            </a>
        </div>

        <div class="server-info">
            <h3>📡 معلومات الخادم</h3>
            <p><strong>المنفذ:</strong> 8444</p>
            <p><strong>الرابط المحلي:</strong> http://localhost:8444</p>
            <p><strong>الحالة:</strong> يعمل بنجاح ✅</p>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="window.location.reload()">🔄 تحديث الصفحة</button>
                <button class="btn" onclick="window.location.href='/system-info'">ℹ️ معلومات النظام</button>
                <button class="btn" onclick="window.location.href='/features'">📋 الميزات</button>
                <button class="btn" onclick="window.location.href='/'">🏠 الصفحة الرئيسية</button>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 LedgerSMB Server is running successfully!');
        console.log('📡 Server Port: 8444');
        console.log('🌐 Local URL: http://localhost:8444');

        // Add some interactivity
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('click', function() {
                alert('هذه الوحدة: ' + this.querySelector('h3').textContent);
            });
        });
    </script>
</body>
</html>"""

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))

    def send_general_accounting_page(self):
        """Send General Accounting page"""
        html_content = self.get_module_page(
            "📊 المحاسبة العامة",
            "General Accounting",
            [
                {"name": "دليل الحسابات", "desc": "إدارة وتنظيم جميع الحسابات المحاسبية", "icon": "📋"},
                {"name": "القيود اليومية", "desc": "تسجيل العمليات المحاسبية اليومية", "icon": "📝"},
                {"name": "ميزان المراجعة", "desc": "عرض أرصدة جميع الحسابات", "icon": "⚖️"},
                {"name": "الأستاذ العام", "desc": "تفاصيل حركة كل حساب", "icon": "📊"},
                {"name": "إقفال الحسابات", "desc": "إقفال الحسابات في نهاية الفترة", "icon": "🔒"},
            ]
        )
        self.send_html_response(html_content)

    def send_accounts_receivable_page(self):
        """Send Accounts Receivable page"""
        html_content = self.get_module_page(
            "🛡️ الحسابات المدينة",
            "Accounts Receivable",
            [
                {"name": "إدارة العملاء", "desc": "إضافة وتعديل بيانات العملاء", "icon": "👥"},
                {"name": "فواتير المبيعات", "desc": "إصدار فواتير البيع للعملاء", "icon": "🧾"},
                {"name": "المقبوضات", "desc": "تسجيل المبالغ المحصلة من العملاء", "icon": "💰"},
                {"name": "كشف حساب العميل", "desc": "عرض تفاصيل حساب كل عميل", "icon": "📄"},
                {"name": "الديون المتأخرة", "desc": "متابعة الديون المستحقة", "icon": "⏰"},
            ]
        )
        self.send_html_response(html_content)

    def send_accounts_payable_page(self):
        """Send Accounts Payable page"""
        html_content = self.get_module_page(
            "💳 الحسابات الدائنة",
            "Accounts Payable",
            [
                {"name": "إدارة الموردين", "desc": "إضافة وتعديل بيانات الموردين", "icon": "🏢"},
                {"name": "فواتير المشتريات", "desc": "تسجيل فواتير الشراء من الموردين", "icon": "📋"},
                {"name": "المدفوعات", "desc": "تسجيل المبالغ المدفوعة للموردين", "icon": "💸"},
                {"name": "كشف حساب المورد", "desc": "عرض تفاصيل حساب كل مورد", "icon": "📊"},
                {"name": "جدولة المدفوعات", "desc": "تنظيم مواعيد السداد", "icon": "📅"},
            ]
        )
        self.send_html_response(html_content)

    def send_inventory_page(self):
        """Send Inventory page"""
        html_content = self.get_module_page(
            "📦 إدارة المخزون",
            "Inventory Management",
            [
                {"name": "إدارة الأصناف", "desc": "إضافة وتعديل بيانات المنتجات", "icon": "📦"},
                {"name": "حركة المخزون", "desc": "تتبع دخول وخروج البضائع", "icon": "🔄"},
                {"name": "جرد المخزون", "desc": "عمليات الجرد الدورية", "icon": "📊"},
                {"name": "تقييم المخزون", "desc": "حساب قيمة المخزون الحالي", "icon": "💰"},
                {"name": "تقارير المخزون", "desc": "تقارير مفصلة عن حالة المخزون", "icon": "📈"},
            ]
        )
        self.send_html_response(html_content)

    def send_reports_page(self):
        """Send Reports page"""
        html_content = self.get_module_page(
            "📈 التقارير المالية",
            "Financial Reports",
            [
                {"name": "قائمة الدخل", "desc": "تقرير الأرباح والخسائر", "icon": "📊"},
                {"name": "الميزانية العمومية", "desc": "تقرير المركز المالي", "icon": "⚖️"},
                {"name": "قائمة التدفقات النقدية", "desc": "تتبع حركة النقد", "icon": "💰"},
                {"name": "تقارير مخصصة", "desc": "إنشاء تقارير حسب الحاجة", "icon": "🔧"},
                {"name": "التحليل المالي", "desc": "مؤشرات الأداء المالي", "icon": "📈"},
            ]
        )
        self.send_html_response(html_content)

    def send_payroll_page(self):
        """Send Payroll page"""
        html_content = self.get_module_page(
            "👥 كشوف الرواتب",
            "Payroll Management",
            [
                {"name": "إدارة الموظفين", "desc": "بيانات الموظفين والوظائف", "icon": "👤"},
                {"name": "حساب الرواتب", "desc": "احتساب الرواتب الشهرية", "icon": "💰"},
                {"name": "الخصومات والإضافات", "desc": "إدارة البدلات والخصومات", "icon": "📋"},
                {"name": "كشوف الراتب", "desc": "طباعة كشوف رواتب الموظفين", "icon": "🧾"},
                {"name": "التقارير الضريبية", "desc": "تقارير الضرائب والتأمينات", "icon": "📊"},
            ]
        )
        self.send_html_response(html_content)

    def send_system_info(self):
        """Send System Information page"""
        html_content = """<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات النظام - LedgerSMB</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; direction: rtl; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .info-card { background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn { background: #667eea; color: white; padding: 12px 25px; border: none; border-radius: 8px; font-size: 1rem; cursor: pointer; margin: 5px; }
        .btn:hover { background: #5a67d8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ℹ️ معلومات النظام</h1>
        </div>
        <div class="info-card">
            <h2>🚀 LedgerSMB - نظام المحاسبة المتقدم</h2>
            <p><strong>الإصدار:</strong> 1.0.0</p>
            <p><strong>المنفذ:</strong> 8444</p>
            <p><strong>الحالة:</strong> يعمل بنجاح ✅</p>
            <p><strong>اللغة:</strong> العربية</p>
            <p><strong>نوع الخادم:</strong> Python HTTP Server</p>
            <p><strong>التاريخ:</strong> """ + str(__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')) + """</p>
        </div>
        <div style="text-align: center;">
            <button class="btn" onclick="window.location.href='/'">🏠 العودة للرئيسية</button>
        </div>
    </div>
</body>
</html>"""
        self.send_html_response(html_content)

    def send_features_page(self):
        """Send Features page"""
        html_content = """<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الميزات - LedgerSMB</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; direction: rtl; }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .feature-card { background: white; border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn { background: #667eea; color: white; padding: 12px 25px; border: none; border-radius: 8px; font-size: 1rem; cursor: pointer; margin: 5px; }
        .btn:hover { background: #5a67d8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 ميزات النظام</h1>
        </div>
        <div class="features-grid">
            <div class="feature-card">
                <h3>🔒 الأمان</h3>
                <p>نظام أمان متقدم لحماية البيانات المالية</p>
            </div>
            <div class="feature-card">
                <h3>📊 التقارير</h3>
                <p>تقارير مالية شاملة ومفصلة</p>
            </div>
            <div class="feature-card">
                <h3>🌐 متعدد اللغات</h3>
                <p>دعم اللغة العربية والإنجليزية</p>
            </div>
            <div class="feature-card">
                <h3>💾 النسخ الاحتياطي</h3>
                <p>نظام نسخ احتياطي تلقائي</p>
            </div>
        </div>
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="window.location.href='/'">🏠 العودة للرئيسية</button>
        </div>
    </div>
</body>
</html>"""
        self.send_html_response(html_content)

    def get_module_page(self, title, subtitle, features):
        """Generate a module page"""
        features_html = ""
        for feature in features:
            features_html += f"""
            <div class="feature-item">
                <div class="feature-icon">{feature['icon']}</div>
                <div class="feature-content">
                    <h3>{feature['name']}</h3>
                    <p>{feature['desc']}</p>
                </div>
            </div>"""

        return f"""<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title} - LedgerSMB</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; direction: rtl; }}
        .container {{ max-width: 1000px; margin: 0 auto; padding: 20px; }}
        .header {{ text-align: center; color: white; margin-bottom: 30px; }}
        .module-card {{ background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }}
        .feature-item {{ display: flex; align-items: center; padding: 20px; margin: 10px 0; background: #f8f9fa; border-radius: 10px; transition: transform 0.2s ease; }}
        .feature-item:hover {{ transform: translateX(-5px); background: #e9ecef; }}
        .feature-icon {{ font-size: 2rem; margin-left: 20px; }}
        .feature-content h3 {{ color: #2d3748; margin-bottom: 5px; }}
        .feature-content p {{ color: #4a5568; }}
        .btn {{ background: #667eea; color: white; padding: 12px 25px; border: none; border-radius: 8px; font-size: 1rem; cursor: pointer; margin: 5px; }}
        .btn:hover {{ background: #5a67d8; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{title}</h1>
            <p>{subtitle}</p>
        </div>
        <div class="module-card">
            <h2>الميزات المتاحة:</h2>
            {features_html}
        </div>
        <div style="text-align: center;">
            <button class="btn" onclick="window.location.href='/'">🏠 العودة للرئيسية</button>
            <button class="btn" onclick="window.history.back()">← رجوع</button>
        </div>
    </div>
</body>
</html>"""

    def send_html_response(self, html_content):
        """Send HTML response"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))

def main():
    PORT = 8444

    print(f"🚀 بدء تشغيل خادم LedgerSMB...")
    print(f"📡 المنفذ: {PORT}")
    print(f"🌐 الرابط المحلي: http://localhost:{PORT}")
    print(f"🔗 الرابط الخارجي: http://[your-ip]:{PORT}")
    print(f"⏹️  للإيقاف: اضغط Ctrl+C")
    print("=" * 50)

    try:
        with socketserver.TCPServer(("", PORT), LedgerSMBHandler) as httpd:
            print(f"✅ الخادم يعمل على المنفذ {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في الخادم: {e}")

if __name__ == "__main__":
    main()
