'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Settings, Database, CheckCircle, AlertCircle, BookOpen } from 'lucide-react'

export default function SetupSimplePage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSetup = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/setup-step-by-step', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      
      if (data.success) {
        setResult(data)
      } else {
        setError(data.error || 'فشل في الإعداد')
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال')
    } finally {
      setLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Settings className="h-8 w-8 mr-3 text-blue-600" />
              إعداد النظام المحاسبي
            </h1>
            <p className="text-gray-600 mt-1">إعداد بسيط وسريع للنظام المحاسبي المدمج</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-6 w-6 mr-2" />
              إعداد قاعدة البيانات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-gray-600">
              سيقوم هذا الإعداد بإنشاء الجداول الأساسية للنظام المحاسبي:
            </div>
            <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
              <li>جدول دليل الحسابات</li>
              <li>جدول القيود العامة</li>
              <li>جدول تفاصيل القيود</li>
              <li>الحسابات الأساسية للمكاتب القانونية</li>
            </ul>

            <div className="pt-4">
              <Button 
                onClick={handleSetup} 
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? 'جاري الإعداد...' : 'بدء الإعداد'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center text-red-800">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span className="font-medium">خطأ في الإعداد:</span>
              </div>
              <div className="mt-2 text-red-700 text-sm">
                {error}
              </div>
            </CardContent>
          </Card>
        )}

        {result && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center text-green-800 mb-4">
                <CheckCircle className="h-5 w-5 mr-2" />
                <span className="font-medium">تم الإعداد بنجاح!</span>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-green-800">خطوات الإعداد:</h4>
                <div className="bg-white p-3 rounded-lg">
                  {result.data.steps.map((step: string, index: number) => (
                    <div key={index} className="text-sm py-1">
                      {step}
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                <div className="text-blue-800 text-sm">
                  ✅ تم إعداد النظام المحاسبي بنجاح. يمكنك الآن استخدام جميع الميزات المحاسبية.
                </div>
                <div className="mt-2 space-x-2 space-x-reverse">
                  <Button asChild className="bg-blue-600 hover:bg-blue-700">
                    <a href="/accounting/chart-of-accounts">
                      <BookOpen className="h-4 w-4 mr-2" />
                      دليل الحسابات
                    </a>
                  </Button>
                  <Button asChild variant="outline">
                    <a href="/dashboard">العودة للوحة التحكم</a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* معلومات إضافية */}
        <Card>
          <CardHeader>
            <CardTitle>ما يتم إعداده</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium mb-2 text-blue-600">الجداول الأساسية</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• دليل الحسابات (chart_of_accounts)</li>
                  <li>• القيود العامة (gl)</li>
                  <li>• تفاصيل القيود (acc_trans)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-2 text-green-600">الحسابات الأساسية</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• النقدية والبنوك</li>
                  <li>• حسابات العملاء والموردين</li>
                  <li>• رأس المال</li>
                  <li>• أتعاب الخدمات القانونية</li>
                  <li>• الرواتب والمصروفات</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
