'use client'

import { useState, useEffect } from 'react'

interface Company {
  id: number
  name: string
  legal_name: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  website: string
  tax_number: string
  commercial_register: string
  logo_url: string
  logo_right_text: string
  logo_left_text: string
  logo_image_url: string
  description: string
}

interface CompanyHeaderProps {
  title?: string
  subtitle?: string
  showLogo?: boolean
  showContactInfo?: boolean
  className?: string
}

export function CompanyHeader({ 
  title = '', 
  subtitle = '', 
  showLogo = true, 
  showContactInfo = true,
  className = ''
}: CompanyHeaderProps) {
  const [company, setCompany] = useState<Company | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // جلب بيانات الشركة
  const fetchCompany = async () => {
    try {
      const response = await fetch('/api/companies')
      const data = await response.json()
      if (data.success && data.data && data.data.length > 0) {
        setCompany(data.data[0])
      }
    } catch (error) {
      console.error('Error fetching company data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCompany()
  }, [])

  if (isLoading) {
    return (
      <div className={`print-header ${className}`}>
        <div className="text-center py-8">جاري تحميل بيانات الشركة...</div>
      </div>
    )
  }

  if (!company) {
    return (
      <div className={`print-header ${className}`}>
        <div className="text-center py-8">لا توجد بيانات للشركة</div>
      </div>
    )
  }

  return (
    <div className={`print-header ${className}`}>
      <style jsx>{`
        .print-header {
          width: 100%;
          margin-bottom: 30px;
          padding: 20px 0;
          border-bottom: 3px solid #2563eb;
          page-break-inside: avoid;
        }

        .header-container {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          min-height: 120px;
          position: relative;
        }

        .logo-right {
          flex: 1;
          text-align: right;
          padding-right: 20px;
          max-width: 30%;
        }

        .logo-center {
          flex: 0 0 auto;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-width: 40%;
        }

        .logo-left {
          flex: 1;
          text-align: left;
          padding-left: 20px;
          max-width: 30%;
        }

        .logo-image {
          max-width: 120px;
          max-height: 120px;
          object-fit: contain;
          margin-bottom: 10px;
        }

        .company-name {
          font-size: 24px;
          font-weight: bold;
          color: #1e40af;
          margin-bottom: 5px;
          line-height: 1.2;
        }

        .company-legal-name {
          font-size: 16px;
          color: #374151;
          margin-bottom: 10px;
          font-style: italic;
        }

        .document-title {
          font-size: 20px;
          font-weight: bold;
          color: #dc2626;
          margin-top: 10px;
          text-decoration: underline;
        }

        .document-subtitle {
          font-size: 14px;
          color: #6b7280;
          margin-top: 5px;
        }

        .logo-text {
          font-size: 12px;
          line-height: 1.4;
          color: #4b5563;
          text-align: justify;
          word-wrap: break-word;
        }

        .contact-info {
          margin-top: 15px;
          padding-top: 15px;
          border-top: 1px solid #e5e7eb;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          font-size: 11px;
          color: #6b7280;
        }

        .contact-item {
          margin: 2px 10px;
        }

        @media print {
          .print-header {
            margin-bottom: 20px;
            padding: 15px 0;
          }
          
          .company-name {
            font-size: 22px;
          }
          
          .document-title {
            font-size: 18px;
          }
          
          .logo-image {
            max-width: 100px;
            max-height: 100px;
          }
          
          .contact-info {
            font-size: 10px;
          }
        }
      `}</style>

      <div className="header-container">
        {/* النص الأيمن */}
        <div className="logo-right">
          {company.logo_right_text && (
            <div className="logo-text">
              {company.logo_right_text}
            </div>
          )}
        </div>

        {/* الوسط - الشعار واسم الشركة */}
        <div className="logo-center">
          {showLogo && company.logo_image_url && (
            <img 
              src={company.logo_image_url} 
              alt="شعار الشركة" 
              className="logo-image"
              onError={(e) => {
                e.currentTarget.style.display = 'none'
              }}
            />
          )}
          
          <div className="company-name">
            {company.name}
          </div>
          
          {company.legal_name && (
            <div className="company-legal-name">
              {company.legal_name}
            </div>
          )}
          
          {title && (
            <div className="document-title">
              {title}
            </div>
          )}
          
          {subtitle && (
            <div className="document-subtitle">
              {subtitle}
            </div>
          )}
        </div>

        {/* النص الأيسر */}
        <div className="logo-left">
          {company.logo_left_text && (
            <div className="logo-text">
              {company.logo_left_text}
            </div>
          )}
        </div>
      </div>

      {/* معلومات الاتصال */}
      {showContactInfo && (
        <div className="contact-info">
          {company.address && (
            <span className="contact-item">
              📍 العنوان: {company.address}
              {company.city && `, ${company.city}`}
              {company.country && `, ${company.country}`}
            </span>
          )}
          
          {company.phone && (
            <span className="contact-item">
              📞 الهاتف: {company.phone}
            </span>
          )}
          
          {company.email && (
            <span className="contact-item">
              📧 البريد: {company.email}
            </span>
          )}
          
          {company.website && (
            <span className="contact-item">
              🌐 الموقع: {company.website}
            </span>
          )}
          
          {company.tax_number && (
            <span className="contact-item">
              🏛️ الرقم الضريبي: {company.tax_number}
            </span>
          )}
          
          {company.commercial_register && (
            <span className="contact-item">
              📋 السجل التجاري: {company.commercial_register}
            </span>
          )}
        </div>
      )}
    </div>
  )
}
