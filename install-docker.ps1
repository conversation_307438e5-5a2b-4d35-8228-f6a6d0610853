# تثبيت Docker Desktop لـ Windows
# Install Docker Desktop for Windows

Write-Host "🐳 تثبيت Docker Desktop..." -ForegroundColor Green

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCur<PERSON>()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ يجب تشغيل هذا السكريبت كمدير" -ForegroundColor Red
    Write-Host "انقر بزر الماوس الأيمن على PowerShell واختر 'تشغيل كمدير'" -ForegroundColor Yellow
    exit 1
}

# التحقق من وجود Docker
try {
    docker --version | Out-Null
    Write-Host "✅ Docker مثبت مسبقاً" -ForegroundColor Green
    docker --version
    exit 0
} catch {
    Write-Host "📦 Docker غير مثبت، سيتم تثبيته..." -ForegroundColor Yellow
}

# تحميل Docker Desktop
$dockerUrl = "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
$dockerInstaller = "$env:TEMP\DockerDesktopInstaller.exe"

Write-Host "⬇️ تحميل Docker Desktop..." -ForegroundColor Cyan
try {
    Invoke-WebRequest -Uri $dockerUrl -OutFile $dockerInstaller -UseBasicParsing
    Write-Host "✅ تم تحميل Docker Desktop" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في تحميل Docker Desktop" -ForegroundColor Red
    Write-Host "يرجى تحميله يدوياً من: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

# تثبيت Docker Desktop
Write-Host "🔧 تثبيت Docker Desktop..." -ForegroundColor Cyan
Write-Host "⚠️ قد يستغرق التثبيت عدة دقائق..." -ForegroundColor Yellow

try {
    Start-Process -FilePath $dockerInstaller -ArgumentList "install --quiet" -Wait
    Write-Host "✅ تم تثبيت Docker Desktop" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في تثبيت Docker Desktop" -ForegroundColor Red
    Write-Host "يرجى تثبيته يدوياً" -ForegroundColor Yellow
    exit 1
}

# تنظيف ملف التثبيت
Remove-Item $dockerInstaller -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 تم تثبيت Docker Desktop بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 الخطوات التالية:" -ForegroundColor Cyan
Write-Host "1. أعد تشغيل الكمبيوتر" -ForegroundColor White
Write-Host "2. شغل Docker Desktop من قائمة ابدأ" -ForegroundColor White
Write-Host "3. انتظر حتى يكتمل تشغيل Docker" -ForegroundColor White
Write-Host "4. شغل سكريبت LedgerSMB: .\run-ledgersmb-docker.ps1" -ForegroundColor White
Write-Host ""

$response = Read-Host "هل تريد إعادة تشغيل الكمبيوتر الآن؟ (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host "🔄 إعادة تشغيل الكمبيوتر..." -ForegroundColor Yellow
    Restart-Computer -Force
} else {
    Write-Host "⚠️ يرجى إعادة تشغيل الكمبيوتر يدوياً لإكمال تثبيت Docker" -ForegroundColor Yellow
}
