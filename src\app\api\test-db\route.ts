import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET() {
  try {
    // اختبار الاتصال بقاعدة البيانات
    const result = await query('SELECT NOW() as current_time')
    
    return NextResponse.json({
      success: true,
      message: 'اتصال قاعدة البيانات يعمل بنجاح',
      data: {
        current_time: result.rows[0].current_time,
        connection: 'successful'
      }
    })
  } catch (error) {
    console.error('Database connection error:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في الاتصال بقاعدة البيانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
