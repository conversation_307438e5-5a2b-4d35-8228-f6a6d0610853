'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Scale,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  MapPin,
  Phone,
  Building
} from 'lucide-react'

interface Court {
  id: number
  name: string
  type: string
  governorate_id: number
  governorate_name: string
  address: string
  phone: string
  is_active: boolean
  created_date: string
}

interface Governorate {
  id: number
  name: string
}

export default function CourtsPage() {
  const [courts, setCourts] = useState<Court[]>([])
  const [governorates, setGovernorates] = useState<Governorate[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingCourt, setEditingCourt] = useState<Court | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    governorate_id: '',
    address: '',
    phone: '',
    is_active: true
  })

  const fetchCourts = async () => {
    setIsLoading(true)
    setDbError(null)
    
    try {
      const response = await fetch('/api/courts')
      const result = await response.json()
      
      if (result.success) {
        setCourts(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات المحاكم')
        setCourts([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setCourts([])
    } finally {
      setIsLoading(false)
    }
  }

  const fetchGovernorates = async () => {
    try {
      const response = await fetch('/api/governorates')
      const result = await response.json()
      
      if (result.success) {
        setGovernorates(result.data)
      }
    } catch (error) {
      console.error('Error fetching governorates:', error)
    }
  }

  useEffect(() => {
    fetchCourts()
    fetchGovernorates()
  }, [])

  const filteredCourts = courts.filter(court =>
    (court.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (court.type || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (court.governorate_name || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه المحكمة؟')) {
      try {
        const response = await fetch(`/api/courts?id=${id}`, {
          method: 'DELETE'
        })
        
        const result = await response.json()
        
        if (result.success) {
          alert('تم حذف المحكمة بنجاح')
          fetchCourts()
        } else {
          alert(result.error || 'فشل في حذف المحكمة')
        }
      } catch (error) {
        console.error('Error deleting court:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  const handleEdit = (court: Court) => {
    setEditingCourt(court)
    setFormData({
      name: court.name,
      type: court.type,
      governorate_id: court.governorate_id.toString(),
      address: court.address,
      phone: court.phone,
      is_active: court.is_active
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleView = (court: Court) => {
    setEditingCourt(court)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingCourt(null)
    setFormData({
      name: '',
      type: '',
      governorate_id: '',
      address: '',
      phone: '',
      is_active: true
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      if (modalType === 'add') {
        const response = await fetch('/api/courts', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })
        
        const result = await response.json()
        
        if (result.success) {
          alert('تم إضافة المحكمة بنجاح')
          fetchCourts()
        } else {
          alert(result.error || 'فشل في إضافة المحكمة')
          return
        }
      } else if (modalType === 'edit' && editingCourt) {
        const response = await fetch('/api/courts', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, id: editingCourt.id })
        })
        
        const result = await response.json()
        
        if (result.success) {
          alert('تم تحديث بيانات المحكمة بنجاح')
          fetchCourts()
        } else {
          alert(result.error || 'فشل في تحديث بيانات المحكمة')
          return
        }
      }

      setIsModalOpen(false)
      setEditingCourt(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const courtTypes = [
    'محكمة عامة',
    'محكمة تجارية',
    'محكمة عمالية',
    'محكمة أحوال شخصية',
    'محكمة جنائية',
    'محكمة إدارية',
    'محكمة استئناف',
    'محكمة عليا'
  ]

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Scale className="h-8 w-8 mr-3 text-blue-600" />
              إدارة المحاكم
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع المحاكم</p>
          </div>
          
          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة محكمة جديدة
          </Button>
        </div>

        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في المحاكم..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* عرض رسالة الخطأ */}
        {dbError && (
          <Card>
            <CardContent className="p-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">خطأ في الاتصال بقاعدة البيانات</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={fetchCourts} variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Scale className="h-5 w-5 mr-2" />
              قائمة المحاكم ({filteredCourts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!dbError && !isLoading && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">اسم المحكمة</th>
                      <th className="text-right p-3 font-semibold">النوع</th>
                      <th className="text-right p-3 font-semibold">المحافظة</th>
                      <th className="text-right p-3 font-semibold">العنوان</th>
                      <th className="text-right p-3 font-semibold">الهاتف</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCourts.map((court) => (
                      <tr key={court.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{court.name}</td>
                        <td className="p-3">{court.type}</td>
                        <td className="p-3">{court.governorate_name}</td>
                        <td className="p-3 flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                          {court.address}
                        </td>
                        <td className="p-3 flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-400" />
                          {court.phone}
                        </td>
                        <td className="text-center p-3">
                          <Badge className={court.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {court.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                        </td>
                        <td className="text-center p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline" onClick={() => handleView(court)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(court)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(court.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {!dbError && !isLoading && courts.length === 0 && (
              <div className="text-center py-8">
                <Scale className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد محاكم</h3>
                <p className="text-gray-600">لم يتم العثور على أي محاكم في قاعدة البيانات</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {modalType === 'add' && 'إضافة محكمة جديدة'}
                  {modalType === 'edit' && 'تعديل بيانات المحكمة'}
                  {modalType === 'view' && 'عرض بيانات المحكمة'}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {modalType === 'view' && editingCourt ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>اسم المحكمة</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingCourt.name}</p>
                    </div>
                    <div>
                      <Label>النوع</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingCourt.type}</p>
                    </div>
                    <div>
                      <Label>المحافظة</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingCourt.governorate_name}</p>
                    </div>
                    <div>
                      <Label>الهاتف</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingCourt.phone}</p>
                    </div>
                  </div>
                  <div>
                    <Label>العنوان</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingCourt.address}</p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">اسم المحكمة *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">نوع المحكمة *</Label>
                      <select
                        id="type"
                        value={formData.type}
                        onChange={(e) => setFormData({...formData, type: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        required
                      >
                        <option value="">اختر نوع المحكمة</option>
                        {courtTypes.map((type) => (
                          <option key={type} value={type}>{type}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="governorate_id">المحافظة *</Label>
                      <select
                        id="governorate_id"
                        value={formData.governorate_id}
                        onChange={(e) => setFormData({...formData, governorate_id: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        required
                      >
                        <option value="">اختر المحافظة</option>
                        {governorates.map((gov) => (
                          <option key={gov.id} value={gov.id}>{gov.name}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="phone">الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">العنوان</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({...formData, address: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="is_active">الحالة</Label>
                    <select
                      id="is_active"
                      value={formData.is_active.toString()}
                      onChange={(e) => setFormData({...formData, is_active: e.target.value === 'true'})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="true">نشط</option>
                      <option value="false">غير نشط</option>
                    </select>
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button type="submit" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)} className="flex-1">
                      إلغاء
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
