'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { 
  Construction, 
  ArrowRight, 
  Home, 
  Clock,
  AlertTriangle,
  Wrench,
  Settings
} from 'lucide-react'

export default function UnderConstructionPage() {
  const router = useRouter()

  const handleGoBack = () => {
    // محاولة العودة للصفحة السابقة
    if (window.history.length > 1) {
      router.back()
    } else {
      // إذا لم تكن هناك صفحة سابقة، اذهب للوحة التحكم
      router.push('/dashboard')
    }
  }

  const handleGoHome = () => {
    router.push('/dashboard')
  }

  return (
    <MainLayout>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <Card className="w-full max-w-2xl shadow-2xl border-0">
          <CardHeader className="text-center pb-2">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Construction className="h-24 w-24 text-orange-500 animate-bounce" />
                <div className="absolute -top-2 -right-2">
                  <Wrench className="h-8 w-8 text-gray-600 animate-spin" style={{ animationDuration: '3s' }} />
                </div>
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-gray-800 mb-2">
              🚧 صفحة قيد الإنشاء
            </CardTitle>
            <p className="text-lg text-gray-600">
              نعتذر، هذه الصفحة قيد التطوير حالياً
            </p>
          </CardHeader>
          
          <CardContent className="text-center space-y-6">
            {/* رسالة توضيحية */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                <span className="font-semibold text-yellow-800">تنبيه</span>
              </div>
              <p className="text-yellow-700 text-sm">
                نحن نعمل بجد لإنجاز هذه الصفحة. سيتم إتاحتها قريباً مع ميزات محسنة ووظائف متقدمة.
              </p>
            </div>

            {/* معلومات إضافية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="bg-blue-50 rounded-lg p-4">
                <Clock className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <h3 className="font-semibold text-blue-800 mb-1">وقت التطوير المتوقع</h3>
                <p className="text-blue-700">قريباً جداً</p>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <Settings className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <h3 className="font-semibold text-green-800 mb-1">الميزات القادمة</h3>
                <p className="text-green-700">واجهة محسنة ووظائف متقدمة</p>
              </div>
            </div>

            {/* أزرار التنقل */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={handleGoBack}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white"
                size="lg"
              >
                <ArrowRight className="h-5 w-5 mr-2 rotate-180" />
                العودة للصفحة السابقة
              </Button>
              <Button 
                onClick={handleGoHome}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                size="lg"
              >
                <Home className="h-5 w-5 mr-2" />
                الذهاب للوحة التحكم
              </Button>
            </div>

            {/* رسالة تشجيعية */}
            <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
              <p className="text-purple-800 font-medium">
                💡 نقدر صبركم ونعدكم بتجربة استثنائية قريباً!
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
