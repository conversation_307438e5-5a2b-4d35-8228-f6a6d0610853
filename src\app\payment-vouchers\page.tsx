'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { AccountSelect } from '@/components/ui/account-select'
import {
  CreditCard,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  Calendar,
  DollarSign,
  FileText
} from 'lucide-react'

interface PaymentVoucher {
  id: number
  voucher_number: string
  date: string
  account_id: string
  account_name: string
  account_type: string
  amount: number
  description: string
  reference: string
  payment_method: string
  status: string
  created_by: string
  created_date: string
}

export default function PaymentVouchersPage() {
  const [vouchers, setVouchers] = useState<PaymentVoucher[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingVoucher, setEditingVoucher] = useState<PaymentVoucher | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    voucher_number: '',
    date: '',
    account_id: '',
    account_name: '',
    amount: '',
    description: '',
    reference: '',
    payment_method: 'cash',
    status: 'pending',
    created_by: ''
  })

  const fetchVouchers = async () => {
    setIsLoading(true)
    setDbError(null)
    
    try {
      const response = await fetch('/api/payment-vouchers')
      const result = await response.json()
      
      if (result.success) {
        setVouchers(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات سندات الدفع')
        setVouchers([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setVouchers([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchVouchers()
  }, [])

  const filteredVouchers = vouchers.filter(voucher => {
    const matchesSearch = (voucher.voucher_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (voucher.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (voucher.account_name || '').toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || voucher.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف سند الدفع هذا؟')) {
      try {
        const response = await fetch(`/api/payment-vouchers?id=${id}`, {
          method: 'DELETE'
        })
        
        const result = await response.json()
        
        if (result.success) {
          alert('تم حذف سند الدفع بنجاح')
          fetchVouchers()
        } else {
          alert(result.error || 'فشل في حذف سند الدفع')
        }
      } catch (error) {
        console.error('Error deleting voucher:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  const handleEdit = (voucher: PaymentVoucher) => {
    setEditingVoucher(voucher)
    setFormData({
      voucher_number: voucher.voucher_number,
      date: voucher.date,
      account_id: voucher.account_id,
      account_name: voucher.account_name,
      amount: voucher.amount.toString(),
      description: voucher.description,
      reference: voucher.reference,
      payment_method: voucher.payment_method,
      status: voucher.status,
      created_by: voucher.created_by
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleView = (voucher: PaymentVoucher) => {
    setEditingVoucher(voucher)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingVoucher(null)
    setFormData({
      voucher_number: '',
      date: new Date().toISOString().split('T')[0],
      account_id: '',
      account_name: '',
      amount: '',
      description: '',
      reference: '',
      payment_method: 'cash',
      status: 'pending',
      created_by: ''
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleAccountChange = (accountId: string, accountData: any) => {
    setFormData({
      ...formData,
      account_id: accountId,
      account_name: accountData ? accountData.name : ''
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      if (modalType === 'add') {
        const response = await fetch('/api/payment-vouchers', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })
        
        const result = await response.json()
        
        if (result.success) {
          alert('تم إضافة سند الدفع بنجاح')
          fetchVouchers()
        } else {
          alert(result.error || 'فشل في إضافة سند الدفع')
          return
        }
      } else if (modalType === 'edit' && editingVoucher) {
        const response = await fetch('/api/payment-vouchers', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, id: editingVoucher.id })
        })
        
        const result = await response.json()
        
        if (result.success) {
          alert('تم تحديث سند الدفع بنجاح')
          fetchVouchers()
        } else {
          alert(result.error || 'فشل في تحديث سند الدفع')
          return
        }
      }

      setIsModalOpen(false)
      setEditingVoucher(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً'
      case 'bank_transfer': return 'تحويل بنكي'
      case 'check': return 'شيك'
      case 'credit_card': return 'بطاقة ائتمان'
      default: return method
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'معتمد'
      case 'pending': return 'في الانتظار'
      case 'rejected': return 'مرفوض'
      default: return status
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'client': return 'bg-blue-100 text-blue-800'
      case 'employee': return 'bg-green-100 text-green-800'
      case 'financial': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAccountTypeText = (type: string) => {
    switch (type) {
      case 'client': return 'موكل'
      case 'employee': return 'موظف'
      case 'financial': return 'حساب مالي'
      default: return 'حساب'
    }
  }

  const stats = {
    total: vouchers.length,
    approved: vouchers.filter(v => v.status === 'approved').length,
    pending: vouchers.filter(v => v.status === 'pending').length,
    totalAmount: vouchers.reduce((sum, v) => sum + (v.amount || 0), 0)
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <CreditCard className="h-8 w-8 mr-3 text-red-600" />
              سندات الدفع
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة سندات الدفع والمصروفات</p>
          </div>
          
          <Button onClick={handleAddNew} className="bg-red-600 hover:bg-red-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة سند دفع جديد
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCard className="h-8 w-8 text-red-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي السندات</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-bold">✓</span>
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">المعتمدة</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.approved}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <span className="text-yellow-600 font-bold">⏳</span>
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">في الانتظار</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.pending}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-red-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي المبالغ</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalAmount.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في سندات الدفع..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="pending">في الانتظار</option>
                  <option value="approved">معتمد</option>
                  <option value="rejected">مرفوض</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* عرض رسالة الخطأ */}
        {dbError && (
          <Card>
            <CardContent className="p-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">خطأ في الاتصال بقاعدة البيانات</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={fetchVouchers} variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              قائمة سندات الدفع ({filteredVouchers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!dbError && !isLoading && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">رقم السند</th>
                      <th className="text-center p-3 font-semibold">التاريخ</th>
                      <th className="text-right p-3 font-semibold">الحساب</th>
                      <th className="text-center p-3 font-semibold">نوع الحساب</th>
                      <th className="text-center p-3 font-semibold">المبلغ</th>
                      <th className="text-right p-3 font-semibold">الوصف</th>
                      <th className="text-center p-3 font-semibold">طريقة الدفع</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredVouchers.map((voucher) => (
                      <tr key={voucher.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{voucher.voucher_number}</td>
                        <td className="text-center p-3">
                          <div className="flex items-center justify-center">
                            <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                            {voucher.date}
                          </div>
                        </td>
                        <td className="p-3">{voucher.account_name}</td>
                        <td className="text-center p-3">
                          <Badge className={getAccountTypeColor(voucher.account_type)}>
                            {getAccountTypeText(voucher.account_type)}
                          </Badge>
                        </td>
                        <td className="text-center p-3">
                          <div className="flex items-center justify-center">
                            <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                            {(voucher.amount || 0).toLocaleString()}
                          </div>
                        </td>
                        <td className="p-3">{voucher.description}</td>
                        <td className="text-center p-3">{getPaymentMethodText(voucher.payment_method)}</td>
                        <td className="text-center p-3">
                          <Badge className={getStatusColor(voucher.status)}>
                            {getStatusText(voucher.status)}
                          </Badge>
                        </td>
                        <td className="text-center p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline" onClick={() => handleView(voucher)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(voucher)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(voucher.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {!dbError && !isLoading && vouchers.length === 0 && (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد سندات دفع</h3>
                <p className="text-gray-600">لم يتم العثور على أي سندات دفع في النظام</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {modalType === 'add' && 'إضافة سند دفع جديد'}
                  {modalType === 'edit' && 'تعديل سند الدفع'}
                  {modalType === 'view' && 'عرض سند الدفع'}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {modalType === 'view' && editingVoucher ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>رقم السند</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingVoucher.voucher_number}</p>
                    </div>
                    <div>
                      <Label>التاريخ</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingVoucher.date}</p>
                    </div>
                    <div>
                      <Label>الحساب</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingVoucher.account_name}</p>
                    </div>
                    <div>
                      <Label>المبلغ</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingVoucher.amount.toLocaleString()} ريال</p>
                    </div>
                    <div>
                      <Label>طريقة الدفع</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{getPaymentMethodText(editingVoucher.payment_method)}</p>
                    </div>
                    <div>
                      <Label>الحالة</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{getStatusText(editingVoucher.status)}</p>
                    </div>
                  </div>
                  <div>
                    <Label>الوصف</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingVoucher.description}</p>
                  </div>
                  <div>
                    <Label>المرجع</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingVoucher.reference}</p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="voucher_number">رقم السند *</Label>
                      <Input
                        id="voucher_number"
                        value={formData.voucher_number}
                        onChange={(e) => setFormData({...formData, voucher_number: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="date">التاريخ *</Label>
                      <Input
                        id="date"
                        type="date"
                        value={formData.date}
                        onChange={(e) => setFormData({...formData, date: e.target.value})}
                        required
                      />
                    </div>
                  </div>

                  {/* قائمة الحسابات الذكية */}
                  <AccountSelect
                    value={formData.account_id}
                    onChange={handleAccountChange}
                    label="الحساب"
                    placeholder="اختر الحساب للدفع إليه"
                    required
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="amount">المبلغ *</Label>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        value={formData.amount}
                        onChange={(e) => setFormData({...formData, amount: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="payment_method">طريقة الدفع</Label>
                      <select
                        id="payment_method"
                        value={formData.payment_method}
                        onChange={(e) => setFormData({...formData, payment_method: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      >
                        <option value="cash">نقداً</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="check">شيك</option>
                        <option value="credit_card">بطاقة ائتمان</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="description">الوصف *</Label>
                    <Input
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="reference">المرجع</Label>
                      <Input
                        id="reference"
                        value={formData.reference}
                        onChange={(e) => setFormData({...formData, reference: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="created_by">المنشئ</Label>
                      <Input
                        id="created_by"
                        value={formData.created_by}
                        onChange={(e) => setFormData({...formData, created_by: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button type="submit" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)} className="flex-1">
                      إلغاء
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
