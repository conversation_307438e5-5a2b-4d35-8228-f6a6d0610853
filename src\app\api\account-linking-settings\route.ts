import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع إعدادات الربط
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        als.*,
        coa.account_name,
        coa.account_code,
        (
          SELECT COUNT(*) 
          FROM account_sub_links asl 
          WHERE asl.main_account_id = als.default_main_account_id
        ) as linked_records_count
      FROM account_linking_settings als
      LEFT JOIN chart_of_accounts coa ON als.default_main_account_id = coa.id
      ORDER BY als.table_display_name
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching account linking settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب إعدادات الربط' },
      { status: 500 }
    )
  }
}

// POST - إنشاء أو تحديث إعدادات الربط
export async function POST(request: NextRequest) {
  try {
    const {
      table_name,
      table_display_name,
      table_description,
      is_enabled,
      auto_create_on_insert,
      account_prefix,
      name_field,
      id_field,
      default_main_account_id
    } = await request.json()

    if (!table_name || !table_display_name || !default_main_account_id) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة غير مكتملة' },
        { status: 400 }
      )
    }

    // التحقق من وجود الحساب الرئيسي
    const accountResult = await query(
      'SELECT id, account_name FROM chart_of_accounts WHERE id = $1',
      [default_main_account_id]
    )

    if (accountResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب الرئيسي غير موجود' },
        { status: 404 }
      )
    }

    // إدراج أو تحديث الإعدادات
    const result = await query(`
      INSERT INTO account_linking_settings 
      (table_name, table_display_name, table_description, is_enabled, 
       auto_create_on_insert, account_prefix, name_field, id_field, default_main_account_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      ON CONFLICT (table_name) DO UPDATE SET
        table_display_name = $2,
        table_description = $3,
        is_enabled = $4,
        auto_create_on_insert = $5,
        account_prefix = $6,
        name_field = $7,
        id_field = $8,
        default_main_account_id = $9
      RETURNING *
    `, [
      table_name,
      table_display_name,
      table_description || '',
      is_enabled !== false,
      auto_create_on_insert !== false,
      account_prefix || '',
      name_field || 'name',
      id_field || 'id',
      default_main_account_id
    ])

    // تحديث الحساب الرئيسي لتفعيل الربط التلقائي
    await query(`
      UPDATE chart_of_accounts 
      SET linked_table = $1, auto_create_sub_accounts = $2
      WHERE id = $3
    `, [table_name, is_enabled !== false, default_main_account_id])

    // إنشاء حسابات فرعية للسجلات الموجودة إذا كان مفعلاً
    if (is_enabled !== false) {
      await createSubAccountsForExistingRecords(default_main_account_id, table_name, name_field || 'name')
    }

    return NextResponse.json({
      success: true,
      message: 'تم حفظ إعدادات الربط بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error saving account linking settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حفظ إعدادات الربط' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إعدادات الربط
export async function DELETE(request: NextRequest) {
  try {
    const { table_name } = await request.json()

    if (!table_name) {
      return NextResponse.json(
        { success: false, error: 'اسم الجدول مطلوب' },
        { status: 400 }
      )
    }

    // الحصول على معرف الحساب الرئيسي قبل الحذف
    const settingsResult = await query(
      'SELECT default_main_account_id FROM account_linking_settings WHERE table_name = $1',
      [table_name]
    )

    if (settingsResult.rows.length > 0) {
      const accountId = settingsResult.rows[0].default_main_account_id

      // إلغاء ربط الحساب الرئيسي
      await query(`
        UPDATE chart_of_accounts 
        SET linked_table = NULL, auto_create_sub_accounts = false
        WHERE id = $1
      `, [accountId])

      // حذف الروابط الفرعية
      await query(
        'DELETE FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2',
        [accountId, table_name]
      )
    }

    // حذف إعدادات الربط
    await query('DELETE FROM account_linking_settings WHERE table_name = $1', [table_name])

    return NextResponse.json({
      success: true,
      message: 'تم حذف إعدادات الربط بنجاح'
    })
  } catch (error) {
    console.error('Error deleting account linking settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف إعدادات الربط' },
      { status: 500 }
    )
  }
}

// دالة مساعدة لإنشاء حسابات فرعية للسجلات الموجودة
async function createSubAccountsForExistingRecords(accountId: number, tableName: string, nameField: string) {
  try {
    // الحصول على كود الحساب الرئيسي
    const accountResult = await query(
      'SELECT account_code, account_name FROM chart_of_accounts WHERE id = $1',
      [accountId]
    )

    if (accountResult.rows.length === 0) return

    const { account_code } = accountResult.rows[0]

    // جلب السجلات من الجدول المحدد
    const dataResult = await query(`SELECT id, ${nameField} as name FROM ${tableName} WHERE is_active = true`)

    // إنشاء حساب فرعي لكل سجل
    for (const record of dataResult.rows) {
      const subAccountCode = `${account_code}-${record.id.toString().padStart(4, '0')}`
      const subAccountName = `${getTableDisplayName(tableName)}: ${record.name}`

      // التحقق من عدم وجود الرابط مسبقاً
      const existingLink = await query(
        'SELECT id FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2 AND linked_record_id = $3',
        [accountId, tableName, record.id]
      )

      if (existingLink.rows.length === 0) {
        await query(`
          INSERT INTO account_sub_links
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
        `, [
          accountId,
          tableName,
          record.id,
          subAccountCode,
          subAccountName,
          'النظام'
        ])
      }
    }
  } catch (error) {
    console.error('Error creating sub accounts for existing records:', error)
  }
}

// دالة مساعدة للحصول على الاسم المعروض للجدول
function getTableDisplayName(tableName: string): string {
  const tableNames: { [key: string]: string } = {
    'clients': 'الموكل',
    'employees': 'الموظف',
    'issues': 'القضية',
    'courts': 'المحكمة',
    'governorates': 'المحافظة',
    'branches': 'الفرع'
  }
  return tableNames[tableName] || tableName
}
