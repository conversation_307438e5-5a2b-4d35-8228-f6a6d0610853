import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب حساب محدد مع تفاصيله
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const accountId = parseInt(params.id)

    // جلب بيانات الحساب
    const accountResult = await query(`
      SELECT 
        c.*,
        p.account_name as parent_name,
        (
          SELECT COUNT(*) 
          FROM chart_of_accounts child 
          WHERE child.parent_id = c.id
        ) as children_count
      FROM chart_of_accounts c
      LEFT JOIN chart_of_accounts p ON c.parent_id = p.id
      WHERE c.id = $1
    `, [accountId])

    if (accountResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    const account = accountResult.rows[0]

    // جلب الحسابات الفرعية إذا وجدت
    let subAccounts = []
    if (account.children_count > 0) {
      const subAccountsResult = await query(`
        SELECT 
          id,
          account_code,
          account_name,
          account_type,
          current_balance,
          is_active
        FROM chart_of_accounts 
        WHERE parent_id = $1
        ORDER BY account_code
      `, [accountId])
      
      subAccounts = subAccountsResult.rows
    }

    // جلب الجداول المرتبطة إذا وجدت
    let linkedData = null
    if (account.linked_table) {
      try {
        switch (account.linked_table) {
          case 'clients':
            const clientsResult = await query(`
              SELECT id, name, phone, email, address
              FROM clients
              ORDER BY name
            `)
            linkedData = {
              table: 'clients',
              label: 'الموكلين',
              data: clientsResult.rows
            }
            break
            
          case 'employees':
            const employeesResult = await query(`
              SELECT id, name, position, phone, email
              FROM employees
              ORDER BY name
            `)
            linkedData = {
              table: 'employees',
              label: 'الموظفين',
              data: employeesResult.rows
            }
            break
        }
      } catch (error) {
        console.error('Error fetching linked data:', error)
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        ...account,
        sub_accounts: subAccounts,
        linked_data: linkedData
      }
    })
  } catch (error) {
    console.error('Error fetching account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الحساب' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حساب
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const accountId = parseInt(params.id)
    const data = await request.json()

    const {
      account_name,
      account_type,
      parent_id,
      account_nature,
      opening_balance,
      notes,
      is_active,
      linked_table
    } = data

    // التحقق من وجود الحساب
    const existingAccount = await query(
      'SELECT id FROM chart_of_accounts WHERE id = $1',
      [accountId]
    )

    if (existingAccount.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    // تحديث الحساب
    const result = await query(`
      UPDATE chart_of_accounts 
      SET 
        account_name = COALESCE($2, account_name),
        account_type = COALESCE($3, account_type),
        parent_id = $4,
        account_nature = COALESCE($5, account_nature),
        opening_balance = COALESCE($6, opening_balance),
        notes = COALESCE($7, notes),
        is_active = COALESCE($8, is_active),
        linked_table = $9,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      accountId, account_name, account_type, parent_id, 
      account_nature, opening_balance, notes, is_active, linked_table
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error updating account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الحساب' },
      { status: 500 }
    )
  }
}

// DELETE - حذف حساب
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const accountId = parseInt(params.id)

    // التحقق من وجود الحساب
    const accountResult = await query(
      'SELECT * FROM chart_of_accounts WHERE id = $1',
      [accountId]
    )

    if (accountResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    const account = accountResult.rows[0]

    // التحقق من وجود حسابات فرعية
    const childrenResult = await query(
      'SELECT COUNT(*) as count FROM chart_of_accounts WHERE parent_id = $1',
      [accountId]
    )

    if (parseInt(childrenResult.rows[0].count) > 0) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف حساب يحتوي على حسابات فرعية' },
        { status: 400 }
      )
    }

    // التحقق من وجود معاملات على الحساب
    // يمكن إضافة فحص المعاملات هنا لاحقاً
    
    // حذف الحساب (أو تعطيله)
    await query(
      'UPDATE chart_of_accounts SET is_active = false, updated_date = CURRENT_TIMESTAMP WHERE id = $1',
      [accountId]
    )

    return NextResponse.json({
      success: true,
      message: 'تم حذف الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error deleting account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الحساب' },
      { status: 500 }
    )
  }
}
