const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function testAccountCodeGeneration() {
  try {
    console.log('🧪 اختبار توليد أرقام الحسابات التلقائي...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. عرض الحسابات الحالية
    console.log('\n📊 الحسابات الحالية:');
    const currentAccounts = await client.query(`
      SELECT 
        account_code,
        account_name,
        account_level,
        parent_id,
        (SELECT account_code FROM chart_of_accounts p WHERE p.id = c.parent_id) as parent_code
      FROM chart_of_accounts c
      ORDER BY account_code
    `);
    
    currentAccounts.rows.forEach(account => {
      const indent = '  '.repeat(account.account_level - 1);
      const parentInfo = account.parent_code ? ` (تحت: ${account.parent_code})` : '';
      console.log(`${indent}${account.account_code}: ${account.account_name}${parentInfo}`);
    });

    // 2. محاكاة توليد أرقام حسابات جديدة
    console.log('\n🔢 محاكاة توليد أرقام الحسابات:');
    
    // دالة محاكاة توليد رقم الحساب (نفس المنطق من الواجهة)
    const generateAccountCode = (parentCode, existingCodes) => {
      // الحصول على الحسابات الفرعية المباشرة
      const directChildCodes = existingCodes.filter(code => 
        code.startsWith(parentCode) && 
        code.length === parentCode.length + 1 &&
        !isNaN(parseInt(code.substring(parentCode.length)))
      );
      
      if (directChildCodes.length === 0) {
        return parentCode + '1';
      }
      
      // استخراج الأرقام المستخدمة
      const usedNumbers = directChildCodes
        .map(code => parseInt(code.substring(parentCode.length)))
        .filter(num => !isNaN(num) && num > 0)
        .sort((a, b) => a - b);
      
      // العثور على أول رقم متاح
      let nextNumber = 1;
      for (const usedNum of usedNumbers) {
        if (nextNumber === usedNum) {
          nextNumber++;
        } else {
          break;
        }
      }
      
      return parentCode + nextNumber;
    };

    // الحصول على جميع أرقام الحسابات الحالية
    const allCodes = currentAccounts.rows.map(acc => acc.account_code);
    
    // اختبار توليد أرقام جديدة للحسابات الرئيسية
    const mainAccounts = ['1', '2', '3', '4', '5'];
    
    for (const mainCode of mainAccounts) {
      const mainAccount = currentAccounts.rows.find(acc => acc.account_code === mainCode);
      if (mainAccount) {
        const newCode = generateAccountCode(mainCode, allCodes);
        console.log(`   ${mainCode} (${mainAccount.account_name}): الرقم الجديد → ${newCode}`);
        
        // إضافة الرقم الجديد للقائمة لمحاكاة الإضافة
        allCodes.push(newCode);
        
        // اختبار إضافة رقم آخر
        const secondCode = generateAccountCode(mainCode, allCodes);
        console.log(`   ${mainCode} (${mainAccount.account_name}): الرقم التالي → ${secondCode}`);
      }
    }

    // 3. اختبار توليد أرقام للحسابات الفرعية
    console.log('\n🌳 اختبار توليد أرقام للحسابات الفرعية:');
    
    const subAccounts = ['11', '111', '1111'];
    for (const subCode of subAccounts) {
      const subAccount = currentAccounts.rows.find(acc => acc.account_code === subCode);
      if (subAccount) {
        const newCode = generateAccountCode(subCode, allCodes);
        console.log(`   ${subCode} (${subAccount.account_name}): الرقم الجديد → ${newCode}`);
        allCodes.push(newCode);
      }
    }

    // 4. اختبار حالات خاصة
    console.log('\n🔍 اختبار حالات خاصة:');
    
    // محاكاة حذف حساب وإعادة إضافة
    console.log('   محاكاة حذف الحساب 12 وإعادة توليد:');
    const codesWithoutTwelve = allCodes.filter(code => code !== '12');
    const regeneratedCode = generateAccountCode('1', codesWithoutTwelve);
    console.log(`   الرقم المُعاد توليده: ${regeneratedCode} (يجب أن يكون 12)`);

    // 5. إنشاء دالة SQL لتوليد الأرقام في قاعدة البيانات
    console.log('\n🔧 إنشاء دالة SQL لتوليد أرقام الحسابات...');
    
    await client.query(`
      CREATE OR REPLACE FUNCTION generate_account_code(parent_account_id INTEGER)
      RETURNS VARCHAR(20) AS $$
      DECLARE
        parent_code VARCHAR(20);
        used_numbers INTEGER[];
        next_number INTEGER := 1;
        used_num INTEGER;
      BEGIN
        -- الحصول على رمز الحساب الأب
        SELECT account_code INTO parent_code
        FROM chart_of_accounts 
        WHERE id = parent_account_id;
        
        IF parent_code IS NULL THEN
          RAISE EXCEPTION 'الحساب الأب غير موجود';
        END IF;
        
        -- الحصول على الأرقام المستخدمة للحسابات الفرعية المباشرة
        SELECT ARRAY(
          SELECT CAST(SUBSTRING(account_code FROM LENGTH(parent_code) + 1) AS INTEGER)
          FROM chart_of_accounts 
          WHERE parent_id = parent_account_id
            AND account_code ~ ('^' || parent_code || '[0-9]+$')
            AND LENGTH(account_code) = LENGTH(parent_code) + 1
          ORDER BY CAST(SUBSTRING(account_code FROM LENGTH(parent_code) + 1) AS INTEGER)
        ) INTO used_numbers;
        
        -- العثور على أول رقم متاح
        FOREACH used_num IN ARRAY used_numbers
        LOOP
          IF next_number = used_num THEN
            next_number := next_number + 1;
          ELSE
            EXIT;
          END IF;
        END LOOP;
        
        RETURN parent_code || next_number;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    console.log('   ✅ تم إنشاء دالة generate_account_code');

    // 6. اختبار الدالة الجديدة
    console.log('\n🧪 اختبار دالة SQL:');
    
    const testParents = await client.query(`
      SELECT id, account_code, account_name
      FROM chart_of_accounts 
      WHERE account_level <= 3
      ORDER BY account_code
      LIMIT 5
    `);
    
    for (const parent of testParents.rows) {
      const result = await client.query(`
        SELECT generate_account_code($1) as new_code
      `, [parent.id]);
      
      console.log(`   ${parent.account_code} (${parent.account_name}): ${result.rows[0].new_code}`);
    }

    // 7. إحصائيات الحسابات حسب المستوى
    console.log('\n📈 إحصائيات الحسابات حسب المستوى:');
    
    const levelStats = await client.query(`
      SELECT 
        account_level,
        COUNT(*) as count,
        MIN(LENGTH(account_code)) as min_code_length,
        MAX(LENGTH(account_code)) as max_code_length
      FROM chart_of_accounts
      GROUP BY account_level
      ORDER BY account_level
    `);
    
    levelStats.rows.forEach(stat => {
      console.log(`   المستوى ${stat.account_level}: ${stat.count} حساب (طول الرمز: ${stat.min_code_length}-${stat.max_code_length})`);
    });

    // 8. التحقق من تسلسل الأرقام
    console.log('\n🔍 التحقق من تسلسل الأرقام:');
    
    const sequenceCheck = await client.query(`
      WITH account_analysis AS (
        SELECT 
          account_code,
          account_name,
          parent_id,
          (SELECT account_code FROM chart_of_accounts p WHERE p.id = c.parent_id) as parent_code,
          CASE 
            WHEN parent_id IS NULL THEN NULL
            ELSE CAST(SUBSTRING(account_code FROM LENGTH((SELECT account_code FROM chart_of_accounts p WHERE p.id = c.parent_id)) + 1) AS INTEGER)
          END as sequence_number
        FROM chart_of_accounts c
        WHERE parent_id IS NOT NULL
      )
      SELECT 
        parent_code,
        COUNT(*) as children_count,
        MIN(sequence_number) as min_seq,
        MAX(sequence_number) as max_seq,
        ARRAY_AGG(sequence_number ORDER BY sequence_number) as all_sequences
      FROM account_analysis
      WHERE parent_code IS NOT NULL
      GROUP BY parent_code
      ORDER BY parent_code
    `);
    
    sequenceCheck.rows.forEach(check => {
      const sequences = check.all_sequences;
      const hasGaps = sequences.some((num, index) => index > 0 && num !== sequences[index - 1] + 1);
      const gapStatus = hasGaps ? '❌ يوجد فجوات' : '✅ متسلسل';
      
      console.log(`   ${check.parent_code}: ${check.children_count} فرعي (${check.min_seq}-${check.max_seq}) ${gapStatus}`);
      if (hasGaps) {
        console.log(`      الأرقام: [${sequences.join(', ')}]`);
      }
    });

    console.log('\n✅ تم اختبار نظام توليد أرقام الحسابات بنجاح!');
    console.log('🎯 النظام جاهز لتوليد أرقام الحسابات تلقائياً');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  testAccountCodeGeneration()
    .then(() => {
      console.log('🎉 تم إنجاز الاختبار بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في الاختبار:', error);
      process.exit(1);
    });
}

module.exports = { testAccountCodeGeneration };
