// حذف عمود الموظف من جدول الخدمات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

const updateQueries = [
  // حذف عمود employee_id من جدول الخدمات
  `ALTER TABLE services DROP COLUMN IF EXISTS employee_id;`,
  
  // تحديث البيانات لتكون بدون employee_id
  `
  TRUNCATE TABLE services RESTART IDENTITY CASCADE;
  
  INSERT INTO services (name, lineage_id, created_date) VALUES
  ('اعداد', 1, '2024-01-01'),
  ('جلسة', 1, '2024-01-01'),
  ('متابعة', 2, '2024-01-01'),
  ('اشراف', 2, '2024-01-01'),
  ('مصروفات قضائية', 3, '2024-01-01'),
  ('استشارات قانونية', 4, '2024-01-01'),
  ('صياغة عقود', 5, '2024-01-01'),
  ('تمثيل قانوني', 6, '2024-01-01');
  `
];

async function removeEmployeeFromServices() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    console.log('🔄 جاري حذف عمود الموظف من جدول الخدمات...');
    
    for (let i = 0; i < updateQueries.length; i++) {
      try {
        console.log(`🔄 تطبيق التحديث ${i + 1}/${updateQueries.length}...`);
        await client.query(updateQueries[i]);
        console.log(`✅ تم تطبيق التحديث ${i + 1} بنجاح`);
      } catch (error) {
        console.error(`❌ خطأ في التحديث ${i + 1}:`, error.message);
      }
    }

    // التحقق من النتائج
    console.log('🔄 جاري التحقق من النتائج...');
    
    // عرض هيكل جدول الخدمات الجديد
    const servicesStructure = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'services' 
      ORDER BY ordinal_position
    `);
    
    console.log('📋 هيكل جدول الخدمات الجديد:');
    servicesStructure.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // عرض بيانات الخدمات
    const servicesData = await client.query(`
      SELECT s.id, s.name, s.lineage_id, l.name as lineage_name 
      FROM services s 
      LEFT JOIN lineages l ON s.lineage_id = l.id 
      ORDER BY s.id
    `);
    
    console.log('📋 بيانات الخدمات مع النسب:');
    servicesData.rows.forEach(row => {
      console.log(`   - ${row.id}: ${row.name} → ${row.lineage_name}`);
    });

    console.log('🎉 تم حذف عمود الموظف من جدول الخدمات بنجاح!');
    console.log('📋 الهيكل النهائي لجدول الخدمات:');
    console.log('   ✅ id - الرقم التعريفي');
    console.log('   ✅ name - اسم الخدمة');
    console.log('   ✅ lineage_id - ربط بمجموعة النسب المالية');
    console.log('   ✅ created_date - تاريخ الإنشاء');
    
  } catch (error) {
    console.error('❌ خطأ في تحديث قاعدة البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
removeEmployeeFromServices();
