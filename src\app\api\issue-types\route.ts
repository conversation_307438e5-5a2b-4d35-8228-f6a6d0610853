import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع أنواع القضايا
export async function GET() {
  try {
    const result = await query(`
      SELECT * FROM issue_types 
      ORDER BY created_date DESC
    `)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching issue types:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات أنواع القضايا' },
      { status: 500 }
    )
  }
}

// POST - إضافة نوع قضية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, color, is_active = true } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم نوع القضية مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO issue_types (name, description, color, is_active)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [name, description, color, is_active])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة نوع القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating issue type:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة نوع القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث نوع قضية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, description, color, is_active } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم نوع القضية مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE issue_types 
      SET name = $1, description = $2, color = $3, is_active = $4, 
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `, [name, description, color, is_active, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث نوع القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating issue type:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث نوع القضية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف نوع قضية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف نوع القضية مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM issue_types WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'نوع القضية غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف نوع القضية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting issue type:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف نوع القضية' },
      { status: 500 }
    )
  }
}
