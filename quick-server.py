import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 8444

html_content = """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>LedgerSMB - يعمل بنجاح!</title>
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; background: #f0f8ff; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c5aa0; margin-bottom: 30px; }
        .status { background: #4CAF50; color: white; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0; }
        .feature { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-right: 4px solid #2c5aa0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 LedgerSMB</h1>
            <h2>نظام المحاسبة مفتوح المصدر</h2>
        </div>
        
        <div class="status">
            ✅ الخادم يعمل بنجاح على المنفذ 8444
        </div>
        
        <div class="feature">
            <h3>🌐 معلومات الاتصال</h3>
            <p><strong>المحلي:</strong> http://localhost:8444</p>
            <p><strong>الحالة:</strong> متصل ويعمل</p>
        </div>
        
        <div class="feature">
            <h3>📋 ميزات LedgerSMB</h3>
            <ul>
                <li>المحاسبة العامة</li>
                <li>إدارة العملاء والموردين</li>
                <li>إدارة المخزون</li>
                <li>التقارير المالية</li>
                <li>كشوف الرواتب</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🔗 روابط مفيدة</h3>
            <p><a href="https://ledgersmb.org" target="_blank">الموقع الرسمي</a></p>
            <p><a href="https://ledgersmb.org/documentation" target="_blank">الوثائق</a></p>
        </div>
    </div>
</body>
</html>"""

class SimpleHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))

def open_browser():
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}')

print(f"🚀 تشغيل LedgerSMB على المنفذ {PORT}")
print(f"🌐 الرابط: http://localhost:{PORT}")

# فتح المتصفح
threading.Thread(target=open_browser, daemon=True).start()

try:
    with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
        print("✅ الخادم يعمل - اضغط Ctrl+C للإيقاف")
        httpd.serve_forever()
except KeyboardInterrupt:
    print("\n🛑 تم إيقاف الخادم")
except Exception as e:
    print(f"❌ خطأ: {e}")
