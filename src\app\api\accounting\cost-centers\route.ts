import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع مراكز التكلفة
export async function GET() {
  try {
    const result = await query(`
      SELECT
        id,
        center_code,
        center_name,
        center_level,
        is_active,
        created_date
      FROM cost_centers
      ORDER BY center_code
    `)

    return NextResponse.json({
      success: true,
      data: result.rows,
      total: result.rows.length,
      message: 'تم جلب مراكز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب مراكز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب مراكز التكلفة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة مركز تكلفة جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      center_code,
      center_name,
      center_level = 1,
      is_active = true
    } = body

    // التحقق من صحة البيانات
    if (!center_code || !center_name) {
      return NextResponse.json({
        success: false,
        error: 'رمز مركز التكلفة واسم مركز التكلفة مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار رمز مركز التكلفة
    const existingCenter = await query(
      'SELECT id FROM cost_centers WHERE center_code = $1',
      [center_code]
    )

    if (existingCenter.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رمز مركز التكلفة موجود مسبقاً'
      }, { status: 400 })
    }

    const result = await query(`
      INSERT INTO cost_centers (center_code, center_name, center_level, is_active)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [center_code, center_name, center_level, is_active])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة مركز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة مركز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة مركز التكلفة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث مركز تكلفة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      center_code,
      center_name,
      center_level,
      is_active
    } = body

    if (!id || !center_code || !center_name) {
      return NextResponse.json({
        success: false,
        error: 'المعرف ورمز المركز واسم المركز مطلوبان'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE cost_centers
      SET center_code = $1, center_name = $2, center_level = $3, is_active = $4
      WHERE id = $5
      RETURNING *
    `, [center_code, center_name, center_level, is_active, id])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'مركز التكلفة غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث مركز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث مركز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث مركز التكلفة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف مركز تكلفة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف مركز التكلفة مطلوب'
      }, { status: 400 })
    }

    const result = await query(
      'DELETE FROM cost_centers WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'مركز التكلفة غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف مركز التكلفة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف مركز التكلفة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف مركز التكلفة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
