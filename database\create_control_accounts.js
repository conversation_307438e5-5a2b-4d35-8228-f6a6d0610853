const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function createControlAccounts() {
  try {
    console.log('🔄 إنشاء حسابات التحكم للعملاء والموظفين...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // البحث عن حساب الأصول المتداولة (المستوى 2)
    const currentAssetsResult = await client.query(`
      SELECT id FROM chart_of_accounts 
      WHERE account_code = '0101' AND account_level = 2
    `);

    // البحث عن حساب الخصوم المتداولة (المستوى 2)
    const currentLiabilitiesResult = await client.query(`
      SELECT id FROM chart_of_accounts 
      WHERE account_code = '0201' AND account_level = 2
    `);

    if (currentAssetsResult.rows.length === 0) {
      throw new Error('حساب الأصول المتداولة غير موجود');
    }

    if (currentLiabilitiesResult.rows.length === 0) {
      throw new Error('حساب الخصوم المتداولة غير موجود');
    }

    const currentAssetsId = currentAssetsResult.rows[0].id;
    const currentLiabilitiesId = currentLiabilitiesResult.rows[0].id;

    // إنشاء حساب تحكم العملاء (المستوى 3)
    console.log('\n📊 إنشاء حساب تحكم العملاء...');
    await client.query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_name_en, 
        level_1_code, level_2_code, level_3_code,
        account_level, parent_id, account_type, account_nature,
        allow_transactions, linked_table, auto_create_sub_accounts,
        opening_balance, current_balance, description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      ON CONFLICT (account_code) DO UPDATE SET
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        description = EXCLUDED.description
    `, [
      '010102', 'حسابات العملاء', 'Clients Accounts',
      '01', '0101', '010102',
      3, currentAssetsId, 'أصول', 'مدين',
      false, 'clients', true,
      0, 0, 'حساب تحكم لجميع العملاء - يحتوي على حسابات فرعية لكل عميل'
    ]);

    // إنشاء حساب تحكم الموظفين (المستوى 3)
    console.log('📊 إنشاء حساب تحكم الموظفين...');
    await client.query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_name_en, 
        level_1_code, level_2_code, level_3_code,
        account_level, parent_id, account_type, account_nature,
        allow_transactions, linked_table, auto_create_sub_accounts,
        opening_balance, current_balance, description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      ON CONFLICT (account_code) DO UPDATE SET
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        description = EXCLUDED.description
    `, [
      '020102', 'حسابات الموظفين', 'Employees Accounts',
      '02', '0201', '020102',
      3, currentLiabilitiesId, 'خصوم', 'دائن',
      false, 'employees', true,
      0, 0, 'حساب تحكم لجميع الموظفين - يحتوي على حسابات فرعية لكل موظف'
    ]);

    console.log('✅ تم إنشاء حسابات التحكم بنجاح!');

    // التحقق من النتائج
    const clientsControlAccount = await client.query(`
      SELECT * FROM chart_of_accounts WHERE account_code = '010102'
    `);

    const employeesControlAccount = await client.query(`
      SELECT * FROM chart_of_accounts WHERE account_code = '020102'
    `);

    console.log('\n📋 تفاصيل حسابات التحكم:');
    console.log('   حساب تحكم العملاء:', clientsControlAccount.rows[0].account_name);
    console.log('   حساب تحكم الموظفين:', employeesControlAccount.rows[0].account_name);

    // عرض إحصائيات
    const clientsCount = await client.query(`
      SELECT COUNT(*) as count FROM clients WHERE status = 'active'
    `);

    const employeesCount = await client.query(`
      SELECT COUNT(*) as count FROM employees WHERE status = 'active'
    `);

    console.log('\n📊 الإحصائيات:');
    console.log(`   عدد العملاء النشطين: ${clientsCount.rows[0].count}`);
    console.log(`   عدد الموظفين النشطين: ${employeesCount.rows[0].count}`);

  } catch (error) {
    console.error('❌ خطأ في إنشاء حسابات التحكم:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  createControlAccounts()
    .then(() => {
      console.log('\n🎉 تم إنشاء حسابات التحكم بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في إنشاء حسابات التحكم:', error);
      process.exit(1);
    });
}

module.exports = { createControlAccounts };
