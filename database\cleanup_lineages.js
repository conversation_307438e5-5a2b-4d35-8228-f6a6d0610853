// تنظيف جدول النسب المالية من الأعمدة القديمة
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

const cleanupQueries = [
  // حذف الأعمدة القديمة
  `ALTER TABLE lineages DROP COLUMN IF EXISTS management_share;`,
  `ALTER TABLE lineages DROP COLUMN IF EXISTS court_share;`,
  `ALTER TABLE lineages DROP COLUMN IF EXISTS commission_share;`,
  `ALTER TABLE lineages DROP COLUMN IF EXISTS other_share;`,
  `ALTER TABLE lineages DROP COLUMN IF EXISTS updated_at;`,
  
  // إنشاء جدول المحاكم إذا لم يكن موجوداً
  `
  CREATE TABLE IF NOT EXISTS courts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100),
    governorate_id INTEGER,
    address TEXT,
    phone VARCHAR(50),
    employee_id INTEGER,
    issue_id INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,
  
  // إدراج بيانات نموذجية للمحاكم
  `
  INSERT INTO courts (name, type, governorate_id, address, phone, employee_id, issue_id, is_active, created_date) VALUES
  ('المحكمة العليا - صنعاء', 'محكمة عليا', 1, 'شارع الزبيري - صنعاء', '01-123456', 1, 1, true, '2024-01-01'),
  ('محكمة الاستئناف التجارية - صنعاء', 'محكمة استئناف', 1, 'شارع الستين - صنعاء', '01-234567', 2, 2, true, '2024-01-01'),
  ('المحكمة التجارية الابتدائية - صنعاء', 'محكمة تجارية', 1, 'شارع الحصبة - صنعاء', '01-345678', 3, 3, true, '2024-01-01'),
  ('محكمة الأحوال الشخصية - صنعاء', 'محكمة أحوال شخصية', 1, 'شارع الثورة - صنعاء', '01-456789', 4, 4, true, '2024-01-01'),
  ('المحكمة الجنائية - عدن', 'محكمة جنائية', 2, 'كريتر - عدن', '02-123456', 1, 5, true, '2024-01-01')
  ON CONFLICT (id) DO NOTHING;
  `
];

async function cleanupDatabase() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    console.log('🔄 جاري تنظيف الجداول...');
    
    for (let i = 0; i < cleanupQueries.length; i++) {
      try {
        console.log(`🔄 تطبيق التنظيف ${i + 1}/${cleanupQueries.length}...`);
        await client.query(cleanupQueries[i]);
        console.log(`✅ تم تطبيق التنظيف ${i + 1} بنجاح`);
      } catch (error) {
        console.error(`❌ خطأ في التنظيف ${i + 1}:`, error.message);
      }
    }

    // التحقق من النتائج النهائية
    console.log('🔄 جاري التحقق من النتائج النهائية...');
    
    // عرض هيكل جدول النسب المالية النهائي
    const lineagesStructure = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'lineages' 
      ORDER BY ordinal_position
    `);
    
    console.log('📋 هيكل جدول النسب المالية النهائي:');
    lineagesStructure.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // عرض عدد المحاكم
    const courtsResult = await client.query('SELECT COUNT(*) FROM courts');
    console.log(`✅ جدول المحاكم: ${courtsResult.rows[0].count} سجل`);

    console.log('🎉 تم تنظيف قاعدة البيانات بنجاح!');
    console.log('📋 الجداول النهائية:');
    console.log('   ✅ lineages - 3 أعمدة (id, name, admin_percentage)');
    console.log('   ✅ services - مع الربط بالنسب والموظفين');
    console.log('   ✅ courts - مع الربط بالموظفين والقضايا');
    console.log('   ✅ case_distribution - لتوزيع القضايا');
    console.log('   ✅ service_distributions - لتفاصيل التوزيع');
    
  } catch (error) {
    console.error('❌ خطأ في تنظيف قاعدة البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التنظيف
cleanupDatabase();
