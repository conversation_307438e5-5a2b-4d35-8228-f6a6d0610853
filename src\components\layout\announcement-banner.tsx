'use client'

import { useState, useEffect } from 'react'
import { X, Megaphone } from 'lucide-react'

interface Announcement {
  id: number
  announcement_1: string
  announcement_2: string
  announcement_3: string
  announcement_4: string
  is_active: boolean
}

export function AnnouncementBanner() {
  const [announcement, setAnnouncement] = useState<Announcement | null>(null)
  const [isVisible, setIsVisible] = useState(true)
  const [isLoading, setIsLoading] = useState(true)

  // جلب الإعلانات
  const fetchAnnouncements = async () => {
    try {
      const response = await fetch('/api/settings/announcements')
      const data = await response.json()
      if (data.success && data.data && data.data.is_active) {
        setAnnouncement(data.data)
      }
    } catch (error) {
      console.error('Error fetching announcements:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAnnouncements()
  }, [])

  // إخفاء الشريط
  const handleClose = () => {
    setIsVisible(false)
  }

  // إذا كان يتم التحميل أو لا توجد إعلانات أو الشريط مخفي
  if (isLoading || !announcement || !isVisible) {
    return null
  }

  // جمع الإعلانات غير الفارغة
  const announcements = [
    announcement.announcement_1,
    announcement.announcement_2,
    announcement.announcement_3,
    announcement.announcement_4
  ].filter(text => text && text.trim())

  // إذا لم توجد إعلانات
  if (announcements.length === 0) {
    return null
  }

  // دمج الإعلانات في نص واحد
  const announcementText = announcements.join(' • ')

  return (
    <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md relative overflow-hidden">
      <div className="flex items-center px-4 py-2">
        {/* أيقونة الإعلان */}
        <div className="flex-shrink-0 mr-3">
          <Megaphone className="h-5 w-5 text-blue-200" />
        </div>

        {/* النص المتحرك */}
        <div className="flex-1 overflow-hidden">
          <div className="animate-marquee whitespace-nowrap text-sm font-medium">
            {announcementText}
          </div>
        </div>

        {/* زر الإغلاق */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 ml-3 p-1 rounded-full hover:bg-blue-500 transition-colors"
          title="إخفاء الإعلانات"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      {/* خط متحرك في الأسفل */}
      <div className="h-1 bg-gradient-to-r from-blue-400 to-blue-500">
        <div className="h-full bg-white opacity-30 animate-pulse"></div>
      </div>
    </div>
  )
}

// إضافة CSS للحركة المتحركة
const styles = `
  @keyframes marquee {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  .animate-marquee {
    animation: marquee 30s linear infinite;
  }

  .animate-marquee:hover {
    animation-play-state: paused;
  }
`

// إضافة الأنماط إلى الصفحة
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style')
  styleSheet.type = 'text/css'
  styleSheet.innerText = styles
  document.head.appendChild(styleSheet)
}
