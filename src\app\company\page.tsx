'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  Edit,
  Save,
  X,
  Phone,
  Mail,
  MapPin,
  Globe,
  FileText,
  Calendar
} from 'lucide-react'

interface Company {
  id: number
  name: string
  legal_name: string
  registration_number: string
  tax_number: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  website: string
  logo_url: string
  logo_right_text: string
  logo_left_text: string
  logo_image_url: string
  established_date: string
  legal_form: string
  capital: number
  description: string
  is_active: boolean
  created_date: string
}

export default function CompanyPage() {
  const [company, setCompany] = useState<Company | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    legal_name: '',
    registration_number: '',
    tax_number: '',
    address: '',
    city: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    logo_url: '',
    logo_right_text: '',
    logo_left_text: '',
    logo_image_url: '',
    established_date: '',
    legal_form: '',
    capital: '',
    description: '',
    is_active: true
  })

  const fetchCompany = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      // جلب بيانات الشركة من قاعدة البيانات
      const response = await fetch('/api/companies')
      const data = await response.json()

      if (data.success && data.data && data.data.length > 0) {
        // أخذ أول شركة من القائمة
        const companyData = data.data[0]
        setCompany(companyData)
        setFormData({
          name: companyData.name || '',
          legal_name: companyData.legal_name || '',
          registration_number: companyData.registration_number || '',
          tax_number: companyData.tax_number || '',
          address: companyData.address || '',
          city: companyData.city || '',
          country: companyData.country || '',
          phone: companyData.phone || '',
          email: companyData.email || '',
          website: companyData.website || '',
          logo_url: companyData.logo_url || '',
          logo_right_text: companyData.logo_right_text || '',
          logo_left_text: companyData.logo_left_text || '',
          logo_image_url: companyData.logo_image_url || '',
          established_date: companyData.established_date || '',
          legal_form: companyData.legal_form || '',
          capital: companyData.capital?.toString() || '',
          description: companyData.description || '',
          is_active: companyData.is_active !== undefined ? companyData.is_active : true
        })
      } else {
        // إذا لم توجد بيانات، إنشاء شركة افتراضية
        const defaultCompany = {
          name: 'مكتب المحاماة والاستشارات القانونية',
          legal_name: 'شركة المحاماة والاستشارات القانونية المحدودة',
          registration_number: 'CR-2024-001',
          tax_number: 'TAX-*********',
          address: 'شارع الزبيري، مبنى رقم 15، الطابق الثالث',
          city: 'صنعاء',
          country: 'اليمن',
          phone: '+967-1-123456',
          email: '<EMAIL>',
          website: 'www.legalfirm.ye',
          logo_url: '/images/company-logo.png',
          logo_right_text: 'مكتب المحاماة والاستشارات القانونية',
          logo_left_text: 'العدالة والنزاهة في خدمة القانون',
          logo_image_url: '/images/logo.png',
          established_date: '2020-01-15',
          legal_form: 'شركة محدودة المسؤولية',
          capital: 1000000,
          description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',
          is_active: true
        }

        // إنشاء الشركة في قاعدة البيانات
        const createResponse = await fetch('/api/companies', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(defaultCompany)
        })

        if (createResponse.ok) {
          const createData = await createResponse.json()
          setCompany(createData.data)
          setFormData({
            name: createData.data.name,
            legal_name: createData.data.legal_name,
            registration_number: createData.data.registration_number,
            tax_number: createData.data.tax_number,
            address: createData.data.address,
            city: createData.data.city,
            country: createData.data.country,
            phone: createData.data.phone,
            email: createData.data.email,
            website: createData.data.website,
            logo_url: createData.data.logo_url,
            logo_right_text: createData.data.logo_right_text,
            logo_left_text: createData.data.logo_left_text,
            logo_image_url: createData.data.logo_image_url,
            established_date: createData.data.established_date,
            legal_form: createData.data.legal_form,
            capital: createData.data.capital?.toString() || '',
            description: createData.data.description,
            is_active: createData.data.is_active
          })
        }
      }
    } catch (error) {
      console.error('Error fetching company data:', error)
      setDbError('فشل في جلب بيانات الشركة')
      setCompany(null)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCompany()
  }, [])

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancel = () => {
    setIsEditing(false)
    if (company) {
      setFormData({
        name: company.name,
        legal_name: company.legal_name,
        registration_number: company.registration_number,
        tax_number: company.tax_number,
        address: company.address,
        city: company.city,
        country: company.country,
        phone: company.phone,
        email: company.email,
        website: company.website,
        logo_url: company.logo_url,
        logo_right_text: company.logo_right_text,
        logo_left_text: company.logo_left_text,
        logo_image_url: company.logo_image_url,
        established_date: company.established_date,
        legal_form: company.legal_form,
        capital: company.capital.toString(),
        description: company.description,
        is_active: company.is_active
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (company) {
      try {
        const updateData = {
          id: company.id,
          ...formData,
          capital: Number(formData.capital) || 0
        }

        const response = await fetch('/api/companies', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updateData)
        })

        const data = await response.json()

        if (data.success) {
          setCompany(data.data)
          setIsEditing(false)
          alert('تم تحديث بيانات الشركة بنجاح')
        } else {
          alert(`خطأ في تحديث البيانات: ${data.error}`)
        }
      } catch (error) {
        console.error('Error updating company:', error)
        alert('حدث خطأ أثناء تحديث البيانات')
      }
    }
  }

  const legalForms = [
    'شركة محدودة المسؤولية',
    'شركة مساهمة',
    'شركة تضامن',
    'مؤسسة فردية',
    'شركة توصية بسيطة',
    'شركة توصية بالأسهم'
  ]

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Building2 className="h-8 w-8 mr-3 text-blue-600" />
              بيانات الشركة
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة بيانات الشركة الأساسية</p>
          </div>

          {!isEditing && (
            <Button onClick={handleEdit} className="bg-blue-600 hover:bg-blue-700">
              <Edit className="h-4 w-4 mr-2" />
              تعديل البيانات
            </Button>
          )}
        </div>

        {/* عرض رسالة الخطأ */}
        {dbError && (
          <Card>
            <CardContent className="p-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">خطأ في الاتصال بقاعدة البيانات</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={fetchCompany} variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {!dbError && !isLoading && company && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* معلومات أساسية */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building2 className="h-5 w-5 mr-2" />
                  المعلومات الأساسية
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">اسم الشركة التجاري *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="legal_name">الاسم القانوني *</Label>
                        <Input
                          id="legal_name"
                          value={formData.legal_name}
                          onChange={(e) => setFormData({...formData, legal_name: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="registration_number">رقم التسجيل</Label>
                        <Input
                          id="registration_number"
                          value={formData.registration_number}
                          onChange={(e) => setFormData({...formData, registration_number: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="tax_number">الرقم الضريبي</Label>
                        <Input
                          id="tax_number"
                          value={formData.tax_number}
                          onChange={(e) => setFormData({...formData, tax_number: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="legal_form">الشكل القانوني</Label>
                        <select
                          id="legal_form"
                          value={formData.legal_form}
                          onChange={(e) => setFormData({...formData, legal_form: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        >
                          <option value="">اختر الشكل القانوني</option>
                          {legalForms.map((form) => (
                            <option key={form} value={form}>{form}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <Label htmlFor="established_date">تاريخ التأسيس</Label>
                        <Input
                          id="established_date"
                          type="date"
                          value={formData.established_date}
                          onChange={(e) => setFormData({...formData, established_date: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="capital">رأس المال</Label>
                        <Input
                          id="capital"
                          type="number"
                          value={formData.capital}
                          onChange={(e) => setFormData({...formData, capital: e.target.value})}
                        />
                      </div>
                    </div>

                    {/* حقول الشعارات */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">معلومات الشعار</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="logo_right_text">شعار يمين (نص طويل)</Label>
                          <textarea
                            id="logo_right_text"
                            value={formData.logo_right_text}
                            onChange={(e) => setFormData({...formData, logo_right_text: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            rows={3}
                            placeholder="النص الذي يظهر على يمين الشعار"
                          />
                        </div>
                        <div>
                          <Label htmlFor="logo_left_text">شعار يسار (نص طويل)</Label>
                          <textarea
                            id="logo_left_text"
                            value={formData.logo_left_text}
                            onChange={(e) => setFormData({...formData, logo_left_text: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            rows={3}
                            placeholder="النص الذي يظهر على يسار الشعار"
                          />
                        </div>
                        <div>
                          <Label htmlFor="logo_image_url">رابط صورة الشعار</Label>
                          <Input
                            id="logo_image_url"
                            value={formData.logo_image_url}
                            onChange={(e) => setFormData({...formData, logo_image_url: e.target.value})}
                            placeholder="/images/logo.png"
                          />
                        </div>
                        <div>
                          <Label htmlFor="logo_url">رابط الشعار القديم</Label>
                          <Input
                            id="logo_url"
                            value={formData.logo_url}
                            onChange={(e) => setFormData({...formData, logo_url: e.target.value})}
                            placeholder="/images/company-logo.png"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="description">وصف النشاط</Label>
                      <textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        rows={3}
                      />
                    </div>

                    <div className="flex space-x-3 space-x-reverse">
                      <Button type="submit" className="flex-1">
                        <Save className="h-4 w-4 mr-2" />
                        حفظ التغييرات
                      </Button>
                      <Button type="button" variant="outline" onClick={handleCancel} className="flex-1">
                        <X className="h-4 w-4 mr-2" />
                        إلغاء
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>اسم الشركة التجاري</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded font-medium">{company.name}</p>
                      </div>
                      <div>
                        <Label>الاسم القانوني</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded">{company.legal_name}</p>
                      </div>
                      <div>
                        <Label>رقم التسجيل</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-400" />
                          {company.registration_number}
                        </p>
                      </div>
                      <div>
                        <Label>الرقم الضريبي</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded">{company.tax_number}</p>
                      </div>
                      <div>
                        <Label>الشكل القانوني</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded">{company.legal_form}</p>
                      </div>
                      <div>
                        <Label>تاريخ التأسيس</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          {company.established_date}
                        </p>
                      </div>
                      <div>
                        <Label>رأس المال</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded font-medium">
                          {company.capital.toLocaleString()} ريال
                        </p>
                      </div>
                      <div>
                        <Label>الحالة</Label>
                        <div className="mt-1">
                          <Badge className={company.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {company.is_active ? 'نشطة' : 'غير نشطة'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* عرض معلومات الشعار */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">معلومات الشعار</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>شعار يمين</Label>
                          <p className="mt-1 p-2 bg-gray-50 rounded text-sm">
                            {company.logo_right_text || 'غير محدد'}
                          </p>
                        </div>
                        <div>
                          <Label>شعار يسار</Label>
                          <p className="mt-1 p-2 bg-gray-50 rounded text-sm">
                            {company.logo_left_text || 'غير محدد'}
                          </p>
                        </div>
                        <div>
                          <Label>صورة الشعار</Label>
                          <div className="mt-1 p-2 bg-gray-50 rounded">
                            {company.logo_image_url ? (
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <img
                                  src={company.logo_image_url}
                                  alt="شعار الشركة"
                                  className="h-12 w-12 object-contain"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none'
                                  }}
                                />
                                <span className="text-sm text-blue-600">{company.logo_image_url}</span>
                              </div>
                            ) : (
                              <span className="text-sm text-gray-500">لا توجد صورة</span>
                            )}
                          </div>
                        </div>
                        <div>
                          <Label>رابط الشعار القديم</Label>
                          <p className="mt-1 p-2 bg-gray-50 rounded text-sm">
                            {company.logo_url || 'غير محدد'}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label>وصف النشاط</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{company.description}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* معلومات الاتصال */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  معلومات الاتصال
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="phone">الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">البريد الإلكتروني</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="website">الموقع الإلكتروني</Label>
                      <Input
                        id="website"
                        value={formData.website}
                        onChange={(e) => setFormData({...formData, website: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="address">العنوان</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => setFormData({...formData, address: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">المدينة</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => setFormData({...formData, city: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="country">البلد</Label>
                      <Input
                        id="country"
                        value={formData.country}
                        onChange={(e) => setFormData({...formData, country: e.target.value})}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center p-2 bg-gray-50 rounded">
                      <Phone className="h-4 w-4 mr-2 text-gray-400" />
                      <span>{company.phone}</span>
                    </div>
                    <div className="flex items-center p-2 bg-gray-50 rounded">
                      <Mail className="h-4 w-4 mr-2 text-gray-400" />
                      <span>{company.email}</span>
                    </div>
                    <div className="flex items-center p-2 bg-gray-50 rounded">
                      <Globe className="h-4 w-4 mr-2 text-gray-400" />
                      <span>{company.website}</span>
                    </div>
                    <div className="flex items-start p-2 bg-gray-50 rounded">
                      <MapPin className="h-4 w-4 mr-2 text-gray-400 mt-0.5" />
                      <div>
                        <p>{company.address}</p>
                        <p className="text-sm text-gray-600">{company.city}, {company.country}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {!dbError && !isLoading && !company && (
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات شركة</h3>
                <p className="text-gray-600">لم يتم العثور على بيانات الشركة في النظام</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
