# تثبيت وتشغيل LedgerSMB
# LedgerSMB Installation and Setup Guide

## 📋 نظرة عامة
LedgerSMB هو نظام محاسبي مفتوح المصدر قوي ومتقدم يوفر إدارة شاملة للحسابات والمالية.

## 🚀 طرق التشغيل

### الطريقة 1: التشغيل باستخدام Docker (الأسهل والأسرع) ⭐
```powershell
# تشغيل LedgerSMB باستخدام Docker على المنفذ 8444
PowerShell -ExecutionPolicy Bypass -File "E:\mohammi\ledgersmb-install\run-ledgersmb-docker.ps1"
```

**المتطلبات:**
- Docker Desktop مثبت
- اتصال بالإنترنت لتحميل الصور

**المميزات:**
- ✅ تثبيت سريع (5-10 دقائق)
- ✅ لا يتطلب تثبيت تبعيات إضافية
- ✅ سهل الإدارة والصيانة
- ✅ معزول عن النظام الأساسي

### الطريقة 2: التثبيت الكامل المحلي
```powershell
# تثبيت LedgerSMB محلياً مع جميع التبعيات
PowerShell -ExecutionPolicy Bypass -File "E:\mohammi\ledgersmb-install\download-and-install-ledgersmb.ps1"
```

**المتطلبات:**
- صلاحيات المدير
- اتصال بالإنترنت
- مساحة قرص 2-3 جيجابايت

**ما يتم تثبيته:**
- Git
- Strawberry Perl
- PostgreSQL
- LedgerSMB من المصدر
- جميع التبعيات المطلوبة

### الطريقة 3: التشغيل المبسط
```powershell
# تشغيل LedgerSMB إذا كان مثبتاً مسبقاً
PowerShell -ExecutionPolicy Bypass -File "E:\mohammi\ledgersmb-install\run-ledgersmb-simple.ps1"
```

## 🌐 الوصول إلى LedgerSMB

بعد التشغيل الناجح:
- **المحلي:** http://localhost:8444
- **الخارجي:** http://[your-ip]:8444

## 🔐 معلومات تسجيل الدخول الافتراضية

### للتثبيت باستخدام Docker:
- **المستخدم:** admin
- **كلمة المرور:** admin123

### للتثبيت المحلي:
- يتم إنشاء المستخدم أثناء الإعداد الأولي
- اتبع معالج الإعداد في المتصفح

## 🗄️ معلومات قاعدة البيانات

### Docker:
- **الخادم:** ledgersmb-db
- **قاعدة البيانات:** ledgersmb
- **المستخدم:** postgres
- **كلمة المرور:** postgres123
- **المنفذ:** 5432 (داخلي)

### التثبيت المحلي:
- **الخادم:** localhost
- **قاعدة البيانات:** ledgersmb_standalone
- **المستخدم:** postgres
- **كلمة المرور:** postgres123
- **المنفذ:** 5432

## 🛠️ أوامر إدارة مفيدة

### Docker:
```powershell
# عرض حالة الحاويات
docker ps

# عرض سجلات التطبيق
docker logs ledgersmb-app

# عرض سجلات قاعدة البيانات
docker logs ledgersmb-db

# إيقاف الخدمة
docker stop ledgersmb-app ledgersmb-db

# بدء الخدمة
docker start ledgersmb-db ledgersmb-app

# حذف الحاويات (سيحذف البيانات!)
docker rm -f ledgersmb-app ledgersmb-db
docker volume rm ledgersmb-data ledgersmb-files
```

### التثبيت المحلي:
```powershell
# تشغيل LedgerSMB
cd "E:\mohammi\ledgersmb-install\LedgerSMB"
.\start-ledgersmb.ps1

# إيقاف خدمة PostgreSQL
Stop-Service postgresql-x64-15

# بدء خدمة PostgreSQL
Start-Service postgresql-x64-15
```

## 🔧 استكشاف الأخطاء وإصلاحها

### مشكلة: المنفذ 8444 مستخدم
```powershell
# تغيير المنفذ إلى 8445
PowerShell -ExecutionPolicy Bypass -File "run-ledgersmb-docker.ps1" -Port 8445
```

### مشكلة: Docker غير مثبت
1. حمل Docker Desktop من: https://www.docker.com/products/docker-desktop
2. ثبت Docker Desktop
3. أعد تشغيل الكمبيوتر
4. شغل السكريبت مرة أخرى

### مشكلة: خطأ في الصلاحيات
1. انقر بزر الماوس الأيمن على PowerShell
2. اختر "تشغيل كمدير"
3. شغل السكريبت

### مشكلة: لا يمكن الوصول إلى LedgerSMB
1. تأكد من تشغيل الحاويات: `docker ps`
2. تحقق من السجلات: `docker logs ledgersmb-app`
3. تأكد من أن المنفذ 8444 غير محجوب بواسطة الجدار الناري

## 📚 الميزات الرئيسية لـ LedgerSMB

- **المحاسبة العامة:** دليل الحسابات، القيود اليومية، ميزان المراجعة
- **الحسابات المدينة:** إدارة العملاء، الفواتير، المقبوضات
- **الحسابات الدائنة:** إدارة الموردين، الفواتير، المدفوعات
- **إدارة المخزون:** تتبع المنتجات، الكميات، التقييم
- **كشوف الرواتب:** إدارة الموظفين، الرواتب، الخصومات
- **التقارير المالية:** قائمة الدخل، الميزانية العمومية، التدفق النقدي
- **إدارة المشاريع:** تتبع التكاليف والإيرادات لكل مشروع
- **نقاط البيع:** واجهة بيع سهلة الاستخدام

## 🔗 روابط مفيدة

- **الموقع الرسمي:** https://ledgersmb.org/
- **الوثائق:** https://ledgersmb.org/documentation
- **المجتمع:** https://ledgersmb.org/community
- **GitHub:** https://github.com/ledgersmb/LedgerSMB
- **الدعم:** https://ledgersmb.org/support

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. راجع قسم استكشاف الأخطاء أعلاه
2. تحقق من السجلات للحصول على تفاصيل الخطأ
3. ابحث في الوثائق الرسمية
4. اطلب المساعدة من مجتمع LedgerSMB

---

**ملاحظة:** هذا الدليل مخصص لبيئة Windows. للأنظمة الأخرى، راجع الوثائق الرسمية.
