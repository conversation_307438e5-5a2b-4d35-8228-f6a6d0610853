import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع المستخدمين
export async function GET() {
  try {
    const result = await query(`
      SELECT
        u.*,
        e.name as employee_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      ORDER BY u.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات المستخدمين' },
      { status: 500 }
    )
  }
}

// POST - إضافة مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, email, password, role = 'user', status = 'active' } = body

    if (!username || !email || !password) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم والبريد الإلكتروني وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
    const existingUser = await query(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    )

    if (existingUser.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً' },
        { status: 400 }
      )
    }

    // تشفير كلمة المرور (في التطبيق الحقيقي يجب استخدام bcrypt)
    const password_hash = Buffer.from(password).toString('base64')

    const result = await query(`
      INSERT INTO users (username, email, password_hash, role, status)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, username, email, role, status, created_at
    `, [username, email, password_hash, role, status])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المستخدم بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المستخدم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مستخدم
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, username, email, role, status } = body

    if (!id || !username || !email) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم المستخدم والبريد الإلكتروني مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE users
      SET username = $1, email = $2, role = $3, status = $4,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING id, username, email, role, status, created_at
    `, [username, email, role, status, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المستخدم بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات المستخدم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مستخدم
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM users WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المستخدم' },
      { status: 500 }
    )
  }
}
