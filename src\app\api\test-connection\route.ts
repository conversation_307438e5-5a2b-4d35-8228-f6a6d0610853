import { NextResponse } from 'next/server'
import { Pool } from 'pg'

export async function GET() {
  let pool: Pool | null = null
  
  try {
    // إعداد الاتصال
    const dbConfig = {
      host: 'localhost',
      port: 5432,
      database: 'moham<PERSON>',
      user: 'postgres',
      password: 'yemen123',
      connectionTimeoutMillis: 5000,
      idleTimeoutMillis: 30000,
      max: 1
    }

    console.log('🔄 محاولة الاتصال بقاعدة البيانات...')
    console.log('📋 معلومات الاتصال:', {
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.user
    })

    pool = new Pool(dbConfig)
    
    // اختبار الاتصال
    const client = await pool.connect()
    
    // تشغيل استعلام بسيط
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version')
    const serverInfo = result.rows[0]
    
    client.release()
    
    console.log('✅ نجح الاتصال بقاعدة البيانات')
    
    return NextResponse.json({
      success: true,
      message: 'تم الاتصال بقاعدة البيانات بنجاح',
      details: `الوقت الحالي: ${serverInfo.current_time}\nإصدار PostgreSQL: ${serverInfo.pg_version}`,
      config: {
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.user
      }
    })
    
  } catch (error: any) {
    console.error('❌ فشل الاتصال بقاعدة البيانات:', error)
    
    let errorMessage = 'خطأ غير معروف'
    let errorDetails = error.message
    
    // تحليل نوع الخطأ
    if (error.code === 'ECONNREFUSED') {
      errorMessage = 'فشل في الاتصال - الخادم غير متاح'
      errorDetails = 'تأكد من تشغيل PostgreSQL على localhost:5432'
    } else if (error.code === 'ENOTFOUND') {
      errorMessage = 'لم يتم العثور على الخادم'
      errorDetails = 'تأكد من صحة عنوان الخادم'
    } else if (error.code === '28P01') {
      errorMessage = 'خطأ في المصادقة'
      errorDetails = 'تأكد من صحة اسم المستخدم وكلمة المرور'
    } else if (error.code === '3D000') {
      errorMessage = 'قاعدة البيانات غير موجودة'
      errorDetails = 'تأكد من وجود قاعدة بيانات بالاسم "mohammi"'
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = 'انتهت مهلة الاتصال'
      errorDetails = 'الخادم لا يستجيب في الوقت المحدد'
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      details: `رمز الخطأ: ${error.code || 'غير محدد'}\nالتفاصيل: ${errorDetails}`,
      config: {
        host: 'localhost',
        port: 5432,
        database: 'mohammi',
        user: 'postgres'
      }
    }, { status: 500 })
    
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}
