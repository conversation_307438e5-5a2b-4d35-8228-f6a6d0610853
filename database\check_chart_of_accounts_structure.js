// التحقق من هيكل جدول دليل الحسابات وتحديثه
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkAndUpdateChartOfAccounts() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من هيكل الجدول الحالي
    console.log('🔄 جاري التحقق من هيكل جدول دليل الحسابات...');
    
    const currentStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'chart_of_accounts'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 هيكل الجدول الحالي:');
    currentStructure.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    // إضافة الحقول المطلوبة لنظام ربط الحسابات
    console.log('🔄 جاري إضافة الحقول المطلوبة...');

    const fieldsToAdd = [
      {
        name: 'parent_id',
        type: 'INTEGER',
        description: 'معرف الحساب الأب'
      },
      {
        name: 'account_level',
        type: 'INTEGER DEFAULT 1',
        description: 'مستوى الحساب في الشجرة'
      },
      {
        name: 'is_main_account',
        type: 'BOOLEAN DEFAULT FALSE',
        description: 'هل هو حساب رئيسي'
      },
      {
        name: 'is_sub_account',
        type: 'BOOLEAN DEFAULT FALSE',
        description: 'هل هو حساب فرعي'
      },
      {
        name: 'linked_table',
        type: 'VARCHAR(100)',
        description: 'اسم الجدول المربوط'
      },
      {
        name: 'auto_create_sub_accounts',
        type: 'BOOLEAN DEFAULT FALSE',
        description: 'إنشاء حسابات فرعية تلقائياً'
      },
      {
        name: 'sub_account_prefix',
        type: 'VARCHAR(20)',
        description: 'بادئة الحسابات الفرعية'
      },
      {
        name: 'account_nature',
        type: 'VARCHAR(20) DEFAULT \'مدين\'',
        description: 'طبيعة الحساب (مدين/دائن)'
      },
      {
        name: 'is_active',
        type: 'BOOLEAN DEFAULT TRUE',
        description: 'حالة الحساب'
      },
      {
        name: 'notes',
        type: 'TEXT',
        description: 'ملاحظات'
      }
    ];

    for (const field of fieldsToAdd) {
      try {
        await client.query(`
          ALTER TABLE chart_of_accounts 
          ADD COLUMN IF NOT EXISTS ${field.name} ${field.type}
        `);
        console.log(`   ✅ تم إضافة حقل: ${field.name} - ${field.description}`);
      } catch (error) {
        console.log(`   ⚠️  الحقل ${field.name} موجود بالفعل`);
      }
    }

    // إنشاء فهرس للحساب الأب
    try {
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_parent_id 
        ON chart_of_accounts(parent_id)
      `);
      console.log('   ✅ تم إنشاء فهرس parent_id');
    } catch (error) {
      console.log('   ⚠️  فهرس parent_id موجود بالفعل');
    }

    // إنشاء فهرس للجدول المربوط
    try {
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_linked_table 
        ON chart_of_accounts(linked_table)
      `);
      console.log('   ✅ تم إنشاء فهرس linked_table');
    } catch (error) {
      console.log('   ⚠️  فهرس linked_table موجود بالفعل');
    }

    // تحديث البيانات الموجودة
    console.log('🔄 جاري تحديث البيانات الموجودة...');

    // تحديد الحسابات الرئيسية (المستوى الأول)
    await client.query(`
      UPDATE chart_of_accounts 
      SET is_main_account = TRUE, account_level = 1
      WHERE parent_id IS NULL OR parent_id = 0
    `);

    // تحديد الحسابات الفرعية
    await client.query(`
      UPDATE chart_of_accounts 
      SET is_sub_account = TRUE, account_level = 2
      WHERE parent_id IS NOT NULL AND parent_id > 0
    `);

    // إضافة بيانات تجريبية للحسابات الرئيسية
    console.log('🔄 جاري إضافة بيانات تجريبية...');

    const mainAccounts = [
      {
        code: '1000',
        name: 'الأصول',
        type: 'أصول',
        nature: 'مدين',
        level: 1,
        is_main: true
      },
      {
        code: '1100',
        name: 'الأصول المتداولة',
        type: 'أصول',
        nature: 'مدين',
        level: 2,
        parent_code: '1000'
      },
      {
        code: '1110',
        name: 'النقدية والبنوك',
        type: 'أصول',
        nature: 'مدين',
        level: 3,
        parent_code: '1100'
      },
      {
        code: '1120',
        name: 'حسابات الموكلين',
        type: 'أصول',
        nature: 'مدين',
        level: 3,
        parent_code: '1100',
        linked_table: 'clients',
        auto_create: true,
        prefix: 'CLI'
      },
      {
        code: '1130',
        name: 'حسابات الموظفين',
        type: 'أصول',
        nature: 'مدين',
        level: 3,
        parent_code: '1100',
        linked_table: 'employees',
        auto_create: true,
        prefix: 'EMP'
      },
      {
        code: '2000',
        name: 'الخصوم',
        type: 'خصوم',
        nature: 'دائن',
        level: 1,
        is_main: true
      },
      {
        code: '2100',
        name: 'الخصوم المتداولة',
        type: 'خصوم',
        nature: 'دائن',
        level: 2,
        parent_code: '2000'
      },
      {
        code: '3000',
        name: 'حقوق الملكية',
        type: 'حقوق ملكية',
        nature: 'دائن',
        level: 1,
        is_main: true
      },
      {
        code: '4000',
        name: 'الإيرادات',
        type: 'إيرادات',
        nature: 'دائن',
        level: 1,
        is_main: true
      },
      {
        code: '4100',
        name: 'إيرادات القضايا',
        type: 'إيرادات',
        nature: 'دائن',
        level: 2,
        parent_code: '4000',
        linked_table: 'issues',
        auto_create: true,
        prefix: 'CASE'
      },
      {
        code: '5000',
        name: 'المصروفات',
        type: 'مصروفات',
        nature: 'مدين',
        level: 1,
        is_main: true
      },
      {
        code: '5100',
        name: 'مصروفات المحاكم',
        type: 'مصروفات',
        nature: 'مدين',
        level: 2,
        parent_code: '5000',
        linked_table: 'courts',
        auto_create: true,
        prefix: 'CRT'
      }
    ];

    for (const account of mainAccounts) {
      // البحث عن الحساب الأب
      let parentId = null;
      if (account.parent_code) {
        const parentResult = await client.query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [account.parent_code]
        );
        if (parentResult.rows.length > 0) {
          parentId = parentResult.rows[0].id;
        }
      }

      // التحقق من وجود الحساب
      const existingAccount = await client.query(
        'SELECT id FROM chart_of_accounts WHERE account_code = $1',
        [account.code]
      );

      if (existingAccount.rows.length === 0) {
        await client.query(`
          INSERT INTO chart_of_accounts 
          (account_code, account_name, account_type, parent_id, account_level, 
           is_main_account, is_sub_account, linked_table, auto_create_sub_accounts, 
           sub_account_prefix, account_nature, is_active)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        `, [
          account.code,
          account.name,
          account.type,
          parentId,
          account.level,
          account.is_main || false,
          !account.is_main,
          account.linked_table || null,
          account.auto_create || false,
          account.prefix || null,
          account.nature,
          true
        ]);
        console.log(`   ✅ تم إضافة حساب: ${account.code} - ${account.name}`);
      } else {
        // تحديث الحساب الموجود
        await client.query(`
          UPDATE chart_of_accounts 
          SET parent_id = $1, account_level = $2, is_main_account = $3, 
              is_sub_account = $4, linked_table = $5, auto_create_sub_accounts = $6,
              sub_account_prefix = $7, account_nature = $8
          WHERE account_code = $9
        `, [
          parentId,
          account.level,
          account.is_main || false,
          !account.is_main,
          account.linked_table || null,
          account.auto_create || false,
          account.prefix || null,
          account.nature,
          account.code
        ]);
        console.log(`   ✅ تم تحديث حساب: ${account.code} - ${account.name}`);
      }
    }

    // عرض الهيكل النهائي
    console.log('📋 الهيكل النهائي لدليل الحسابات:');
    const finalStructure = await client.query(`
      SELECT 
        account_code,
        account_name,
        account_type,
        account_level,
        is_main_account,
        linked_table,
        auto_create_sub_accounts,
        sub_account_prefix
      FROM chart_of_accounts 
      ORDER BY account_code
    `);

    finalStructure.rows.forEach(row => {
      const indent = '  '.repeat(row.account_level - 1);
      const linkInfo = row.linked_table ? ` [مربوط بـ ${row.linked_table}]` : '';
      const autoCreate = row.auto_create_sub_accounts ? ` [إنشاء تلقائي: ${row.sub_account_prefix}]` : '';
      console.log(`${indent}${row.account_code} - ${row.account_name}${linkInfo}${autoCreate}`);
    });

    console.log('🎉 تم تحديث جدول دليل الحسابات بنجاح!');
    console.log('📋 الميزات الجديدة:');
    console.log('   ✅ هيكل شجري للحسابات (أب ← فرع)');
    console.log('   ✅ ربط الحسابات بالجداول الأخرى');
    console.log('   ✅ إنشاء حسابات فرعية تلقائياً');
    console.log('   ✅ تصنيف الحسابات (رئيسي/فرعي)');
    console.log('   ✅ طبيعة الحساب (مدين/دائن)');
    
  } catch (error) {
    console.error('❌ خطأ في تحديث جدول دليل الحسابات:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
checkAndUpdateChartOfAccounts();
