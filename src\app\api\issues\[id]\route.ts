import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب قضية واحدة
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    const result = await query(
      'SELECT * FROM issues WHERE id = $1',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error fetching issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث قضية
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const body = await request.json()

    const {
      case_number, title, description, client_name, court_name,
      issue_type, status, amount, notes, contract_method, contract_date
    } = body

    console.log('Received data for update:', {
      case_number, title, description, client_name, court_name,
      issue_type, status, amount, notes, contract_method, contract_date
    })

    if (!case_number || !title || !client_name) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية والعنوان واسم الموكل مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود القضية
    const existingIssue = await query(
      'SELECT id FROM issues WHERE id = $1',
      [id]
    )

    if (existingIssue.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار رقم القضية (باستثناء القضية الحالية)
    const duplicateCheck = await query(
      'SELECT id FROM issues WHERE case_number = $1 AND id != $2',
      [case_number, id]
    )

    if (duplicateCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // تحديث بيانات القضية
    const result = await query(`
      UPDATE issues
      SET case_number = $1, title = $2, description = $3, client_name = $4,
          court_name = $5, issue_type = $6, status = $7, amount = $8,
          notes = $9, contract_method = $10, contract_date = $11,
          updated_date = CURRENT_TIMESTAMP
      WHERE id = $12
      RETURNING *
    `, [
      case_number, title, description, client_name, court_name,
      issue_type, status, parseFloat(amount) || 0, notes, contract_method, contract_date, id
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating issue:', error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    })
    return NextResponse.json(
      { success: false, error: `فشل في تحديث القضية: ${error.message}` },
      { status: 500 }
    )
  }
}

// DELETE - حذف قضية
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    // التحقق من وجود القضية
    const existingIssue = await query(
      'SELECT id, case_number FROM issues WHERE id = $1',
      [id]
    )

    if (existingIssue.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    // حذف القضية
    await query('DELETE FROM issues WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف القضية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف القضية' },
      { status: 500 }
    )
  }
}
