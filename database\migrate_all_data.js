// نسخ جميع البيانات التجريبية إلى قاعدة البيانات الحقيقية
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123'
};

// البيانات التجريبية لجميع الجداول
const sampleData = {
  // 1. الأرصدة الافتتاحية
  opening_balances: [
    { account_name: 'النقدية بالصندوق', account_code: '1001', debit_amount: 50000, credit_amount: 0, balance_type: 'مدين' },
    { account_name: 'البنك الأهلي', account_code: '1002', debit_amount: 150000, credit_amount: 0, balance_type: 'مدين' },
    { account_name: 'العملاء', account_code: '1101', debit_amount: 75000, credit_amount: 0, balance_type: 'مدين' },
    { account_name: 'المخزون', account_code: '1201', debit_amount: 25000, credit_amount: 0, balance_type: 'مدين' },
    { account_name: 'رأس المال', account_code: '3001', debit_amount: 0, credit_amount: 200000, balance_type: 'دائن' },
    { account_name: 'الموردين', account_code: '2001', debit_amount: 0, credit_amount: 50000, balance_type: 'دائن' },
    { account_name: 'مصروفات التأسيس', account_code: '1301', debit_amount: 15000, credit_amount: 0, balance_type: 'مدين' },
    { account_name: 'إيرادات الخدمات', account_code: '4001', debit_amount: 0, credit_amount: 65000, balance_type: 'دائن' }
  ],

  // 2. مراكز التكلفة
  cost_centers: [
    { name: 'قسم القضايا المدنية', code: 'CC001', description: 'مركز تكلفة القضايا المدنية' },
    { name: 'قسم القضايا التجارية', code: 'CC002', description: 'مركز تكلفة القضايا التجارية' },
    { name: 'قسم القضايا الجنائية', code: 'CC003', description: 'مركز تكلفة القضايا الجنائية' },
    { name: 'قسم الاستشارات', code: 'CC004', description: 'مركز تكلفة الاستشارات القانونية' },
    { name: 'الإدارة العامة', code: 'CC005', description: 'مركز تكلفة الإدارة العامة' }
  ],

  // 3. دليل الحسابات
  chart_of_accounts: [
    { account_name: 'الأصول المتداولة', account_code: '1000', account_type: 'أصول', parent_id: null, is_main: true },
    { account_name: 'النقدية بالصندوق', account_code: '1001', account_type: 'أصول', parent_id: 1, is_main: false },
    { account_name: 'البنوك', account_code: '1002', account_type: 'أصول', parent_id: 1, is_main: false },
    { account_name: 'العملاء', account_code: '1101', account_type: 'أصول', parent_id: 1, is_main: false },
    { account_name: 'الخصوم المتداولة', account_code: '2000', account_type: 'خصوم', parent_id: null, is_main: true },
    { account_name: 'الموردين', account_code: '2001', account_type: 'خصوم', parent_id: 5, is_main: false },
    { account_name: 'حقوق الملكية', account_code: '3000', account_type: 'حقوق ملكية', parent_id: null, is_main: true },
    { account_name: 'رأس المال', account_code: '3001', account_type: 'حقوق ملكية', parent_id: 7, is_main: false },
    { account_name: 'الإيرادات', account_code: '4000', account_type: 'إيرادات', parent_id: null, is_main: true },
    { account_name: 'إيرادات الخدمات', account_code: '4001', account_type: 'إيرادات', parent_id: 9, is_main: false }
  ],

  // 4. سندات القبض
  receipt_vouchers: [
    { voucher_number: 'RV001', amount: 5000, description: 'استلام أتعاب قضية مدنية', client_name: 'أحمد محمد', account_name: 'إيرادات الخدمات' },
    { voucher_number: 'RV002', amount: 3000, description: 'استلام أتعاب استشارة قانونية', client_name: 'فاطمة علي', account_name: 'إيرادات الخدمات' },
    { voucher_number: 'RV003', amount: 7500, description: 'استلام أتعاب قضية تجارية', client_name: 'شركة النور', account_name: 'إيرادات الخدمات' },
    { voucher_number: 'RV004', amount: 2000, description: 'استلام أتعاب صياغة عقد', client_name: 'محمد سالم', account_name: 'إيرادات الخدمات' }
  ],

  // 5. سندات الصرف
  payment_vouchers: [
    { voucher_number: 'PV001', amount: 1500, description: 'مصروفات انتقال للمحكمة', employee_name: 'ماجد أحمد', account_name: 'مصروفات الانتقال' },
    { voucher_number: 'PV002', amount: 500, description: 'رسوم قضائية', employee_name: 'يحيى علي', account_name: 'الرسوم القضائية' },
    { voucher_number: 'PV003', amount: 2000, description: 'مصروفات طباعة وثائق', employee_name: 'أحمد صالح', account_name: 'مصروفات مكتبية' },
    { voucher_number: 'PV004', amount: 800, description: 'مصروفات اتصالات', employee_name: 'محمد صالح', account_name: 'مصروفات الاتصالات' }
  ]
};

// استعلامات إنشاء الجداول
const createTableQueries = [
  // جدول الأرصدة الافتتاحية
  `
  CREATE TABLE IF NOT EXISTS opening_balances (
    id SERIAL PRIMARY KEY,
    account_name VARCHAR(255) NOT NULL,
    account_code VARCHAR(50) NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    balance_type VARCHAR(20) NOT NULL,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // جدول مراكز التكلفة
  `
  CREATE TABLE IF NOT EXISTS cost_centers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // جدول دليل الحسابات
  `
  CREATE TABLE IF NOT EXISTS chart_of_accounts (
    id SERIAL PRIMARY KEY,
    account_name VARCHAR(255) NOT NULL,
    account_code VARCHAR(50) UNIQUE NOT NULL,
    account_type VARCHAR(100) NOT NULL,
    parent_id INTEGER REFERENCES chart_of_accounts(id),
    is_main BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // جدول سندات القبض
  `
  CREATE TABLE IF NOT EXISTS receipt_vouchers (
    id SERIAL PRIMARY KEY,
    voucher_number VARCHAR(50) UNIQUE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    client_name VARCHAR(255),
    account_name VARCHAR(255),
    voucher_date DATE DEFAULT CURRENT_DATE,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // جدول سندات الصرف
  `
  CREATE TABLE IF NOT EXISTS payment_vouchers (
    id SERIAL PRIMARY KEY,
    voucher_number VARCHAR(50) UNIQUE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    employee_name VARCHAR(255),
    account_name VARCHAR(255),
    voucher_date DATE DEFAULT CURRENT_DATE,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `
];

async function migrateAllData() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إنشاء الجداول
    console.log('🔄 جاري إنشاء الجداول...');
    for (let i = 0; i < createTableQueries.length; i++) {
      try {
        await client.query(createTableQueries[i]);
        console.log(`✅ تم إنشاء الجدول ${i + 1}/${createTableQueries.length}`);
      } catch (error) {
        console.error(`❌ خطأ في إنشاء الجدول ${i + 1}:`, error.message);
      }
    }

    // نسخ البيانات
    console.log('🔄 جاري نسخ البيانات...');

    // 1. الأرصدة الافتتاحية
    await client.query('TRUNCATE TABLE opening_balances RESTART IDENTITY CASCADE');
    for (const balance of sampleData.opening_balances) {
      await client.query(`
        INSERT INTO opening_balances (account_name, account_code, debit_amount, credit_amount, balance_type)
        VALUES ($1, $2, $3, $4, $5)
      `, [balance.account_name, balance.account_code, balance.debit_amount, balance.credit_amount, balance.balance_type]);
    }
    console.log(`✅ تم نسخ ${sampleData.opening_balances.length} رصيد افتتاحي`);

    // 2. مراكز التكلفة
    await client.query('TRUNCATE TABLE cost_centers RESTART IDENTITY CASCADE');
    for (const center of sampleData.cost_centers) {
      await client.query(`
        INSERT INTO cost_centers (name, code, description)
        VALUES ($1, $2, $3)
      `, [center.name, center.code, center.description]);
    }
    console.log(`✅ تم نسخ ${sampleData.cost_centers.length} مركز تكلفة`);

    // 3. دليل الحسابات
    await client.query('TRUNCATE TABLE chart_of_accounts RESTART IDENTITY CASCADE');
    for (const account of sampleData.chart_of_accounts) {
      await client.query(`
        INSERT INTO chart_of_accounts (account_name, account_code, account_type, parent_id, is_main)
        VALUES ($1, $2, $3, $4, $5)
      `, [account.account_name, account.account_code, account.account_type, account.parent_id, account.is_main]);
    }
    console.log(`✅ تم نسخ ${sampleData.chart_of_accounts.length} حساب`);

    // 4. سندات القبض
    await client.query('TRUNCATE TABLE receipt_vouchers RESTART IDENTITY CASCADE');
    for (const voucher of sampleData.receipt_vouchers) {
      await client.query(`
        INSERT INTO receipt_vouchers (voucher_number, amount, description, client_name, account_name)
        VALUES ($1, $2, $3, $4, $5)
      `, [voucher.voucher_number, voucher.amount, voucher.description, voucher.client_name, voucher.account_name]);
    }
    console.log(`✅ تم نسخ ${sampleData.receipt_vouchers.length} سند قبض`);

    // 5. سندات الصرف
    await client.query('TRUNCATE TABLE payment_vouchers RESTART IDENTITY CASCADE');
    for (const voucher of sampleData.payment_vouchers) {
      await client.query(`
        INSERT INTO payment_vouchers (voucher_number, amount, description, employee_name, account_name)
        VALUES ($1, $2, $3, $4, $5)
      `, [voucher.voucher_number, voucher.amount, voucher.description, voucher.employee_name, voucher.account_name]);
    }
    console.log(`✅ تم نسخ ${sampleData.payment_vouchers.length} سند صرف`);

    // التحقق من النتائج
    console.log('🔄 جاري التحقق من النتائج...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM opening_balances'),
      client.query('SELECT COUNT(*) FROM cost_centers'),
      client.query('SELECT COUNT(*) FROM chart_of_accounts'),
      client.query('SELECT COUNT(*) FROM receipt_vouchers'),
      client.query('SELECT COUNT(*) FROM payment_vouchers')
    ]);

    console.log('📋 ملخص البيانات المنسوخة:');
    console.log(`   ✅ الأرصدة الافتتاحية: ${results[0].rows[0].count} سجل`);
    console.log(`   ✅ مراكز التكلفة: ${results[1].rows[0].count} سجل`);
    console.log(`   ✅ دليل الحسابات: ${results[2].rows[0].count} سجل`);
    console.log(`   ✅ سندات القبض: ${results[3].rows[0].count} سجل`);
    console.log(`   ✅ سندات الصرف: ${results[4].rows[0].count} سجل`);

    console.log('🎉 تم نسخ جميع البيانات التجريبية إلى قاعدة البيانات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في نسخ البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل النسخ
migrateAllData();
