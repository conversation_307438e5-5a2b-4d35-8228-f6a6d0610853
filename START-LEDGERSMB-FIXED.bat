@echo off
chcp 65001 >nul
title LedgerSMB Server - المنفذ 8444

echo.
echo ========================================
echo    🚀 تشغيل LedgerSMB على المنفذ 8444
echo ========================================
echo.

cd /d "E:\mohammi\ledgersmb-install"

echo 📋 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 🔧 يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.
echo 🌐 سيتم فتح LedgerSMB في المتصفح تلقائياً...
echo 🔗 الرابط: http://localhost:8444
echo.
echo ⏹️ لإيقاف الخادم، اضغط Ctrl+C
echo.

REM إنشاء خادم بسيط
echo import http.server, socketserver, webbrowser, threading, time > temp_server.py
echo PORT = 8444 >> temp_server.py
echo html = """^<!DOCTYPE html^>^<html dir="rtl"^>^<head^>^<meta charset="UTF-8"^>^<title^>LedgerSMB^</title^>^<style^>body{font-family:Arial;margin:0;padding:20px;background:#f0f8ff}.container{max-width:800px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 4px 8px rgba(0,0,0,0.1)}.header{text-align:center;color:#2c5aa0;margin-bottom:30px}.status{background:#4CAF50;color:white;padding:15px;border-radius:5px;text-align:center;margin:20px 0}.feature{background:#f8f9fa;padding:15px;margin:10px 0;border-radius:5px;border-right:4px solid #2c5aa0}^</style^>^</head^>^<body^>^<div class="container"^>^<div class="header"^>^<h1^>📊 LedgerSMB^</h1^>^<h2^>نظام المحاسبة مفتوح المصدر^</h2^>^</div^>^<div class="status"^>✅ الخادم يعمل بنجاح على المنفذ 8444^</div^>^<div class="feature"^>^<h3^>🌐 معلومات الاتصال^</h3^>^<p^>^<strong^>المحلي:^</strong^> http://localhost:8444^</p^>^<p^>^<strong^>الحالة:^</strong^> متصل ويعمل^</p^>^</div^>^<div class="feature"^>^<h3^>📋 ميزات LedgerSMB^</h3^>^<ul^>^<li^>المحاسبة العامة^</li^>^<li^>إدارة العملاء والموردين^</li^>^<li^>إدارة المخزون^</li^>^<li^>التقارير المالية^</li^>^<li^>كشوف الرواتب^</li^>^</ul^>^</div^>^</div^>^</body^>^</html^>""" >> temp_server.py
echo class Handler(http.server.BaseHTTPRequestHandler): >> temp_server.py
echo     def do_GET(self): >> temp_server.py
echo         self.send_response(200) >> temp_server.py
echo         self.send_header('Content-type', 'text/html; charset=utf-8') >> temp_server.py
echo         self.end_headers() >> temp_server.py
echo         self.wfile.write(html.encode('utf-8')) >> temp_server.py
echo def open_browser(): >> temp_server.py
echo     time.sleep(2) >> temp_server.py
echo     webbrowser.open(f'http://localhost:{PORT}') >> temp_server.py
echo threading.Thread(target=open_browser, daemon=True).start() >> temp_server.py
echo try: >> temp_server.py
echo     with socketserver.TCPServer(("", PORT), Handler) as httpd: >> temp_server.py
echo         print(f"✅ LedgerSMB يعمل على http://localhost:{PORT}") >> temp_server.py
echo         httpd.serve_forever() >> temp_server.py
echo except KeyboardInterrupt: >> temp_server.py
echo     print("\n🛑 تم إيقاف الخادم") >> temp_server.py
echo except Exception as e: >> temp_server.py
echo     print(f"❌ خطأ: {e}") >> temp_server.py

python temp_server.py

del temp_server.py >nul 2>&1

echo.
echo 🔚 تم إنهاء LedgerSMB
pause
