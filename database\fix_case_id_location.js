const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function fixCaseIdLocation() {
  try {
    console.log('🔧 إصلاح موقع case_id في الجداول...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. فحص الوضع الحالي
    console.log('\n📋 فحص الوضع الحالي...');
    
    // فحص journal_entries
    const journalEntriesColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'journal_entries' 
      AND column_name IN ('case_id', 'case_number')
    `);

    // فحص journal_entry_details
    const journalDetailsColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'journal_entry_details' 
      AND column_name = 'case_id'
    `);

    console.log('📊 الوضع الحالي:');
    console.log(`   - journal_entries.case_id: ${journalEntriesColumns.rows.some(r => r.column_name === 'case_id') ? 'موجود ❌' : 'غير موجود ✅'}`);
    console.log(`   - journal_entries.case_number: ${journalEntriesColumns.rows.some(r => r.column_name === 'case_number') ? 'موجود ❌' : 'غير موجود ✅'}`);
    console.log(`   - journal_entry_details.case_id: ${journalDetailsColumns.rows.length > 0 ? 'موجود ✅' : 'غير موجود ❌'}`);

    // 2. إزالة case_id و case_number من journal_entries إذا كانا موجودين
    if (journalEntriesColumns.rows.some(r => r.column_name === 'case_id')) {
      console.log('\n🗑️ إزالة case_id من journal_entries...');
      
      // إزالة Foreign Key أولاً
      try {
        const constraints = await client.query(`
          SELECT constraint_name 
          FROM information_schema.table_constraints 
          WHERE table_name = 'journal_entries' 
          AND constraint_type = 'FOREIGN KEY'
          AND constraint_name LIKE '%case%'
        `);

        for (const constraint of constraints.rows) {
          await client.query(`ALTER TABLE journal_entries DROP CONSTRAINT ${constraint.constraint_name}`);
          console.log(`     ✅ تم حذف ${constraint.constraint_name}`);
        }
      } catch (error) {
        console.log(`     ⚠️ خطأ في حذف Foreign Key: ${error.message}`);
      }

      // حذف العمود
      try {
        await client.query(`ALTER TABLE journal_entries DROP COLUMN case_id`);
        console.log('     ✅ تم حذف عمود case_id من journal_entries');
      } catch (error) {
        console.log(`     ⚠️ خطأ في حذف case_id: ${error.message}`);
      }
    }

    if (journalEntriesColumns.rows.some(r => r.column_name === 'case_number')) {
      console.log('\n🗑️ إزالة case_number من journal_entries...');
      
      try {
        await client.query(`ALTER TABLE journal_entries DROP COLUMN case_number`);
        console.log('     ✅ تم حذف عمود case_number من journal_entries');
      } catch (error) {
        console.log(`     ⚠️ خطأ في حذف case_number: ${error.message}`);
      }
    }

    // 3. التأكد من وجود case_id في journal_entry_details
    if (journalDetailsColumns.rows.length === 0) {
      console.log('\n➕ إضافة case_id إلى journal_entry_details...');
      
      try {
        await client.query(`
          ALTER TABLE journal_entry_details 
          ADD COLUMN case_id INTEGER REFERENCES issues(id) ON DELETE SET NULL
        `);
        console.log('     ✅ تم إضافة عمود case_id إلى journal_entry_details');
        
        // إضافة فهرس للأداء
        await client.query(`
          CREATE INDEX IF NOT EXISTS idx_journal_entry_details_case_id 
          ON journal_entry_details(case_id)
        `);
        console.log('     ✅ تم إضافة فهرس case_id');
      } catch (error) {
        console.log(`     ⚠️ خطأ في إضافة case_id: ${error.message}`);
      }
    }

    // 4. فحص النتائج النهائية
    console.log('\n📊 فحص النتائج النهائية...');
    
    const finalJournalEntriesColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'journal_entries' 
      AND column_name IN ('case_id', 'case_number')
    `);

    const finalJournalDetailsColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'journal_entry_details' 
      AND column_name = 'case_id'
    `);

    console.log('✅ النتائج النهائية:');
    console.log(`   - journal_entries.case_id: ${finalJournalEntriesColumns.rows.some(r => r.column_name === 'case_id') ? 'موجود ❌' : 'غير موجود ✅'}`);
    console.log(`   - journal_entries.case_number: ${finalJournalEntriesColumns.rows.some(r => r.column_name === 'case_number') ? 'موجود ❌' : 'غير موجود ✅'}`);
    console.log(`   - journal_entry_details.case_id: ${finalJournalDetailsColumns.rows.length > 0 ? 'موجود ✅' : 'غير موجود ❌'}`);

    // 5. اختبار العلاقة
    console.log('\n🧪 اختبار العلاقة الجديدة...');
    
    try {
      const testQuery = await client.query(`
        SELECT 
          jed.id,
          jed.case_id,
          i.case_number,
          i.title,
          jed.debit_amount,
          jed.credit_amount
        FROM journal_entry_details jed
        LEFT JOIN issues i ON jed.case_id = i.id
        WHERE jed.case_id IS NOT NULL
        LIMIT 3
      `);
      
      if (testQuery.rows.length > 0) {
        console.log('✅ العلاقة تعمل بشكل صحيح:');
        testQuery.rows.forEach(row => {
          console.log(`   - عملية ${row.id}: قضية ${row.case_number} (${row.title})`);
          console.log(`     مدين: ${row.debit_amount}, دائن: ${row.credit_amount}`);
        });
      } else {
        console.log('⚠️ لا توجد عمليات مرتبطة بقضايا حالياً');
      }
    } catch (error) {
      console.log('❌ خطأ في اختبار العلاقة:', error.message);
    }

    console.log('\n🎯 الهيكل الصحيح الآن:');
    console.log('');
    console.log('   journal_entries (السندات الرئيسية)');
    console.log('   ├── id (Primary Key)');
    console.log('   ├── entry_number');
    console.log('   ├── entry_date');
    console.log('   ├── description');
    console.log('   └── ❌ بدون case_id (صحيح)');
    console.log('');
    console.log('   journal_entry_details (العمليات المالية)');
    console.log('   ├── id (Primary Key)');
    console.log('   ├── journal_entry_id → journal_entries(id)');
    console.log('   ├── account_id → chart_of_accounts(id)');
    console.log('   ├── debit_amount');
    console.log('   ├── credit_amount');
    console.log('   └── ✅ case_id → issues(id) (صحيح)');

    console.log('\n📋 التطبيق العملي:');
    console.log('✅ كل عملية مالية يمكن ربطها بقضية مختلفة');
    console.log('✅ سند واحد يمكن أن يحتوي على عمليات لقضايا متعددة');
    console.log('✅ مرونة كاملة في ربط العمليات بالقضايا');
    console.log('✅ تقارير دقيقة حسب القضية');

    console.log('\n🎉 تم إصلاح موقع case_id بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح موقع case_id:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
fixCaseIdLocation();
