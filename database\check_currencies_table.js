const { Client } = require('pg');

const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function checkCurrenciesTable() {
  try {
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // فحص هيكل جدول العملات
    const structure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'currencies'
      ORDER BY ordinal_position
    `);

    console.log('\n📋 هيكل جدول العملات الحالي:');
    structure.rows.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`);
    });

    // فحص البيانات الموجودة
    const data = await client.query('SELECT * FROM currencies LIMIT 5');
    console.log('\n📊 البيانات الموجودة:');
    data.rows.forEach(row => {
      console.log(`   ${JSON.stringify(row)}`);
    });

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkCurrenciesTable();
