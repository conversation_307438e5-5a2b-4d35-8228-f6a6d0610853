import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب الإعلانات
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        id,
        announcement_1,
        announcement_2,
        announcement_3,
        announcement_4,
        is_active,
        created_date,
        updated_at
      FROM announcements
      ORDER BY id DESC
      LIMIT 1
    `)

    return NextResponse.json({
      success: true,
      data: result.rows.length > 0 ? result.rows[0] : null,
      message: 'تم جلب الإعلانات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الإعلانات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الإعلانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة إعلانات جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      announcement_1 = '',
      announcement_2 = '',
      announcement_3 = '',
      announcement_4 = '',
      is_active = true
    } = body

    const result = await query(`
      INSERT INTO announcements (announcement_1, announcement_2, announcement_3, announcement_4, is_active)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [announcement_1, announcement_2, announcement_3, announcement_4, is_active])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الإعلانات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الإعلانات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الإعلانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث الإعلانات
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      announcement_1 = '',
      announcement_2 = '',
      announcement_3 = '',
      announcement_4 = '',
      is_active = true
    } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الإعلانات مطلوب'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE announcements 
      SET announcement_1 = $1, announcement_2 = $2, announcement_3 = $3, 
          announcement_4 = $4, is_active = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [announcement_1, announcement_2, announcement_3, announcement_4, is_active, id])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الإعلانات غير موجودة'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الإعلانات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الإعلانات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الإعلانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف الإعلانات
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الإعلانات مطلوب'
      }, { status: 400 })
    }

    const result = await query(
      'DELETE FROM announcements WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الإعلانات غير موجودة'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإعلانات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الإعلانات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف الإعلانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
