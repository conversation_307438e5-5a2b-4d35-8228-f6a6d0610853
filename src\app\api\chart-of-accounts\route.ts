import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'all', 'accounts', 'headings'
    const category = searchParams.get('category') // 'A', 'L', 'Q', 'I', 'E'
    const active = searchParams.get('active') // 'true', 'false'

    let whereClause = 'WHERE 1=1'
    const params: any[] = []

    if (type === 'accounts') {
      whereClause += ' AND charttype = $' + (params.length + 1)
      params.push('A')
    } else if (type === 'headings') {
      whereClause += ' AND charttype = $' + (params.length + 1)
      params.push('H')
    }

    if (category) {
      whereClause += ' AND category = $' + (params.length + 1)
      params.push(category)
    }

    if (active === 'true') {
      whereClause += ' AND obsolete = false'
    }

    // جلب الحسابات مع الأرصدة
    const result = await query(`
      SELECT 
        coa.*,
        COALESCE(
          (SELECT SUM(amount) 
           FROM acc_trans at 
           WHERE at.chart_id = coa.id 
           AND at.approved = true), 
          0
        ) as balance,
        h.description as heading_name
      FROM chart_of_accounts coa
      LEFT JOIN chart_of_accounts h ON coa.heading = h.id
      ${whereClause}
      ORDER BY coa.accno
    `, params)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching chart of accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب دليل الحسابات' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      accno,
      description,
      charttype = 'A',
      category,
      contra = false,
      tax = false,
      link,
      heading
    } = body

    // التحقق من البيانات المطلوبة
    if (!accno || !description || !category) {
      return NextResponse.json(
        { success: false, error: 'رقم الحساب والوصف والفئة مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم الحساب
    const existingAccount = await query(
      'SELECT id FROM chart_of_accounts WHERE accno = $1',
      [accno]
    )

    if (existingAccount.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم الحساب موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إدراج الحساب الجديد
    const result = await query(`
      INSERT INTO chart_of_accounts (
        accno, description, charttype, category, 
        contra, tax, link, heading
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [accno, description, charttype, category, contra, tax, link, heading])

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء الحساب بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الحساب' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      accno,
      description,
      charttype,
      category,
      contra,
      tax,
      link,
      heading,
      obsolete
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب مطلوب' },
        { status: 400 }
      )
    }

    // تحديث الحساب
    const result = await query(`
      UPDATE chart_of_accounts 
      SET 
        accno = COALESCE($2, accno),
        description = COALESCE($3, description),
        charttype = COALESCE($4, charttype),
        category = COALESCE($5, category),
        contra = COALESCE($6, contra),
        tax = COALESCE($7, tax),
        link = COALESCE($8, link),
        heading = $9,
        obsolete = COALESCE($10, obsolete)
      WHERE id = $1
      RETURNING *
    `, [id, accno, description, charttype, category, contra, tax, link, heading, obsolete])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الحساب بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الحساب' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود معاملات على الحساب
    const transactionsCheck = await query(
      'SELECT COUNT(*) as count FROM acc_trans WHERE chart_id = $1',
      [id]
    )

    if (parseInt(transactionsCheck.rows[0].count) > 0) {
      // إذا كان هناك معاملات، قم بوضع علامة obsolete بدلاً من الحذف
      await query(
        'UPDATE chart_of_accounts SET obsolete = true WHERE id = $1',
        [id]
      )

      return NextResponse.json({
        success: true,
        message: 'تم إلغاء الحساب (يحتوي على معاملات)'
      })
    } else {
      // إذا لم تكن هناك معاملات، احذف الحساب
      const result = await query(
        'DELETE FROM chart_of_accounts WHERE id = $1 RETURNING *',
        [id]
      )

      if (result.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'الحساب غير موجود' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'تم حذف الحساب بنجاح'
      })
    }
  } catch (error) {
    console.error('Error deleting account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الحساب' },
      { status: 500 }
    )
  }
}
