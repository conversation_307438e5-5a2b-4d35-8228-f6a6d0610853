'use client'

import { ReactNode } from 'react'
import { CompanyHeader } from './company-header'
import { PrintFooter } from './print-footer'

interface PrintLayoutProps {
  title?: string
  subtitle?: string
  children: ReactNode
  showLogo?: boolean
  showContactInfo?: boolean
  showSignatures?: boolean
  showDate?: boolean
  customSignatures?: string[]
  className?: string
}

export function PrintLayout({
  title = '',
  subtitle = '',
  children,
  showLogo = true,
  showContactInfo = true,
  showSignatures = true,
  showDate = true,
  customSignatures = [],
  className = ''
}: PrintLayoutProps) {
  return (
    <div className={`print-layout ${className}`}>
      <style jsx global>{`
        @media print {
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
          
          body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: white;
          }
          
          .print-layout {
            width: 100%;
            max-width: none;
            margin: 0;
            padding: 15mm;
            box-shadow: none;
            background: white;
          }
          
          .no-print {
            display: none !important;
          }
          
          .page-break {
            page-break-before: always;
          }
          
          .page-break-inside-avoid {
            page-break-inside: avoid;
          }
          
          table {
            border-collapse: collapse;
            width: 100%;
          }
          
          table, th, td {
            border: 1px solid #000;
          }
          
          th, td {
            padding: 8px;
            text-align: right;
          }
          
          th {
            background-color: #f3f4f6 !important;
            font-weight: bold;
          }
          
          .text-right {
            text-align: right;
          }
          
          .text-center {
            text-align: center;
          }
          
          .text-left {
            text-align: left;
          }
          
          .font-bold {
            font-weight: bold;
          }
          
          .text-red-600 {
            color: #dc2626 !important;
          }
          
          .text-green-600 {
            color: #16a34a !important;
          }
          
          .text-blue-600 {
            color: #2563eb !important;
          }
          
          .text-gray-600 {
            color: #4b5563 !important;
          }
          
          .bg-gray-50 {
            background-color: #f9fafb !important;
          }
          
          .bg-blue-50 {
            background-color: #eff6ff !important;
          }
          
          .border {
            border: 1px solid #d1d5db;
          }
          
          .border-t {
            border-top: 1px solid #d1d5db;
          }
          
          .border-b {
            border-bottom: 1px solid #d1d5db;
          }
          
          .rounded {
            border-radius: 4px;
          }
          
          .p-2 {
            padding: 8px;
          }
          
          .p-3 {
            padding: 12px;
          }
          
          .p-4 {
            padding: 16px;
          }
          
          .mb-2 {
            margin-bottom: 8px;
          }
          
          .mb-3 {
            margin-bottom: 12px;
          }
          
          .mb-4 {
            margin-bottom: 16px;
          }
          
          .mt-2 {
            margin-top: 8px;
          }
          
          .mt-3 {
            margin-top: 12px;
          }
          
          .mt-4 {
            margin-top: 16px;
          }
        }
        
        .print-layout {
          width: 100%;
          max-width: 210mm;
          margin: 0 auto;
          padding: 20px;
          background: white;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
          min-height: 297mm;
          position: relative;
        }
        
        @page {
          size: A4;
          margin: 15mm;
        }
      `}</style>

      {/* رأس الصفحة */}
      <CompanyHeader 
        title={title}
        subtitle={subtitle}
        showLogo={showLogo}
        showContactInfo={showContactInfo}
      />

      {/* محتوى الصفحة */}
      <div className="print-content">
        {children}
      </div>

      {/* تذييل الصفحة */}
      <PrintFooter 
        showSignatures={showSignatures}
        showDate={showDate}
        customSignatures={customSignatures}
      />
    </div>
  )
}
