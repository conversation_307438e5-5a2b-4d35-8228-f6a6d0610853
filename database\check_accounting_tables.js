// التحقق من الجداول المحاسبية
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkAccountingTables() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔍 التحقق من الجداول المحاسبية...');
    await client.connect();

    // البحث عن جميع الجداول المحاسبية
    const accountingTables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND (
        table_name LIKE '%voucher%' OR 
        table_name LIKE '%journal%' OR 
        table_name LIKE '%entry%' OR 
        table_name LIKE '%receipt%' OR 
        table_name LIKE '%payment%' OR
        table_name LIKE '%transaction%' OR
        table_name LIKE '%account%'
      )
      ORDER BY table_name
    `);

    console.log('📋 الجداول المحاسبية الموجودة:');
    accountingTables.rows.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });

    // فحص جدول journal_entries (القيود اليومية)
    console.log('\n📊 جدول journal_entries (القيود اليومية):');
    try {
      const journalColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'journal_entries' 
        ORDER BY ordinal_position
      `);

      if (journalColumns.rows.length > 0) {
        journalColumns.rows.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
        });

        // عينة من البيانات
        const sampleJournal = await client.query('SELECT * FROM journal_entries LIMIT 3');
        console.log('\n📋 عينة من بيانات journal_entries:');
        if (sampleJournal.rows.length > 0) {
          sampleJournal.rows.forEach((row, index) => {
            console.log(`   ${index + 1}. ID: ${row.id}, Entry: ${row.entry_number || 'N/A'}, Amount: ${row.debit_amount || row.credit_amount || 'N/A'}`);
          });
        } else {
          console.log('   لا توجد بيانات');
        }
      } else {
        console.log('   ❌ جدول journal_entries غير موجود');
      }
    } catch (error) {
      console.log('   ❌ جدول journal_entries غير موجود');
    }

    // فحص جدول vouchers (السندات العامة)
    console.log('\n📊 جدول vouchers (السندات العامة):');
    try {
      const vouchersColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'vouchers' 
        ORDER BY ordinal_position
      `);

      if (vouchersColumns.rows.length > 0) {
        vouchersColumns.rows.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
        });

        // عينة من البيانات
        const sampleVouchers = await client.query('SELECT * FROM vouchers LIMIT 3');
        console.log('\n📋 عينة من بيانات vouchers:');
        if (sampleVouchers.rows.length > 0) {
          sampleVouchers.rows.forEach((row, index) => {
            console.log(`   ${index + 1}. ID: ${row.id}, Type: ${row.voucher_type || 'N/A'}, Number: ${row.voucher_number || 'N/A'}, Amount: ${row.amount || 'N/A'}`);
          });
        } else {
          console.log('   لا توجد بيانات');
        }
      } else {
        console.log('   ❌ جدول vouchers غير موجود');
      }
    } catch (error) {
      console.log('   ❌ جدول vouchers غير موجود');
    }

    // فحص جدول voucher_entries (قيود السندات)
    console.log('\n📊 جدول voucher_entries (قيود السندات):');
    try {
      const voucherEntriesColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'voucher_entries' 
        ORDER BY ordinal_position
      `);

      if (voucherEntriesColumns.rows.length > 0) {
        voucherEntriesColumns.rows.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
        });

        // عينة من البيانات
        const sampleVoucherEntries = await client.query('SELECT * FROM voucher_entries LIMIT 3');
        console.log('\n📋 عينة من بيانات voucher_entries:');
        if (sampleVoucherEntries.rows.length > 0) {
          sampleVoucherEntries.rows.forEach((row, index) => {
            console.log(`   ${index + 1}. ID: ${row.id}, Voucher: ${row.voucher_id || 'N/A'}, Account: ${row.account_id || 'N/A'}, Debit: ${row.debit_amount || 0}, Credit: ${row.credit_amount || 0}`);
          });
        } else {
          console.log('   لا توجد بيانات');
        }
      } else {
        console.log('   ❌ جدول voucher_entries غير موجود');
      }
    } catch (error) {
      console.log('   ❌ جدول voucher_entries غير موجود');
    }

    // فحص العلاقات بين الجداول
    console.log('\n🔗 العلاقات بين الجداول المحاسبية:');
    const accountingForeignKeys = await client.query(`
      SELECT 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE constraint_type = 'FOREIGN KEY' 
      AND (
        tc.table_name LIKE '%voucher%' OR 
        tc.table_name LIKE '%journal%' OR 
        tc.table_name LIKE '%entry%' OR 
        tc.table_name LIKE '%receipt%' OR 
        tc.table_name LIKE '%payment%'
      )
      ORDER BY tc.table_name, kcu.column_name
    `);

    if (accountingForeignKeys.rows.length > 0) {
      accountingForeignKeys.rows.forEach(fk => {
        console.log(`   - ${fk.table_name}.${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
      });
    } else {
      console.log('   لا توجد علاقات مُعرَّفة');
    }

    // فحص جدول chart_of_accounts
    console.log('\n📊 جدول chart_of_accounts (دليل الحسابات):');
    try {
      const chartColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'chart_of_accounts' 
        ORDER BY ordinal_position
      `);

      if (chartColumns.rows.length > 0) {
        chartColumns.rows.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
        });

        // عدد الحسابات
        const accountsCount = await client.query('SELECT COUNT(*) as count FROM chart_of_accounts');
        console.log(`\n📊 عدد الحسابات: ${accountsCount.rows[0].count}`);
      } else {
        console.log('   ❌ جدول chart_of_accounts غير موجود');
      }
    } catch (error) {
      console.log('   ❌ جدول chart_of_accounts غير موجود');
    }

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkAccountingTables();
