'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { BookOpen, Plus, Edit, Trash2, Search, Calculator, AlertCircle, Eye, Printer, Save, X } from 'lucide-react'

interface JournalEntry {
  id: number
  entry_number: string
  entry_date: string
  description: string
  total_debit: number
  total_credit: number
  currency_id: number
  currency_code?: string
  cost_center_id?: number
  cost_center_name?: string
  case_id?: number
  case_number?: string
  status: string
  created_by_name?: string
  created_date: string
  details?: JournalEntryDetail[]
}

interface JournalEntryDetail {
  id?: number
  line_number: number
  account_id: number
  account_name?: string
  account_code?: string
  debit_amount: number
  credit_amount: number
  currency_id: number
  exchange_rate: number
  cost_center_id?: number
  payment_method_id?: number
  description: string
  reference_number?: string
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  linked_table?: string
}

interface Currency {
  id: number
  currency_code: string
  currency_name: string
  symbol: string
}

interface PaymentMethod {
  id: number
  method_code: string
  method_name: string
}

interface CostCenter {
  id: number
  center_code: string
  center_name: string
}

interface Case {
  id: number
  case_number: string
  title: string
  client_name: string
  status: string
}

interface Service {
  id: number
  name: string
  lineage_id: number
  lineage_name: string
}

export default function JournalEntriesPage() {
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [cases, setCases] = useState<Case[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingEntry, setEditingEntry] = useState<JournalEntry | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // بيانات النموذج
  const [formData, setFormData] = useState({
    entry_date: new Date().toISOString().split('T')[0],
    description: '',
    currency_id: '1',
    cost_center_id: '',
    case_id: '',
    service_id: '',
    reference_number: ''
  })

  // تفاصيل القيد
  const [entryDetails, setEntryDetails] = useState<JournalEntryDetail[]>([
    {
      line_number: 1,
      account_id: 0,
      debit_amount: 0,
      credit_amount: 0,
      currency_id: 1,
      exchange_rate: 1,
      description: ''
    },
    {
      line_number: 2,
      account_id: 0,
      debit_amount: 0,
      credit_amount: 0,
      currency_id: 1,
      exchange_rate: 1,
      description: ''
    }
  ])

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // جلب القيود
      const entriesResponse = await fetch('/api/accounting/journal-entries')
      if (entriesResponse.ok) {
        const entriesData = await entriesResponse.json()
        setEntries(entriesData.entries || [])
      }

      // جلب الحسابات (المستوى 4 فقط + العملاء والموظفين)
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true')
      if (accountsResponse.ok) {
        const accountsData = await accountsResponse.json()
        // ترتيب الحسابات: الحسابات العادية أولاً، ثم العملاء، ثم الموظفين
        const sortedAccounts = accountsData.accounts.sort((a: any, b: any) => {
          if (a.is_linked_record && !b.is_linked_record) return 1
          if (!a.is_linked_record && b.is_linked_record) return -1
          if (a.is_linked_record && b.is_linked_record) {
            if (a.original_table === 'clients' && b.original_table === 'employees') return -1
            if (a.original_table === 'employees' && b.original_table === 'clients') return 1
          }
          return a.account_name.localeCompare(b.account_name, 'ar')
        })
        setAccounts(sortedAccounts)
      }

      // جلب العملات
      const currenciesResponse = await fetch('/api/accounting/currencies')
      if (currenciesResponse.ok) {
        const currenciesData = await currenciesResponse.json()
        setCurrencies(currenciesData.currencies || [])
      }

      // جلب طرق الدفع
      const paymentMethodsResponse = await fetch('/api/accounting/payment-methods')
      if (paymentMethodsResponse.ok) {
        const paymentMethodsData = await paymentMethodsResponse.json()
        setPaymentMethods(paymentMethodsData.methods || [])
      }

      // جلب مراكز التكلفة
      const costCentersResponse = await fetch('/api/accounting/cost-centers')
      if (costCentersResponse.ok) {
        const costCentersData = await costCentersResponse.json()
        setCostCenters(costCentersData.data || [])
      }

      // جلب القضايا
      const casesResponse = await fetch('/api/issues')
      if (casesResponse.ok) {
        const casesData = await casesResponse.json()
        setCases(casesData.data || [])
      }

      // جلب الخدمات
      const servicesResponse = await fetch('/api/services')
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json()
        setServices(servicesData.data || [])
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  // حساب إجماليات القيد
  const calculateTotals = () => {
    const totalDebit = entryDetails.reduce((sum, detail) => sum + (detail.debit_amount || 0), 0)
    const totalCredit = entryDetails.reduce((sum, detail) => sum + (detail.credit_amount || 0), 0)
    return { totalDebit, totalCredit }
  }

  // التحقق من توازن القيد
  const isBalanced = () => {
    const { totalDebit, totalCredit } = calculateTotals()
    return Math.abs(totalDebit - totalCredit) < 0.01 // للتعامل مع أخطاء الفاصلة العشرية
  }

  // إضافة سطر جديد
  const addDetailLine = () => {
    const newLine: JournalEntryDetail = {
      line_number: entryDetails.length + 1,
      account_id: 0,
      debit_amount: 0,
      credit_amount: 0,
      currency_id: 1,
      exchange_rate: 1,
      description: ''
    }
    setEntryDetails([...entryDetails, newLine])
  }

  // حذف سطر
  const removeDetailLine = (index: number) => {
    if (entryDetails.length > 2) { // الحد الأدنى سطرين
      const newDetails = entryDetails.filter((_, i) => i !== index)
      // إعادة ترقيم الأسطر
      const reNumbered = newDetails.map((detail, i) => ({
        ...detail,
        line_number: i + 1
      }))
      setEntryDetails(reNumbered)
    }
  }

  // تحديث تفاصيل السطر
  const updateDetailLine = (index: number, field: keyof JournalEntryDetail, value: any) => {
    const newDetails = [...entryDetails]
    newDetails[index] = { ...newDetails[index], [field]: value }

    // إذا تم إدخال مبلغ مدين، مسح المبلغ الدائن والعكس
    if (field === 'debit_amount' && value > 0) {
      newDetails[index].credit_amount = 0
    } else if (field === 'credit_amount' && value > 0) {
      newDetails[index].debit_amount = 0
    }

    setEntryDetails(newDetails)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // التحقق من توازن القيد
    if (!isBalanced()) {
      alert('القيد غير متوازن! يجب أن تكون إجماليات المدين والدائن متساوية')
      return
    }

    // التحقق من وجود حسابات وأرقام صحيحة
    const validDetails = entryDetails.filter(detail =>
      detail.account_id > 0 && (detail.debit_amount > 0 || detail.credit_amount > 0)
    )

    if (validDetails.length < 2) {
      alert('يجب أن يحتوي القيد على سطرين على الأقل بحسابات وأرقام صحيحة')
      return
    }

    try {
      const { totalDebit, totalCredit } = calculateTotals()

      const url = editingEntry
        ? `/api/accounting/journal-entries/${editingEntry.id}`
        : '/api/accounting/journal-entries'

      const method = editingEntry ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          total_debit: totalDebit,
          total_credit: totalCredit,
          currency_id: parseInt(formData.currency_id),
          cost_center_id: formData.cost_center_id ? parseInt(formData.cost_center_id) : null,
          case_id: formData.case_id ? parseInt(formData.case_id) : null,
          service_id: formData.service_id ? parseInt(formData.service_id) : null,
          details: validDetails.map(detail => ({
            ...detail,
            account_id: parseInt(detail.account_id.toString()),
            currency_id: parseInt(detail.currency_id.toString()),
            cost_center_id: detail.cost_center_id ? parseInt(detail.cost_center_id.toString()) : null,
            payment_method_id: detail.payment_method_id ? parseInt(detail.payment_method_id.toString()) : null
          }))
        }),
      })

      if (response.ok) {
        await fetchData()
        setShowAddDialog(false)
        setEditingEntry(null)
        resetForm()
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حفظ القيد:', error)
      alert('حدث خطأ أثناء حفظ القيد')
    }
  }

  const resetForm = () => {
    setFormData({
      entry_date: new Date().toISOString().split('T')[0],
      description: '',
      currency_id: '1',
      cost_center_id: '',
      case_id: '',
      service_id: '',
      reference_number: ''
    })
    setEntryDetails([
      {
        line_number: 1,
        account_id: 0,
        debit_amount: 0,
        credit_amount: 0,
        currency_id: 1,
        exchange_rate: 1,
        description: ''
      },
      {
        line_number: 2,
        account_id: 0,
        debit_amount: 0,
        credit_amount: 0,
        currency_id: 1,
        exchange_rate: 1,
        description: ''
      }
    ])
  }

  const handleEdit = async (entry: JournalEntry) => {
    try {
      // جلب تفاصيل القيد
      const response = await fetch(`/api/accounting/journal-entries/${entry.id}?include_details=true`)
      if (response.ok) {
        const data = await response.json()
        const entryWithDetails = data.entry

        setEditingEntry(entryWithDetails)
        setFormData({
          entry_date: entryWithDetails.entry_date,
          description: entryWithDetails.description,
          currency_id: entryWithDetails.currency_id?.toString() || '1',
          cost_center_id: entryWithDetails.cost_center_id?.toString() || '',
          case_id: entryWithDetails.case_id?.toString() || '',
          case_number: entryWithDetails.case_number || '',
          reference_number: entryWithDetails.reference_number || ''
        })

        if (entryWithDetails.details && entryWithDetails.details.length > 0) {
          setEntryDetails(entryWithDetails.details.map((detail: any) => ({
            line_number: detail.line_number,
            account_id: detail.account_id,
            debit_amount: detail.debit_amount || 0,
            credit_amount: detail.credit_amount || 0,
            currency_id: detail.currency_id || 1,
            exchange_rate: detail.exchange_rate || 1,
            cost_center_id: detail.cost_center_id,
            payment_method_id: detail.payment_method_id,
            description: detail.description || '',
            reference_number: detail.reference_number || ''
          })))
        }

        setShowAddDialog(true)
      }
    } catch (error) {
      console.error('خطأ في جلب تفاصيل القيد:', error)
      alert('حدث خطأ أثناء جلب تفاصيل القيد')
    }
  }

  const handleView = async (entry: JournalEntry) => {
    try {
      // جلب تفاصيل القيد للعرض
      const response = await fetch(`/api/accounting/journal-entries/${entry.id}?include_details=true`)
      if (response.ok) {
        const data = await response.json()
        const entryWithDetails = data.entry

        // إنشاء نافذة جديدة لعرض القيد
        const printWindow = window.open('', '_blank', 'width=900,height=700')
        if (printWindow) {
          printWindow.document.write(`
            <html dir="rtl">
              <head>
                <title>قيد يومي رقم ${entry.entry_number}</title>
                <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { text-align: center; margin-bottom: 30px; }
                  .entry-info { border: 2px solid #333; padding: 20px; margin-bottom: 20px; }
                  .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                  .details-table th, .details-table td { border: 1px solid #333; padding: 8px; text-align: right; }
                  .details-table th { background-color: #f5f5f5; font-weight: bold; }
                  .row { display: flex; justify-content: space-between; margin: 10px 0; text-align: right; }
                  .label { font-weight: bold; text-align: right; }
                  .totals { margin-top: 20px; font-weight: bold; }
                </style>
              </head>
              <body>
                <div class="header">
                  <h1>قيد يومي</h1>
                  <h2>رقم: ${entry.entry_number}</h2>
                </div>
                <div class="entry-info">
                  <div class="row">
                    <span class="label">التاريخ:</span>
                    <span>${new Date(entry.entry_date).toLocaleDateString('en-GB')}</span>
                  </div>
                  <div class="row">
                    <span class="label">البيان:</span>
                    <span>${entry.description}</span>
                  </div>
                  <div class="row">
                    <span class="label">الحالة:</span>
                    <span>${entry.status === 'approved' ? 'معتمد' : entry.status === 'draft' ? 'مسودة' : 'ملغي'}</span>
                  </div>
                </div>
                <table class="details-table">
                  <thead>
                    <tr>
                      <th>رقم السطر</th>
                      <th>اسم الحساب</th>
                      <th>البيان</th>
                      <th>مدين</th>
                      <th>دائن</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${entryWithDetails.details?.map((detail: any) => `
                      <tr>
                        <td>${detail.line_number}</td>
                        <td>${detail.account_name || 'غير محدد'}</td>
                        <td>${detail.description || ''}</td>
                        <td>${detail.debit_amount ? detail.debit_amount.toLocaleString() : '-'}</td>
                        <td>${detail.credit_amount ? detail.credit_amount.toLocaleString() : '-'}</td>
                      </tr>
                    `).join('') || ''}
                  </tbody>
                </table>
                <div class="totals">
                  <div class="row">
                    <span>إجمالي المدين: ${entry.total_debit.toLocaleString()} ر.ي</span>
                    <span>إجمالي الدائن: ${entry.total_credit.toLocaleString()} ر.ي</span>
                  </div>
                  <div class="row">
                    <span>التوازن: ${entry.total_debit === entry.total_credit ? 'متوازن' : 'غير متوازن'}</span>
                  </div>
                </div>
              </body>
            </html>
          `)
          printWindow.document.close()
        }
      }
    } catch (error) {
      console.error('خطأ في عرض القيد:', error)
      alert('حدث خطأ أثناء عرض القيد')
    }
  }

  const handleDelete = async (entryId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا القيد؟ سيتم حذف جميع تفاصيل القيد أيضاً.')) {
      return
    }

    try {
      const response = await fetch(`/api/accounting/journal-entries/${entryId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchData()
        alert('تم حذف القيد بنجاح')
      } else {
        const errorData = await response.json()
        alert(`خطأ في حذف القيد: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حذف القيد:', error)
      alert('حدث خطأ أثناء حذف القيد')
    }
  }

  const handlePrint = async (entry: JournalEntry) => {
    try {
      // جلب تفاصيل القيد للطباعة
      const response = await fetch(`/api/accounting/journal-entries/${entry.id}?include_details=true`)
      if (response.ok) {
        const data = await response.json()
        const entryWithDetails = data.entry

        // إنشاء نافذة طباعة للقيد المحدد
        const printWindow = window.open('', '_blank', 'width=900,height=700')
        if (printWindow) {
          printWindow.document.write(`
            <html dir="rtl">
              <head>
                <title>طباعة قيد يومي رقم ${entry.entry_number}</title>
                <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { text-align: center; margin-bottom: 30px; }
                  .entry-info { border: 2px solid #333; padding: 20px; margin-bottom: 20px; }
                  .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                  .details-table th, .details-table td { border: 1px solid #333; padding: 10px; text-align: right; }
                  .details-table th { background-color: #f5f5f5; font-weight: bold; }
                  .row { display: flex; justify-content: space-between; margin: 15px 0; text-align: right; }
                  .label { font-weight: bold; width: 150px; text-align: right; }
                  .value { flex: 1; border-bottom: 1px dotted #333; padding-bottom: 5px; text-align: right; }
                  .totals { margin-top: 30px; font-weight: bold; border-top: 2px solid #333; padding-top: 15px; }
                  .signature { margin-top: 50px; display: flex; justify-content: space-between; }
                  .signature div { text-align: center; width: 200px; }
                  .signature-line { border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; }
                  @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                  }
                </style>
              </head>
              <body>
                <div class="header">
                  <h1>قيد يومي</h1>
                  <h2>رقم: ${entry.entry_number}</h2>
                </div>
                <div class="entry-info">
                  <div class="row">
                    <span class="label">التاريخ:</span>
                    <span class="value">${new Date(entry.entry_date).toLocaleDateString('en-GB')}</span>
                  </div>
                  <div class="row">
                    <span class="label">البيان:</span>
                    <span class="value">${entry.description}</span>
                  </div>
                  <div class="row">
                    <span class="label">الحالة:</span>
                    <span class="value">${entry.status === 'approved' ? 'معتمد' : entry.status === 'draft' ? 'مسودة' : 'ملغي'}</span>
                  </div>
                </div>
                <table class="details-table">
                  <thead>
                    <tr>
                      <th>رقم السطر</th>
                      <th>اسم الحساب</th>
                      <th>البيان</th>
                      <th>مدين</th>
                      <th>دائن</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${entryWithDetails.details?.map((detail: any) => `
                      <tr>
                        <td>${detail.line_number}</td>
                        <td>${detail.account_name || 'غير محدد'}</td>
                        <td>${detail.description || ''}</td>
                        <td>${detail.debit_amount ? detail.debit_amount.toLocaleString() + ' ر.ي' : '-'}</td>
                        <td>${detail.credit_amount ? detail.credit_amount.toLocaleString() + ' ر.ي' : '-'}</td>
                      </tr>
                    `).join('') || ''}
                  </tbody>
                </table>
                <div class="totals">
                  <div class="row">
                    <span>إجمالي المدين: ${entry.total_debit.toLocaleString()} ر.ي</span>
                    <span>إجمالي الدائن: ${entry.total_credit.toLocaleString()} ر.ي</span>
                  </div>
                  <div class="row">
                    <span>التوازن: ${entry.total_debit === entry.total_credit ? 'متوازن ✓' : 'غير متوازن ✗'}</span>
                  </div>
                </div>
                <div class="signature">
                  <div>
                    <div class="signature-line">المحاسب</div>
                  </div>
                  <div>
                    <div class="signature-line">المراجع</div>
                  </div>
                  <div>
                    <div class="signature-line">المدير المالي</div>
                  </div>
                </div>
                <script>
                  window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                      window.close();
                    }
                  }
                </script>
              </body>
            </html>
          `)
          printWindow.document.close()
        }
      }
    } catch (error) {
      console.error('خطأ في طباعة القيد:', error)
      alert('حدث خطأ أثناء طباعة القيد')
    }
  }

  const filteredEntries = entries.filter(entry =>
    entry.entry_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    entry.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'draft': { label: 'مسودة', variant: 'secondary' as const },
      'approved': { label: 'معتمد', variant: 'default' as const },
      'cancelled': { label: 'ملغي', variant: 'destructive' as const }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const { totalDebit, totalCredit } = calculateTotals()
  const balanced = isBalanced()

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <BookOpen className="h-8 w-8 text-purple-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">القيود اليومية</h1>
              <p className="text-gray-600">إدارة القيود المحاسبية اليومية</p>
            </div>
          </div>

          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 ml-2" />
            قيد يومي جديد
          </Button>
        </div>

        {/* جدول القيود */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle>القيود اليومية</CardTitle>
            <div className="relative w-80">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في القيود اليومية..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل القيود...</p>
              </div>
            ) : filteredEntries.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد قيود يومية</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-right p-3 font-semibold text-gray-700">رقم القيد</th>
                      <th className="text-right p-3 font-semibold text-gray-700">التاريخ</th>
                      <th className="text-right p-3 font-semibold text-gray-700">إجمالي المدين</th>
                      <th className="text-right p-3 font-semibold text-gray-700">إجمالي الدائن</th>
                      <th className="text-center p-3 font-semibold text-gray-700">التوازن</th>
                      <th className="text-right p-3 font-semibold text-gray-700">البيان</th>
                      <th className="text-center p-3 font-semibold text-gray-700">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEntries.map((entry, index) => (
                      <tr key={entry.id} className={`border-b hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                        <td className="p-3">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="font-mono text-blue-600 font-medium">
                              {entry.entry_number}
                            </span>
                            {getStatusBadge(entry.status)}
                          </div>
                        </td>
                        <td className="p-3 text-gray-600">
                          {new Date(entry.entry_date).toLocaleDateString('en-GB')}
                        </td>
                        <td className="p-3">
                          <span className="font-bold text-blue-600">
                            {entry.total_debit.toLocaleString()} ر.ي
                          </span>
                        </td>
                        <td className="p-3">
                          <span className="font-bold text-green-600">
                            {entry.total_credit.toLocaleString()} ر.ي
                          </span>
                        </td>
                        <td className="p-3 text-center">
                          <div className="flex items-center justify-center space-x-1 space-x-reverse">
                            <Calculator className="h-4 w-4 text-gray-400" />
                            <span className={`text-sm font-medium ${
                              entry.total_debit === entry.total_credit
                                ? 'text-green-600'
                                : 'text-red-600'
                            }`}>
                              {entry.total_debit === entry.total_credit ? 'متوازن' : 'غير متوازن'}
                            </span>
                          </div>
                        </td>
                        <td className="p-3 text-gray-600 max-w-xs truncate" title={entry.description}>
                          {entry.description}
                        </td>
                        <td className="p-3">
                          <div className="flex justify-center space-x-1 space-x-reverse">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleView(entry)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              title="مشاهدة"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(entry)}
                              className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                              title="تعديل"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(entry.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="حذف"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePrint(entry)}
                              className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                              title="طباعة"
                            >
                              <Printer className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل القيد */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
            <DialogHeader className="bg-gradient-to-r from-blue-50 to-blue-100 -m-6 mb-6 p-6 rounded-t-lg">
              <DialogTitle className="text-2xl font-bold text-blue-800 flex items-center">
                <BookOpen className="h-7 w-7 mr-3" />
                {editingEntry ? '✏️ تعديل القيد اليومي' : '📝 قيد يومي جديد'}
              </DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* معلومات القيد الأساسية */}
              <Card className="shadow-md">
                <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 border-b">
                  <CardTitle className="flex items-center text-gray-700">
                    📋 معلومات القيد الأساسية
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                    {/* التاريخ - ربع الحجم */}
                    <div className="md:col-span-1">
                      <Label htmlFor="entry_date" className="text-sm font-bold text-gray-700 mb-2 block">
                        📅 التاريخ *
                      </Label>
                      <Input
                        id="entry_date"
                        type="date"
                        value={formData.entry_date}
                        onChange={(e) => setFormData({...formData, entry_date: e.target.value})}
                        required
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-200"
                      />
                    </div>

                    {/* العملة - ربع الحجم */}
                    <div className="md:col-span-1">
                      <Label htmlFor="currency_id" className="text-sm font-bold text-gray-700 mb-2 block">
                        💰 العملة *
                      </Label>
                      <Select value={formData.currency_id} onValueChange={(value) => setFormData({...formData, currency_id: value})}>
                        <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-200">
                          <SelectValue placeholder="اختر العملة" />
                        </SelectTrigger>
                        <SelectContent>
                          {currencies.map((currency) => (
                            <SelectItem key={currency.id} value={currency.id.toString()}>
                              {currency.currency_name} ({currency.symbol})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* البيان - نصف الحجم */}
                    <div className="md:col-span-2">
                      <Label htmlFor="description" className="text-sm font-bold text-gray-700 mb-2 block">
                        📝 البيان *
                      </Label>
                      <Input
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        required
                        placeholder="وصف مختصر للقيد..."
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-200"
                      />
                    </div>

                    {/* المرجع - ربع الحجم */}
                    <div className="md:col-span-1">
                      <Label htmlFor="reference_number" className="text-sm font-bold text-gray-700 mb-2 block">
                        🔗 المرجع
                      </Label>
                      <Input
                        id="reference_number"
                        value={formData.reference_number}
                        onChange={(e) => setFormData({...formData, reference_number: e.target.value})}
                        placeholder="رقم المرجع..."
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-200"
                      />
                    </div>

                    {/* مساحة فارغة */}
                    <div className="md:col-span-1"></div>
                  </div>

                  {/* القوائم الثلاث */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    {/* القضية */}
                    <div>
                      <Label htmlFor="case_id" className="text-sm font-bold text-gray-700 mb-2 block">
                        ⚖️ القضية
                      </Label>
                      <Select value={formData.case_id} onValueChange={(value) => setFormData({...formData, case_id: value})}>
                        <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-200">
                          <SelectValue placeholder="اختر القضية..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">بدون قضية</SelectItem>
                          {cases.map((caseItem) => (
                            <SelectItem key={caseItem.id} value={caseItem.id.toString()}>
                              <div className="flex flex-col">
                                <span className="font-medium">{caseItem.case_number}</span>
                                <span className="text-sm text-gray-500">{caseItem.title}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* مركز التكلفة */}
                    <div>
                      <Label htmlFor="cost_center_id" className="text-sm font-bold text-gray-700 mb-2 block">
                        🏢 مركز التكلفة
                      </Label>
                      <Select value={formData.cost_center_id} onValueChange={(value) => setFormData({...formData, cost_center_id: value})}>
                        <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-200">
                          <SelectValue placeholder="اختر مركز التكلفة..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">بدون مركز تكلفة</SelectItem>
                          {costCenters.map((center) => (
                            <SelectItem key={center.id} value={center.id.toString()}>
                              <div className="flex flex-col">
                                <span className="font-medium">{center.center_code}</span>
                                <span className="text-sm text-gray-500">{center.center_name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* الخدمة */}
                    <div>
                      <Label htmlFor="service_id" className="text-sm font-bold text-gray-700 mb-2 block">
                        🔧 الخدمة
                      </Label>
                      <Select value={formData.service_id} onValueChange={(value) => setFormData({...formData, service_id: value})}>
                        <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-200">
                          <SelectValue placeholder="اختر الخدمة..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">بدون خدمة</SelectItem>
                          {services.map((service) => (
                            <SelectItem key={service.id} value={service.id.toString()}>
                              <div className="flex flex-col">
                                <span className="font-medium">{service.name}</span>
                                <span className="text-sm text-gray-500">{service.lineage_name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* تفاصيل القيد */}
              <Card className="shadow-md">
                <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 border-b">
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-green-700">
                      📊 تفاصيل القيد
                    </CardTitle>
                    <Button type="button" variant="outline" onClick={addDetailLine} className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100">
                      <Plus className="h-4 w-4 ml-2" />
                      ➕ إضافة سطر
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <div className="bg-gradient-to-r from-gray-100 to-gray-200 p-4 grid grid-cols-12 gap-3 text-sm font-bold text-gray-700 border-b">
                      <div className="col-span-1 text-center">#️⃣</div>
                      <div className="col-span-3">🏦 الحساب</div>
                      <div className="col-span-2 text-center">📈 مدين</div>
                      <div className="col-span-2 text-center">📉 دائن</div>
                      <div className="col-span-3">📝 الوصف</div>
                      <div className="col-span-1 text-center">⚙️</div>
                    </div>

                    {entryDetails.map((detail, index) => (
                      <div key={index} className={`p-4 grid grid-cols-12 gap-3 border-b hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <div className="col-span-1 flex items-center justify-center">
                          <span className="bg-blue-100 text-blue-800 font-bold px-2 py-1 rounded-full text-sm">
                            {detail.line_number}
                          </span>
                        </div>

                        <div className="col-span-3">
                          <Select
                            value={detail.account_id.toString()}
                            onValueChange={(value) => updateDetailLine(index, 'account_id', parseInt(value))}
                          >
                            <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-200">
                              <SelectValue placeholder="اختر الحساب..." />
                            </SelectTrigger>
                            <SelectContent>
                              {accounts.map((account) => (
                                <SelectItem key={account.id} value={account.is_linked_record ? `${account.original_table}_${account.external_id}` : account.id.toString()}>
                                  <div className="flex items-center justify-between w-full">
                                    <span className="font-medium">{account.account_code} - {account.account_name}</span>
                                    <div className="flex space-x-1 space-x-reverse">
                                      {account.linked_table && (
                                        <Badge variant="outline" className="text-xs">
                                          {account.linked_table === 'clients' ? 'تحكم عملاء' : 'تحكم موظفين'}
                                        </Badge>
                                      )}
                                      {account.is_linked_record && (
                                        <Badge variant="secondary" className="text-xs">
                                          {account.original_table === 'clients' ? 'عميل' : 'موظف'}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="col-span-2">
                          <Input
                            type="number"
                            step="0.01"
                            value={detail.debit_amount || ''}
                            onChange={(e) => updateDetailLine(index, 'debit_amount', parseFloat(e.target.value) || 0)}
                            placeholder="0.00"
                            className="text-center font-bold text-blue-600 border-gray-300 focus:border-blue-500 focus:ring-blue-200"
                          />
                        </div>

                        <div className="col-span-2">
                          <Input
                            type="number"
                            step="0.01"
                            value={detail.credit_amount || ''}
                            onChange={(e) => updateDetailLine(index, 'credit_amount', parseFloat(e.target.value) || 0)}
                            placeholder="0.00"
                            className="text-center font-bold text-green-600 border-gray-300 focus:border-green-500 focus:ring-green-200"
                          />
                        </div>

                        <div className="col-span-3">
                          <Input
                            value={detail.description}
                            onChange={(e) => updateDetailLine(index, 'description', e.target.value)}
                            placeholder="وصف السطر..."
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-200"
                        />
                      </div>

                        <div className="col-span-1 flex items-center justify-center">
                          {entryDetails.length > 2 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDetailLine(index)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50 p-2"
                              title="حذف السطر"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}

                    {/* إجماليات القيد */}
                    <div className="bg-gradient-to-r from-blue-50 to-green-50 p-4 grid grid-cols-12 gap-3 border-t-2 border-gray-300">
                      <div className="col-span-4 font-bold text-gray-700 flex items-center">
                        🧮 الإجماليات:
                      </div>
                      <div className="col-span-2 text-center">
                        <div className="bg-blue-100 text-blue-800 font-bold px-3 py-2 rounded-lg">
                          {totalDebit.toLocaleString()} ر.ي
                        </div>
                      </div>
                      <div className="col-span-2 text-center">
                        <div className="bg-green-100 text-green-800 font-bold px-3 py-2 rounded-lg">
                          {totalCredit.toLocaleString()} ر.ي
                        </div>
                      </div>
                      <div className="col-span-3 flex items-center justify-center">
                        {balanced ? (
                          <Badge className="bg-green-500 text-white px-4 py-2 text-sm font-bold">
                            ✅ متوازن
                          </Badge>
                        ) : (
                          <Badge variant="destructive" className="flex items-center px-4 py-2 text-sm font-bold">
                            <AlertCircle className="h-4 w-4 ml-1" />
                            ⚠️ غير متوازن
                          </Badge>
                        )}
                      </div>
                      <div className="col-span-1"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* أزرار الحفظ */}
              <Card className="shadow-md">
                <CardContent className="p-6">
                  <div className="flex justify-end space-x-4 space-x-reverse">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowAddDialog(false)}
                      className="bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100 font-medium px-6 py-3"
                    >
                      <X className="h-5 w-5 mr-2" />
                      ❌ إلغاء
                    </Button>
                    <Button
                      type="submit"
                      disabled={!balanced}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <Save className="h-5 w-5 mr-2" />
                      {editingEntry ? '✏️ تحديث القيد' : '💾 حفظ القيد'}
                    </Button>
                  </div>

                  {!balanced && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center text-red-700">
                        <AlertCircle className="h-5 w-5 mr-2" />
                        <span className="font-medium">⚠️ يجب أن يكون مجموع المدين مساوياً لمجموع الدائن لحفظ القيد</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
