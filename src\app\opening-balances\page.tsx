'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Calculator,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  DollarSign,
  BookOpen
} from 'lucide-react'

interface OpeningBalance {
  id: number
  account_code: string
  account_name: string
  account_type: string
  debit_amount: number
  credit_amount: number
  balance_type: string
  description: string
  fiscal_year: string
  is_active: boolean
  created_date: string
}

export default function OpeningBalancesPage() {
  const [balances, setBalances] = useState<OpeningBalance[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingBalance, setEditingBalance] = useState<OpeningBalance | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    account_code: '',
    account_name: '',
    account_type: '',
    debit_amount: '',
    credit_amount: '',
    balance_type: 'debit',
    description: '',
    fiscal_year: '',
    is_active: true
  })

  const fetchBalances = async () => {
    setIsLoading(true)
    setDbError(null)
    
    try {
      // محاكاة بيانات الأرصدة الافتتاحية
      const sampleData: OpeningBalance[] = [
        {
          id: 1,
          account_code: '1001001',
          account_name: 'النقدية في الصندوق',
          account_type: 'أصول',
          debit_amount: 50000,
          credit_amount: 0,
          balance_type: 'debit',
          description: 'الرصيد الافتتاحي للنقدية في الصندوق',
          fiscal_year: '2024',
          is_active: true,
          created_date: '2024-01-01'
        },
        {
          id: 2,
          account_code: '1001002',
          account_name: 'البنك - الحساب الجاري',
          account_type: 'أصول',
          debit_amount: 150000,
          credit_amount: 0,
          balance_type: 'debit',
          description: 'الرصيد الافتتاحي للحساب الجاري',
          fiscal_year: '2024',
          is_active: true,
          created_date: '2024-01-01'
        },
        {
          id: 3,
          account_code: '1101001',
          account_name: 'العملاء',
          account_type: 'أصول',
          debit_amount: 75000,
          credit_amount: 0,
          balance_type: 'debit',
          description: 'أرصدة العملاء المدينة',
          fiscal_year: '2024',
          is_active: true,
          created_date: '2024-01-01'
        },
        {
          id: 4,
          account_code: '2001001',
          account_name: 'الموردون',
          account_type: 'خصوم',
          debit_amount: 0,
          credit_amount: 45000,
          balance_type: 'credit',
          description: 'أرصدة الموردين الدائنة',
          fiscal_year: '2024',
          is_active: true,
          created_date: '2024-01-01'
        },
        {
          id: 5,
          account_code: '3001',
          account_name: 'رأس المال',
          account_type: 'حقوق ملكية',
          debit_amount: 0,
          credit_amount: 200000,
          balance_type: 'credit',
          description: 'رأس المال المدفوع',
          fiscal_year: '2024',
          is_active: true,
          created_date: '2024-01-01'
        },
        {
          id: 6,
          account_code: '5001',
          account_name: 'مصروفات الرواتب',
          account_type: 'مصروفات',
          debit_amount: 25000,
          credit_amount: 0,
          balance_type: 'debit',
          description: 'مصروفات رواتب مستحقة',
          fiscal_year: '2024',
          is_active: true,
          created_date: '2024-01-01'
        }
      ]
      
      setBalances(sampleData)
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setBalances([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchBalances()
  }, [])

  const filteredBalances = balances.filter(balance => {
    const matchesSearch = (balance.account_code || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (balance.account_name || '').toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = typeFilter === 'all' || balance.account_type === typeFilter
    
    return matchesSearch && matchesType
  })

  const handleDelete = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا الرصيد الافتتاحي؟')) {
      setBalances(balances.filter(balance => balance.id !== id))
      alert('تم حذف الرصيد الافتتاحي بنجاح')
    }
  }

  const handleEdit = (balance: OpeningBalance) => {
    setEditingBalance(balance)
    setFormData({
      account_code: balance.account_code,
      account_name: balance.account_name,
      account_type: balance.account_type,
      debit_amount: balance.debit_amount.toString(),
      credit_amount: balance.credit_amount.toString(),
      balance_type: balance.balance_type,
      description: balance.description,
      fiscal_year: balance.fiscal_year,
      is_active: balance.is_active
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleView = (balance: OpeningBalance) => {
    setEditingBalance(balance)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingBalance(null)
    setFormData({
      account_code: '',
      account_name: '',
      account_type: '',
      debit_amount: '',
      credit_amount: '',
      balance_type: 'debit',
      description: '',
      fiscal_year: new Date().getFullYear().toString(),
      is_active: true
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (modalType === 'add') {
      const newBalance: OpeningBalance = {
        id: Date.now(),
        ...formData,
        debit_amount: Number(formData.debit_amount),
        credit_amount: Number(formData.credit_amount),
        created_date: new Date().toISOString().split('T')[0]
      }
      setBalances([...balances, newBalance])
      alert('تم إضافة الرصيد الافتتاحي بنجاح')
    } else if (modalType === 'edit' && editingBalance) {
      setBalances(balances.map(balance => 
        balance.id === editingBalance.id 
          ? { 
              ...balance, 
              ...formData, 
              debit_amount: Number(formData.debit_amount),
              credit_amount: Number(formData.credit_amount)
            }
          : balance
      ))
      alert('تم تحديث الرصيد الافتتاحي بنجاح')
    }

    setIsModalOpen(false)
    setEditingBalance(null)
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'أصول': return 'bg-blue-100 text-blue-800'
      case 'خصوم': return 'bg-red-100 text-red-800'
      case 'حقوق ملكية': return 'bg-green-100 text-green-800'
      case 'إيرادات': return 'bg-purple-100 text-purple-800'
      case 'مصروفات': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getBalanceTypeColor = (type: string) => {
    return type === 'debit' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
  }

  const getBalanceTypeText = (type: string) => {
    return type === 'debit' ? 'مدين' : 'دائن'
  }

  const accountTypes = ['أصول', 'خصوم', 'حقوق ملكية', 'إيرادات', 'مصروفات']

  const stats = {
    total: balances.length,
    totalDebits: balances.reduce((sum, b) => sum + (b.debit_amount || 0), 0),
    totalCredits: balances.reduce((sum, b) => sum + (b.credit_amount || 0), 0),
    active: balances.filter(b => b.is_active).length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Calculator className="h-8 w-8 mr-3 text-blue-600" />
              الأرصدة الافتتاحية
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة الأرصدة الافتتاحية للحسابات</p>
          </div>
          
          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة رصيد افتتاحي جديد
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Calculator className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي الحسابات</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-bold">+</span>
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي المدين</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalDebits.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-bold">-</span>
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي الدائن</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalCredits.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BookOpen className="h-8 w-8 text-green-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">الحسابات النشطة</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.active}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الحسابات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <div>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="all">جميع الأنواع</option>
                  {accountTypes.map((type) => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              قائمة الأرصدة الافتتاحية ({filteredBalances.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!dbError && !isLoading && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">رمز الحساب</th>
                      <th className="text-right p-3 font-semibold">اسم الحساب</th>
                      <th className="text-center p-3 font-semibold">النوع</th>
                      <th className="text-center p-3 font-semibold">المدين</th>
                      <th className="text-center p-3 font-semibold">الدائن</th>
                      <th className="text-center p-3 font-semibold">نوع الرصيد</th>
                      <th className="text-right p-3 font-semibold">السنة المالية</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredBalances.map((balance) => (
                      <tr key={balance.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{balance.account_code}</td>
                        <td className="p-3">{balance.account_name}</td>
                        <td className="text-center p-3">
                          <Badge className={getAccountTypeColor(balance.account_type)}>
                            {balance.account_type}
                          </Badge>
                        </td>
                        <td className="text-center p-3">
                          {balance.debit_amount > 0 && (
                            <div className="flex items-center justify-center">
                              <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                              {balance.debit_amount.toLocaleString()}
                            </div>
                          )}
                        </td>
                        <td className="text-center p-3">
                          {balance.credit_amount > 0 && (
                            <div className="flex items-center justify-center">
                              <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                              {balance.credit_amount.toLocaleString()}
                            </div>
                          )}
                        </td>
                        <td className="text-center p-3">
                          <Badge className={getBalanceTypeColor(balance.balance_type)}>
                            {getBalanceTypeText(balance.balance_type)}
                          </Badge>
                        </td>
                        <td className="p-3">{balance.fiscal_year}</td>
                        <td className="text-center p-3">
                          <Badge className={balance.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {balance.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                        </td>
                        <td className="text-center p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline" onClick={() => handleView(balance)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(balance)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(balance.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {!dbError && !isLoading && balances.length === 0 && (
              <div className="text-center py-8">
                <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أرصدة افتتاحية</h3>
                <p className="text-gray-600">لم يتم العثور على أي أرصدة افتتاحية في النظام</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {modalType === 'add' && 'إضافة رصيد افتتاحي جديد'}
                  {modalType === 'edit' && 'تعديل الرصيد الافتتاحي'}
                  {modalType === 'view' && 'عرض الرصيد الافتتاحي'}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {modalType === 'view' && editingBalance ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>رمز الحساب</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingBalance.account_code}</p>
                    </div>
                    <div>
                      <Label>اسم الحساب</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingBalance.account_name}</p>
                    </div>
                    <div>
                      <Label>نوع الحساب</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingBalance.account_type}</p>
                    </div>
                    <div>
                      <Label>السنة المالية</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingBalance.fiscal_year}</p>
                    </div>
                    <div>
                      <Label>المدين</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingBalance.debit_amount.toLocaleString()}</p>
                    </div>
                    <div>
                      <Label>الدائن</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingBalance.credit_amount.toLocaleString()}</p>
                    </div>
                  </div>
                  <div>
                    <Label>الوصف</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingBalance.description}</p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="account_code">رمز الحساب *</Label>
                      <Input
                        id="account_code"
                        value={formData.account_code}
                        onChange={(e) => setFormData({...formData, account_code: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="account_name">اسم الحساب *</Label>
                      <Input
                        id="account_name"
                        value={formData.account_name}
                        onChange={(e) => setFormData({...formData, account_name: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="account_type">نوع الحساب *</Label>
                      <select
                        id="account_type"
                        value={formData.account_type}
                        onChange={(e) => setFormData({...formData, account_type: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        required
                      >
                        <option value="">اختر نوع الحساب</option>
                        {accountTypes.map((type) => (
                          <option key={type} value={type}>{type}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="fiscal_year">السنة المالية *</Label>
                      <Input
                        id="fiscal_year"
                        value={formData.fiscal_year}
                        onChange={(e) => setFormData({...formData, fiscal_year: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="debit_amount">المبلغ المدين</Label>
                      <Input
                        id="debit_amount"
                        type="number"
                        step="0.01"
                        value={formData.debit_amount}
                        onChange={(e) => setFormData({...formData, debit_amount: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="credit_amount">المبلغ الدائن</Label>
                      <Input
                        id="credit_amount"
                        type="number"
                        step="0.01"
                        value={formData.credit_amount}
                        onChange={(e) => setFormData({...formData, credit_amount: e.target.value})}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="description">الوصف</Label>
                    <Input
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="balance_type">نوع الرصيد</Label>
                      <select
                        id="balance_type"
                        value={formData.balance_type}
                        onChange={(e) => setFormData({...formData, balance_type: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      >
                        <option value="debit">مدين</option>
                        <option value="credit">دائن</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="is_active">الحالة</Label>
                      <select
                        id="is_active"
                        value={formData.is_active.toString()}
                        onChange={(e) => setFormData({...formData, is_active: e.target.value === 'true'})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      >
                        <option value="true">نشط</option>
                        <option value="false">غير نشط</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button type="submit" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)} className="flex-1">
                      إلغاء
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
