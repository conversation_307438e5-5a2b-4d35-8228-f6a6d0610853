'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Percent,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Save,
  X,
  Calculator,
  TrendingUp
} from 'lucide-react'

interface Lineage {
  id: number
  name: string
  admin_percentage: number
  created_date?: string
}

interface Service {
  id: number
  name: string
  description: string
  category: string
}

interface ServicePercentage {
  service_id: number
  service_name: string
  percentage: number
}

export default function PercentagesPage() {
  const [lineages, setLineages] = useState<Lineage[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingLineage, setEditingLineage] = useState<Lineage | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    admin_percentage: 0
  })
  const [percentageError, setPercentageError] = useState<string | null>(null)

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/services')
      const result = await response.json()

      if (result.success) {
        // فلترة الخدمات الأساسية فقط
        const basicServices = result.data.filter((service: any) =>
          ['اعداد', 'جلسة', 'متابعة', 'اشراف', 'مصروفات قضائية'].includes(service.name)
        )
        setServices(basicServices)
      }
    } catch (error) {
      console.error('Error fetching services:', error)
    }
  }

  const fetchLineages = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/percentages')
      const result = await response.json()

      if (result.success) {
        setLineages(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات النسب المالية')
        setLineages([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setLineages([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchLineages()
    fetchServices()
  }, [])

  const filteredLineages = lineages.filter(lineage =>
    lineage.name?.toLowerCase().includes(searchTerm.toLowerCase()) || false
  )

  const validateForm = () => {
    if (!formData.name.trim()) {
      setPercentageError('اسم النسبة مطلوب')
      return false
    }

    if (formData.admin_percentage < 0 || formData.admin_percentage > 100) {
      setPercentageError('نسبة الإدارة يجب أن تكون بين 0% و 100%')
      return false
    }

    setPercentageError(null)
    return true
  }

  const getPercentageColor = (total: number) => {
    if (total === 100) return 'text-green-600'
    if (total > 100) return 'text-red-600'
    return 'text-yellow-600'
  }

  const handleAddNew = () => {
    setEditingLineage(null)
    setFormData({
      name: '',
      admin_percentage: 0
    })
    setPercentageError(null)
    setIsModalOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // التحقق من صحة البيانات
    if (!validateForm()) {
      return
    }

    try {
      if (editingLineage) {
        // تحديث نسبة موجودة
        const response = await fetch('/api/percentages', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, id: editingLineage.id })
        })

        const result = await response.json()

        if (result.success) {
          setLineages(lineages.map(lineage =>
            lineage.id === editingLineage.id
              ? { ...lineage, ...formData }
              : lineage
          ))
          alert('تم تحديث النسبة المالية بنجاح')
        } else {
          alert(result.error || 'فشل في تحديث النسبة المالية')
          return
        }
      } else {
        // إضافة نسبة جديدة
        const response = await fetch('/api/percentages', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          const newLineage: Lineage = {
            id: Date.now(),
            name: formData.name,
            admin_percentage: formData.admin_percentage,
            created_date: new Date().toISOString().split('T')[0]
          }
          setLineages([...lineages, newLineage])
          alert('تم إضافة النسبة المالية بنجاح')
        } else {
          alert(result.error || 'فشل في إضافة النسبة المالية')
          return
        }
      }

      setIsModalOpen(false)
      setEditingLineage(null)
      setFormData({
        name: '',
        admin_percentage: 0
      })
      setPercentageError(null)
    } catch (error) {
      console.error('Error saving lineage:', error)
      alert('حدث خطأ أثناء الحفظ')
    }
  }

  const handleEdit = (lineage: Lineage) => {
    setEditingLineage(lineage)
    setFormData({
      name: lineage.name || '',
      admin_percentage: lineage.admin_percentage || 0
    })
    setPercentageError(null)
    setIsModalOpen(true)
  }

  const handleDelete = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه النسبة؟')) {
      setLineages(lineages.filter(lineage => lineage.id !== id))
    }
  }

  const currentTotal = formData.management_share + formData.court_share + formData.commission_share + formData.other_share

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والإحصائيات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Percent className="h-8 w-8 mr-3 text-blue-600" />
              النسب المالية
            </h1>
            <p className="text-gray-600 mt-1">إدارة النسب المالية لمجموعات القضايا</p>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{lineages.length}</div>
              <div className="text-sm text-gray-600">إجمالي المجموعات</div>
            </div>
            <Button
              onClick={handleAddNew}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              إضافة نسبة جديدة
            </Button>
          </div>
        </div>

        {/* شريط البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في أسماء المجموعات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* جدول النسب */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              قائمة النسب المالية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-4 font-semibold text-lg">اسم النسبة</th>
                    <th className="text-center p-4 font-semibold text-lg">نسبة الإدارة (%)</th>
                    <th className="text-center p-4 font-semibold text-lg">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredLineages.map((lineage) => (
                    <tr key={lineage.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                          <span className="font-medium text-lg">{lineage.name || 'غير محدد'}</span>
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <Badge className="bg-purple-100 text-purple-800 text-lg px-4 py-2">
                          {lineage.admin_percentage || 0}%
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex justify-center space-x-3 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(lineage)}
                            className="text-blue-600 hover:text-blue-700 px-4 py-2"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            تعديل
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(lineage.id)}
                            className="text-red-600 hover:text-red-700 px-4 py-2"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            حذف
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* نافذة الإضافة/التعديل */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {editingLineage ? 'تعديل النسبة' : 'إضافة نسبة جديدة'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">اسم النسبة</label>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    placeholder="أدخل اسم النسبة (مثل: نسب القضايا المدنية)"
                    required
                    className="text-lg"
                  />
                </div>

                <div className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
                  <label className="block text-lg font-medium mb-3 text-purple-800">نسبة الإدارة (%)</label>
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      value={formData.admin_percentage}
                      onChange={(e) => setFormData({...formData, admin_percentage: Number(e.target.value)})}
                      className="w-40 text-center text-lg font-bold"
                      placeholder="0"
                      required
                    />
                    <span className="text-purple-600 font-bold text-lg">%</span>
                  </div>
                  <p className="text-sm text-purple-600 mt-2">
                    💡 نسبة الإدارة التي سيتم خصمها من مبلغ القضية قبل توزيع باقي المبلغ
                  </p>
                </div>

                {percentageError && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 text-sm">{percentageError}</p>
                  </div>
                )}

                <div className="flex space-x-3 space-x-reverse">
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={getTotalPercentage() !== 100}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {editingLineage ? 'تحديث' : 'حفظ'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                    className="flex-1"
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
