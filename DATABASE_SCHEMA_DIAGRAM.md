# 📊 مخطط هيكل قاعدة البيانات - النظام القانوني

## 🏗️ نظرة عامة على النظام
النظام القانوني يتكون من **25+ جدول** مترابط يغطي جميع جوانب إدارة المكتب القانوني من العملاء والقضايا إلى المحاسبة والتواصل.

---

## 📋 الجداول الأساسية (Core Tables)

### 1. **clients** (العملاء/الموكلين)
```sql
CREATE TABLE clients (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  email VARCHAR(255),
  address TEXT,
  id_number VARCHAR(20) UNIQUE,
  client_type VARCHAR(100),
  username VARCHAR(100),
  password_hash VARCHAR(255),
  status VARCHAR(20) DEFAULT 'active',
  client_number VARCHAR(50),
  account_id INTEGER REFERENCES account_linking_settings(id),
  is_active BOOLEAN DEFAULT true,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: إدارة بيانات الموكلين والعملاء
**العلاقات**:
- ← issues (One-to-Many)
- ← conversations (One-to-Many)
- → account_linking_settings (Many-to-One)

### 2. **employees** (الموظفين)
```sql
CREATE TABLE employees (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  position VARCHAR(255),
  department_id INTEGER REFERENCES courts(id),
  branch_id INTEGER REFERENCES branches(id),
  governorate_id INTEGER REFERENCES governorates(id),
  phone VARCHAR(20),
  email VARCHAR(255),
  salary DECIMAL(10,2),
  hire_date DATE,
  employee_number VARCHAR(50),
  account_id INTEGER REFERENCES account_linking_settings(id),
  status VARCHAR(20) DEFAULT 'active',
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: إدارة بيانات الموظفين والمحامين
**العلاقات**:
- → branches (Many-to-One)
- → governorates (Many-to-One)
- → courts (Many-to-One) [كقسم]
- → account_linking_settings (Many-to-One)
- ← users (One-to-One)

### 3. **issues** (القضايا)
```sql
CREATE TABLE issues (
  id SERIAL PRIMARY KEY,
  case_number VARCHAR(50) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  client_id INTEGER REFERENCES clients(id),
  client_name VARCHAR(255),
  court_name VARCHAR(255),
  issue_type_id INTEGER REFERENCES issue_types(id),
  status VARCHAR(50) DEFAULT 'pending',
  amount DECIMAL(12,2),
  next_hearing DATE,
  notes TEXT,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: إدارة القضايا والدعاوى القانونية
**العلاقات**:
- → clients (Many-to-One)
- → issue_types (Many-to-One)
- ← follows (One-to-Many)
- ← movements (One-to-Many)

---

## 🏢 الجداول التنظيمية (Organizational Tables)

### 4. **governorates** (المحافظات)
```sql
CREATE TABLE governorates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  code VARCHAR(10),
  is_active BOOLEAN DEFAULT true,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تصنيف المناطق الجغرافية
**العلاقات**: ← branches, employees (One-to-Many)

### 5. **branches** (الفروع)
```sql
CREATE TABLE branches (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  governorate_id INTEGER REFERENCES governorates(id),
  address TEXT,
  phone VARCHAR(50),
  manager_name VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: إدارة فروع المكتب القانوني
**العلاقات**:
- → governorates (Many-to-One)
- ← employees (One-to-Many)

### 6. **courts** (المحاكم)
```sql
CREATE TABLE courts (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(100),
  governorate_id INTEGER,
  address TEXT,
  phone VARCHAR(50),
  employee_id INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: إدارة بيانات المحاكم
**العلاقات**: ← employees [كأقسام] (One-to-Many)

### 7. **issue_types** (أنواع القضايا)
```sql
CREATE TABLE issue_types (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(50),
  cases_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تصنيف أنواع القضايا
**العلاقات**: ← issues (One-to-Many)

---

## 💰 النظام المحاسبي (Accounting System)

### 8. **chart_of_accounts** (دليل الحسابات)
```sql
CREATE TABLE chart_of_accounts (
  id SERIAL PRIMARY KEY,
  account_code VARCHAR(20) UNIQUE NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  account_type VARCHAR(50) NOT NULL,
  parent_id INTEGER REFERENCES chart_of_accounts(id),
  account_level INTEGER DEFAULT 1,
  is_main_account BOOLEAN DEFAULT FALSE,
  linked_table VARCHAR(100),
  auto_create_sub_accounts BOOLEAN DEFAULT FALSE,
  account_nature VARCHAR(20) DEFAULT 'مدين',
  opening_balance DECIMAL(15,2) DEFAULT 0,
  current_balance DECIMAL(15,2) DEFAULT 0,
  allow_posting BOOLEAN DEFAULT TRUE,
  created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```
**الغرض**: دليل الحسابات المحاسبي الرئيسي
**العلاقات**:
- → chart_of_accounts [self-reference] (Many-to-One)
- ← account_sub_links (One-to-Many)
- ← account_linking_settings (One-to-Many)

### 9. **account_linking_settings** (إعدادات ربط الحسابات)
```sql
CREATE TABLE account_linking_settings (
  id SERIAL PRIMARY KEY,
  table_name VARCHAR(100) UNIQUE NOT NULL,
  table_display_name VARCHAR(255) NOT NULL,
  table_description TEXT,
  is_enabled BOOLEAN DEFAULT TRUE,
  auto_create_on_insert BOOLEAN DEFAULT TRUE,
  account_prefix VARCHAR(10),
  name_field VARCHAR(100) DEFAULT 'name',
  default_main_account_id INTEGER REFERENCES chart_of_accounts(id),
  created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```
**الغرض**: إعدادات ربط الجداول بالحسابات المحاسبية
**العلاقات**:
- → chart_of_accounts (Many-to-One)
- ← clients (One-to-Many)
- ← employees (One-to-Many)

### 10. **account_sub_links** (روابط الحسابات الفرعية)
```sql
CREATE TABLE account_sub_links (
  id SERIAL PRIMARY KEY,
  main_account_id INTEGER REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
  linked_table VARCHAR(100) NOT NULL,
  linked_record_id INTEGER NOT NULL,
  sub_account_code VARCHAR(50) UNIQUE NOT NULL,
  sub_account_name VARCHAR(255) NOT NULL,
  opening_balance DECIMAL(15,2) DEFAULT 0,
  current_balance DECIMAL(15,2) DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(main_account_id, linked_table, linked_record_id)
)
```
**الغرض**: ربط السجلات بحسابات فرعية محددة
**العلاقات**: → chart_of_accounts (Many-to-One)

### 11. **movements** (الحركات المالية)
```sql
CREATE TABLE movements (
  id SERIAL PRIMARY KEY,
  case_id INTEGER REFERENCES issues(id),
  case_number VARCHAR(50),
  movement_type VARCHAR(20) NOT NULL, -- 'income' or 'expense'
  category VARCHAR(255),
  amount DECIMAL(12,2) NOT NULL,
  description TEXT,
  date DATE DEFAULT CURRENT_DATE,
  reference_number VARCHAR(100),
  status VARCHAR(50) DEFAULT 'pending',
  created_by VARCHAR(255),
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تسجيل الحركات المالية للقضايا
**العلاقات**: → issues (Many-to-One)

### 12. **journal_entries** (القيود اليومية)
```sql
CREATE TABLE journal_entries (
  id SERIAL PRIMARY KEY,
  entry_number VARCHAR(50) UNIQUE NOT NULL,
  description TEXT NOT NULL,
  date DATE DEFAULT CURRENT_DATE,
  total_debit DECIMAL(12,2) DEFAULT 0.00,
  total_credit DECIMAL(12,2) DEFAULT 0.00,
  status VARCHAR(50) DEFAULT 'pending',
  created_by VARCHAR(255),
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تسجيل القيود المحاسبية اليومية

---

## 📊 النظام المالي المتقدم (Advanced Financial System)

### 13. **lineages** (النسب المالية)
```sql
CREATE TABLE lineages (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  admin_percentage DECIMAL(5,2) DEFAULT 0,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تحديد نسب الإدارة لأنواع القضايا المختلفة
**العلاقات**: ← case_distribution (One-to-Many)

### 14. **services** (الخدمات)
```sql
CREATE TABLE services (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  base_price DECIMAL(15,2) DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تعريف الخدمات القانونية المقدمة
**العلاقات**: ← service_distributions (One-to-Many)

### 15. **case_distribution** (توزيع القضايا)
```sql
CREATE TABLE case_distribution (
  id SERIAL PRIMARY KEY,
  issue_id INTEGER,
  lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
  admin_amount DECIMAL(15,2) DEFAULT 0,
  remaining_amount DECIMAL(15,2) DEFAULT 0,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: توزيع الأموال على القضايا حسب النسب
**العلاقات**:
- → lineages (Many-to-One)
- ← service_distributions (One-to-Many)

### 16. **service_distributions** (توزيع الخدمات)
```sql
CREATE TABLE service_distributions (
  id SERIAL PRIMARY KEY,
  case_distribution_id INTEGER REFERENCES case_distribution(id) ON DELETE CASCADE,
  service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
  percentage DECIMAL(5,2) DEFAULT 0,
  amount DECIMAL(15,2) DEFAULT 0,
  lawyer_id INTEGER,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: توزيع الخدمات والأتعاب على المحامين
**العلاقات**:
- → case_distribution (Many-to-One)
- → services (Many-to-One)

---

## 💬 نظام التواصل (Communication System)

### 17. **conversations** (المحادثات)
```sql
CREATE TABLE conversations (
  id SERIAL PRIMARY KEY,
  client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255),
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_message_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```
**الغرض**: إدارة المحادثات بين العملاء والمحامين
**العلاقات**:
- → clients (Many-to-One)
- → users (Many-to-One)
- ← messages (One-to-Many)

### 18. **messages** (الرسائل)
```sql
CREATE TABLE messages (
  id SERIAL PRIMARY KEY,
  conversation_id INTEGER REFERENCES conversations(id) ON DELETE CASCADE,
  sender_type VARCHAR(10) NOT NULL CHECK (sender_type IN ('user', 'client')),
  sender_id INTEGER NOT NULL,
  message_text TEXT,
  message_type VARCHAR(20) DEFAULT 'text',
  file_url VARCHAR(500),
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```
**الغرض**: تخزين الرسائل المتبادلة
**العلاقات**: → conversations (Many-to-One)

### 19. **notifications** (الإشعارات)
```sql
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  recipient_type VARCHAR(10) NOT NULL CHECK (recipient_type IN ('user', 'client')),
  recipient_id INTEGER NOT NULL,
  sender_type VARCHAR(10) NOT NULL,
  sender_id INTEGER NOT NULL,
  notification_type VARCHAR(20) DEFAULT 'message',
  title VARCHAR(255),
  content TEXT,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```
**الغرض**: إدارة الإشعارات للمستخدمين

---

## 👥 إدارة المستخدمين (User Management)

### 20. **users** (المستخدمين)
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE,
  password_hash VARCHAR(255),
  role VARCHAR(50) DEFAULT 'user',
  employee_id INTEGER REFERENCES employees(id),
  status VARCHAR(20) DEFAULT 'active',
  last_login TIMESTAMP,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: إدارة حسابات المستخدمين والصلاحيات
**العلاقات**:
- → employees (One-to-One)
- ← conversations (One-to-Many)

---

## 📈 جداول المتابعة والتقارير (Tracking & Reports)

### 21. **follows** (المتابعات)
```sql
CREATE TABLE follows (
  id SERIAL PRIMARY KEY,
  case_id INTEGER REFERENCES issues(id),
  case_number VARCHAR(50),
  follow_type VARCHAR(50),
  description TEXT,
  due_date DATE,
  status VARCHAR(50) DEFAULT 'pending',
  priority VARCHAR(20) DEFAULT 'medium',
  created_by VARCHAR(255),
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: متابعة المهام والمواعيد المتعلقة بالقضايا
**العلاقات**: → issues (Many-to-One)

### 22. **opening_balances** (الأرصدة الافتتاحية)
```sql
CREATE TABLE opening_balances (
  id SERIAL PRIMARY KEY,
  account_name VARCHAR(255) NOT NULL,
  account_code VARCHAR(50) NOT NULL,
  debit_amount DECIMAL(15,2) DEFAULT 0,
  credit_amount DECIMAL(15,2) DEFAULT 0,
  balance_type VARCHAR(20) NOT NULL,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تسجيل الأرصدة الافتتاحية للحسابات

### 23. **cost_centers** (مراكز التكلفة)
```sql
CREATE TABLE cost_centers (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: تصنيف التكاليف حسب المراكز

### 24. **companies** (الشركات)
```sql
CREATE TABLE companies (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(255),
  tax_number VARCHAR(50),
  commercial_register VARCHAR(50),
  logo_url VARCHAR(500),
  created_date DATE DEFAULT CURRENT_DATE
)
```
**الغرض**: معلومات الشركة/المكتب القانوني

---

## 🔗 مخطط العلاقات الرئيسية

```
📊 النظام القانوني - مخطط العلاقات
├── 🏢 المستوى التنظيمي
│   ├── governorates (1) ←→ (∞) branches
│   ├── branches (1) ←→ (∞) employees
│   └── courts (1) ←→ (∞) employees [كأقسام]
│
├── 👥 إدارة العملاء والموظفين
│   ├── clients (1) ←→ (∞) issues
│   ├── employees (1) ←→ (1) users
│   └── issue_types (1) ←→ (∞) issues
│
├── 💰 النظام المحاسبي
│   ├── chart_of_accounts (1) ←→ (∞) chart_of_accounts [self-reference]
│   ├── account_linking_settings (1) ←→ (∞) clients
│   ├── account_linking_settings (1) ←→ (∞) employees
│   ├── chart_of_accounts (1) ←→ (∞) account_sub_links
│   └── issues (1) ←→ (∞) movements
│
├── 📊 النظام المالي المتقدم
│   ├── lineages (1) ←→ (∞) case_distribution
│   ├── services (1) ←→ (∞) service_distributions
│   └── case_distribution (1) ←→ (∞) service_distributions
│
├── 💬 نظام التواصل
│   ├── clients (1) ←→ (∞) conversations
│   ├── users (1) ←→ (∞) conversations
│   └── conversations (1) ←→ (∞) messages
│
└── 📈 المتابعة والتقارير
    └── issues (1) ←→ (∞) follows
```

---

## 🎯 ملخص أنواع العلاقات

### **One-to-Many (1:∞)**
- governorates → branches, employees
- branches → employees
- clients → issues, conversations
- issues → follows, movements
- chart_of_accounts → account_sub_links
- account_linking_settings → clients, employees

### **Many-to-One (∞:1)**
- employees → branches, governorates, courts
- issues → clients, issue_types
- conversations → clients, users

### **One-to-One (1:1)**
- employees ↔ users

### **Self-Reference**
- chart_of_accounts → chart_of_accounts (parent-child)

---

## 🔧 المحفزات والدوال التلقائية (Triggers & Functions)

### **المحفزات المُفعلة:**
1. `trigger_auto_link_client_account` - ربط العملاء الجدد تلقائياً
2. `trigger_auto_link_employee_account` - ربط الموظفين الجدد تلقائياً
3. `trigger_update_conversation_last_message` - تحديث آخر رسالة في المحادثة

### **الدوال المساعدة:**
1. `auto_link_client_account()` - ربط حساب العميل تلقائياً
2. `auto_link_employee_account()` - ربط حساب الموظف تلقائياً
3. `update_conversation_last_message()` - تحديث وقت آخر رسالة

---

## 📝 ملاحظات مهمة

1. **سلامة البيانات**: جميع المفاتيح الخارجية محمية بقيود CASCADE أو SET NULL
2. **الفهرسة**: تم إنشاء فهارس على جميع المفاتيح الخارجية لتحسين الأداء
3. **التشفير**: كلمات المرور مشفرة باستخدام bcrypt
4. **المرونة**: النظام يدعم إضافة جداول جديدة وربطها بالنظام المحاسبي تلقائياً
5. **التوسع**: البنية تدعم إضافة فروع ومحافظات ومراكز تكلفة جديدة

---

---

## 🎨 مخطط بصري للعلاقات

```
                    🏢 النظام التنظيمي
                    ┌─────────────────┐
                    │   governorates  │
                    └─────────┬───────┘
                              │ 1:∞
                    ┌─────────▼───────┐
                    │    branches     │
                    └─────────┬───────┘
                              │ 1:∞
                    ┌─────────▼───────┐
                    │   employees     │◄──┐
                    └─────────┬───────┘   │ 1:1
                              │ 1:1       │
                    ┌─────────▼───────┐   │
                    │     users       │───┘
                    └─────────────────┘

    💰 النظام المحاسبي                     👥 إدارة العملاء
    ┌─────────────────┐                   ┌─────────────────┐
    │chart_of_accounts│                   │     clients     │
    └─────────┬───────┘                   └─────────┬───────┘
              │ 1:∞                                 │ 1:∞
    ┌─────────▼───────┐                   ┌─────────▼───────┐
    │account_linking_ │◄──────────────────┤     issues      │
    │   settings      │ ∞:1               └─────────┬───────┘
    └─────────┬───────┘                             │ 1:∞
              │ 1:∞                       ┌─────────▼───────┐
    ┌─────────▼───────┐                   │    follows      │
    │account_sub_links│                   └─────────────────┘
    └─────────────────┘                   ┌─────────────────┐
                                          │   movements     │
                                          └─────────────────┘

                    💬 نظام التواصل
                    ┌─────────────────┐
                    │ conversations   │
                    └─────────┬───────┘
                              │ 1:∞
                    ┌─────────▼───────┐
                    │    messages     │
                    └─────────────────┘
                    ┌─────────────────┐
                    │ notifications   │
                    └─────────────────┘
```

---

## 🔍 تفاصيل العلاقات المعقدة

### **1. العلاقة الثلاثية: employees → courts (كأقسام)**
```sql
-- الموظف يمكن أن ينتمي لقسم (محكمة) ومحافظة وفرع
employees.department_id → courts.id
employees.governorate_id → governorates.id
employees.branch_id → branches.id
```

### **2. النظام المحاسبي المتكامل**
```sql
-- ربط تلقائي للعملاء والموظفين بالحسابات
clients.account_id → account_linking_settings.id
employees.account_id → account_linking_settings.id

-- إنشاء حسابات فرعية تلقائياً
account_sub_links.main_account_id → chart_of_accounts.id
account_sub_links.linked_table = 'clients' | 'employees'
account_sub_links.linked_record_id → clients.id | employees.id
```

### **3. نظام توزيع الأرباح المعقد**
```sql
-- سلسلة التوزيع المالي
lineages (نسب) → case_distribution (توزيع القضايا) → service_distributions (توزيع الخدمات)

-- كل قضية لها توزيع مالي حسب النوع
issues → case_distribution (via lineages)
-- كل توزيع له خدمات متعددة
case_distribution → service_distributions
-- كل خدمة مرتبطة بمحامي محدد
service_distributions.lawyer_id → employees.id
```

---

## 📊 إحصائيات النظام الحالية

### **البيانات المُدخلة:**
- **المحافظات**: 12 محافظة يمنية
- **الفروع**: 4 فروع رئيسية
- **الموظفين**: 5 موظفين نشطين
- **العملاء**: 4 عملاء مسجلين
- **القضايا**: متعددة حسب النوع
- **أنواع القضايا**: 5 أنواع رئيسية
- **النسب المالية**: 8 نسب مختلفة
- **الخدمات**: 8 خدمات قانونية

### **الحسابات المحاسبية:**
- **دليل الحسابات**: هيكل محاسبي متكامل
- **إعدادات الربط**: 6 إعدادات نشطة
- **الحسابات الفرعية**: ربط تلقائي لجميع العملاء والموظفين

---

## 🛡️ الأمان وسلامة البيانات

### **قيود سلامة البيانات:**
```sql
-- منع حذف البيانات المرتبطة
ON DELETE CASCADE: conversations → messages
ON DELETE SET NULL: account_linking_settings → clients/employees
ON UPDATE CASCADE: جميع المفاتيح الخارجية

-- قيود التحقق
CHECK (sender_type IN ('user', 'client'))
CHECK (message_type IN ('text', 'image', 'file', 'reply'))
CHECK (movement_type IN ('income', 'expense'))
```

### **الفهرسة للأداء:**
```sql
-- فهارس المفاتيح الخارجية
idx_clients_account_id, idx_employees_account_id
idx_conversations_client_id, idx_conversations_user_id
idx_messages_conversation_id, idx_issues_client_id

-- فهارس البحث
idx_clients_account_id_not_null
idx_employees_account_id_not_null
```

---

## 🔄 العمليات التلقائية (Automated Operations)

### **المحفزات النشطة:**
1. **عند إضافة عميل جديد**: ربط تلقائي بإعدادات الحساب المحاسبي
2. **عند إضافة موظف جديد**: ربط تلقائي بإعدادات الحساب المحاسبي
3. **عند إرسال رسالة**: تحديث وقت آخر رسالة في المحادثة
4. **عند إرسال رسالة**: إنشاء إشعار تلقائي للطرف الآخر

### **الدوال المساعدة:**
```sql
-- إنشاء حساب فرعي تلقائي
create_sub_account_link(p_main_account_id, p_table_name, p_record_id)

-- ربط العميل بالحساب المحاسبي
auto_link_client_account() RETURNS TRIGGER

-- ربط الموظف بالحساب المحاسبي
auto_link_employee_account() RETURNS TRIGGER
```

---

## 🎯 نقاط القوة في التصميم

### **1. المرونة والتوسع:**
- إمكانية إضافة جداول جديدة وربطها بالنظام المحاسبي تلقائياً
- دعم إضافة فروع ومحافظات جديدة دون تعديل الكود
- نظام صلاحيات قابل للتوسع

### **2. التكامل:**
- ربط شامل بين النظام القانوني والمحاسبي
- تتبع مالي دقيق لكل عميل وموظف وقضية
- نظام توزيع أرباح متقدم

### **3. سهولة الاستخدام:**
- واجهات API موحدة لجميع العمليات
- نظام محادثات متكامل للتواصل مع العملاء
- تقارير مالية وإدارية شاملة

### **4. الأمان:**
- تشفير كلمات المرور
- نظام صلاحيات متدرج
- حماية البيانات من الحذف العرضي

---

**📅 آخر تحديث**: 2024-12-19
**🔢 إجمالي الجداول**: 24+ جدول
**🔗 إجمالي العلاقات**: 40+ علاقة
**💾 قاعدة البيانات**: PostgreSQL (mohammi)
**🏗️ نوع النظام**: نظام إدارة مكتب قانوني متكامل
