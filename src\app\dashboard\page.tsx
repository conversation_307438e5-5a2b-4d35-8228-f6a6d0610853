'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  FileText,
  MessageCircle,
  Calendar,
  Bell,
  TrendingUp,
  DollarSign,
  Scale,
  Building2,
  UserCheck,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  LogOut
} from 'lucide-react'

interface UserSession {
  id: number
  username: string
  name: string
  type: 'user'
  token: string
}

interface DashboardStats {
  totalClients: number
  totalCases: number
  activeCases: number
  pendingCases: number
  totalUsers: number
  onlineUsers: number
  unreadMessages: number
  todayHearings: number
}

export default function DashboardPage() {
  const router = useRouter()
  const [userSession, setUserSession] = useState<UserSession | null>(null)
  const [stats, setStats] = useState<DashboardStats>({
    totalClients: 0,
    totalCases: 0,
    activeCases: 0,
    pendingCases: 0,
    totalUsers: 0,
    onlineUsers: 0,
    unreadMessages: 0,
    todayHearings: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  // جلب بيانات الجلسة
  useEffect(() => {
    const session = localStorage.getItem('userSession')
    if (!session) {
      router.push('/login')
      return
    }

    const parsedSession = JSON.parse(session)
    if (parsedSession.type !== 'user') {
      router.push('/login')
      return
    }

    setUserSession(parsedSession)
    fetchDashboardData()
  }, [router])

  // تسجيل الخروج
  const handleLogout = async () => {
    try {
      // محاولة تسجيل الخروج من الخادم
      await fetch('/api/auth/users', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userSession?.token}`
        }
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // حذف الجلسة من localStorage والانتقال لصفحة تسجيل الدخول
      localStorage.removeItem('userSession')
      router.push('/login')
    }
  }

  // جلب بيانات لوحة التحكم
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)

      // جلب إحصائيات الموكلين
      const clientsResponse = await fetch('/api/clients')
      if (clientsResponse.ok) {
        const clientsResult = await clientsResponse.json()
        if (clientsResult.success) {
          setStats(prev => ({ ...prev, totalClients: clientsResult.data.length }))
        }
      }

      // جلب إحصائيات القضايا
      const casesResponse = await fetch('/api/cases')
      if (casesResponse.ok) {
        const casesResult = await casesResponse.json()
        if (casesResult.success) {
          const cases = casesResult.data
          setStats(prev => ({
            ...prev,
            totalCases: cases.length,
            activeCases: cases.filter((c: any) => c.status === 'active').length,
            pendingCases: cases.filter((c: any) => c.status === 'pending').length
          }))
        }
      }

      // جلب إحصائيات المستخدمين
      const usersResponse = await fetch('/api/users')
      if (usersResponse.ok) {
        const usersResult = await usersResponse.json()
        if (usersResult.success) {
          const users = usersResult.data
          setStats(prev => ({
            ...prev,
            totalUsers: users.length,
            onlineUsers: users.filter((u: any) => u.is_online).length
          }))
        }
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!userSession) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* ترحيب */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              مرحباً، {userSession.name}
            </h1>
            <p className="text-gray-600 mt-1">
              لوحة التحكم الرئيسية - نظام الإدارة القانونية
            </p>
          </div>
          <div className="flex items-center space-x-4 space-x-reverse">
            <Badge className="bg-green-100 text-green-800">
              متصل
            </Badge>
            <Button
              onClick={() => router.push('/clients')}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Users className="h-4 w-4 mr-2" />
              إدارة الموكلين
            </Button>
            <Button
              onClick={handleLogout}
              variant="outline"
              className="border-red-200 text-red-700 hover:bg-red-50"
            >
              <LogOut className="h-4 w-4 mr-2" />
              تسجيل الخروج
            </Button>
          </div>
        </div>

        {/* الإحصائيات الرئيسية */}
        {isLoading ? (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-16 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {/* إجمالي الموكلين */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <UserCheck className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">{stats.totalClients}</div>
                    <div className="text-sm text-gray-600">إجمالي الموكلين</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* إجمالي القضايا */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Scale className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">{stats.totalCases}</div>
                    <div className="text-sm text-gray-600">إجمالي القضايا</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* القضايا النشطة */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">{stats.activeCases}</div>
                    <div className="text-sm text-gray-600">القضايا النشطة</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* المستخدمين المتصلين */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.onlineUsers}/{stats.totalUsers}
                    </div>
                    <div className="text-sm text-gray-600">المستخدمين المتصلين</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* الإجراءات السريعة */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* الإجراءات السريعة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                الإجراءات السريعة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center"
                  onClick={() => router.push('/clients')}
                >
                  <UserCheck className="h-6 w-6 mb-2" />
                  إدارة الموكلين
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center"
                  onClick={() => router.push('/cases')}
                >
                  <Scale className="h-6 w-6 mb-2" />
                  إدارة القضايا
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center"
                  onClick={() => router.push('/users')}
                >
                  <Users className="h-6 w-6 mb-2" />
                  إدارة المستخدمين
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center"
                  onClick={() => router.push('/financial-lists')}
                >
                  <DollarSign className="h-6 w-6 mb-2" />
                  القوائم المالية
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* الإحصائيات التفصيلية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                إحصائيات تفصيلية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <span className="text-sm font-medium">القضايا النشطة</span>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    {stats.activeCases}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-yellow-600 mr-2" />
                    <span className="text-sm font-medium">القضايا المعلقة</span>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {stats.pendingCases}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center">
                    <MessageCircle className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium">الرسائل غير المقروءة</span>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">
                    {stats.unreadMessages}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-purple-600 mr-2" />
                    <span className="text-sm font-medium">جلسات اليوم</span>
                  </div>
                  <Badge className="bg-purple-100 text-purple-800">
                    {stats.todayHearings}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* النشاط الأخير */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2" />
              النشاط الأخير
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">تم تسجيل دخولك بنجاح</p>
                  <p className="text-xs text-gray-500">منذ دقائق</p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">تم تحديث بيانات النظام</p>
                  <p className="text-xs text-gray-500">اليوم</p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">نظام المحادثات متاح</p>
                  <p className="text-xs text-gray-500">اليوم</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
