# تشغيل LedgerSMB باستخدام Docker (الطريقة الأسهل)
# Run LedgerSMB using Docker (Easiest Method)

param(
    [int]$Port = 8444
)

Write-Host "🐳 تشغيل LedgerSMB باستخدام Docker..." -ForegroundColor Green
Write-Host "🌐 المنفذ: $Port" -ForegroundColor Yellow

# التحقق من وجود Docker
try {
    docker --version | Out-Null
    Write-Host "✅ Docker متوفر" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker غير مثبت" -ForegroundColor Red
    Write-Host "🔧 يرجى تثبيت Docker Desktop من: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    
    $response = Read-Host "هل تريد تحميل Docker Desktop؟ (y/n)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        Start-Process "https://www.docker.com/products/docker-desktop"
    }
    exit 1
}

# إيقاف أي حاويات موجودة
Write-Host "🛑 إيقاف الحاويات الموجودة..." -ForegroundColor Cyan
docker stop ledgersmb-app ledgersmb-db 2>$null | Out-Null
docker rm ledgersmb-app ledgersmb-db 2>$null | Out-Null

# إنشاء شبكة Docker
Write-Host "🌐 إنشاء شبكة Docker..." -ForegroundColor Cyan
docker network create ledgersmb-network 2>$null | Out-Null

# تشغيل قاعدة البيانات PostgreSQL
Write-Host "🗄️ تشغيل قاعدة بيانات PostgreSQL..." -ForegroundColor Cyan
docker run -d --name ledgersmb-db `
    --network ledgersmb-network `
    -e POSTGRES_DB=ledgersmb `
    -e POSTGRES_USER=postgres `
    -e POSTGRES_PASSWORD=postgres123 `
    -v ledgersmb-data:/var/lib/postgresql/data `
    postgres:13

Write-Host "⏳ انتظار تشغيل قاعدة البيانات..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# تشغيل LedgerSMB
Write-Host "💼 تشغيل تطبيق LedgerSMB..." -ForegroundColor Cyan

# استخدام صورة LedgerSMB الرسمية أو إنشاء حاوية مخصصة
docker run -d --name ledgersmb-app `
    --network ledgersmb-network `
    -e POSTGRES_HOST=ledgersmb-db `
    -e POSTGRES_PORT=5432 `
    -e POSTGRES_DB=ledgersmb `
    -e POSTGRES_USER=postgres `
    -e POSTGRES_PASSWORD=postgres123 `
    -e LSMB_ADMIN_USERNAME=admin `
    -e LSMB_ADMIN_PASSWORD=admin123 `
    -p ${Port}:5762 `
    -v ledgersmb-files:/opt/ledgersmb `
    --restart unless-stopped `
    ledgersmb/ledgersmb:latest

if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ الصورة الرسمية غير متوفرة، سنستخدم صورة بديلة..." -ForegroundColor Yellow
    
    # استخدام صورة Perl مع LedgerSMB
    docker run -d --name ledgersmb-app `
        --network ledgersmb-network `
        -e POSTGRES_HOST=ledgersmb-db `
        -e POSTGRES_PORT=5432 `
        -e POSTGRES_DB=ledgersmb `
        -e POSTGRES_USER=postgres `
        -e POSTGRES_PASSWORD=postgres123 `
        -p ${Port}:8080 `
        -v ledgersmb-files:/app `
        --restart unless-stopped `
        perl:5.32 `
        bash -c "
            apt-get update && 
            apt-get install -y git postgresql-client && 
            git clone https://github.com/ledgersmb/LedgerSMB.git /app && 
            cd /app && 
            cpan App::cpanminus && 
            cpanm --installdeps . && 
            perl -Ilib starman --port 8080 --host 0.0.0.0 bin/ledgersmb-server.psgi
        "
}

Write-Host "⏳ انتظار تشغيل LedgerSMB..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# التحقق من حالة الحاويات
Write-Host "🔍 التحقق من حالة الحاويات..." -ForegroundColor Cyan
$dbStatus = docker ps --filter "name=ledgersmb-db" --format "table {{.Names}}\t{{.Status}}"
$appStatus = docker ps --filter "name=ledgersmb-app" --format "table {{.Names}}\t{{.Status}}"

Write-Host "📊 حالة قاعدة البيانات:" -ForegroundColor Cyan
Write-Host $dbStatus -ForegroundColor White

Write-Host "📊 حالة التطبيق:" -ForegroundColor Cyan
Write-Host $appStatus -ForegroundColor White

Write-Host ""
Write-Host "🎉 تم تشغيل LedgerSMB بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 الوصول إلى LedgerSMB:" -ForegroundColor Cyan
Write-Host "   المحلي: http://localhost:$Port" -ForegroundColor Yellow
Write-Host "   الخارجي: http://[your-ip]:$Port" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 معلومات تسجيل الدخول:" -ForegroundColor Cyan
Write-Host "   المستخدم: admin" -ForegroundColor White
Write-Host "   كلمة المرور: admin123" -ForegroundColor White
Write-Host ""
Write-Host "🗄️ معلومات قاعدة البيانات:" -ForegroundColor Cyan
Write-Host "   الخادم: ledgersmb-db" -ForegroundColor White
Write-Host "   قاعدة البيانات: ledgersmb" -ForegroundColor White
Write-Host "   المستخدم: postgres" -ForegroundColor White
Write-Host "   كلمة المرور: postgres123" -ForegroundColor White
Write-Host ""
Write-Host "🛠️ أوامر إدارة مفيدة:" -ForegroundColor Cyan
Write-Host "   عرض السجلات: docker logs ledgersmb-app" -ForegroundColor White
Write-Host "   إيقاف الخدمة: docker stop ledgersmb-app ledgersmb-db" -ForegroundColor White
Write-Host "   بدء الخدمة: docker start ledgersmb-db ledgersmb-app" -ForegroundColor White
Write-Host "   حذف الحاويات: docker rm -f ledgersmb-app ledgersmb-db" -ForegroundColor White
Write-Host ""

# فتح المتصفح
$response = Read-Host "هل تريد فتح LedgerSMB في المتصفح؟ (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host "🌐 فتح LedgerSMB في المتصفح..." -ForegroundColor Cyan
    Start-Sleep -Seconds 3
    Start-Process "http://localhost:$Port"
}

Write-Host "✅ LedgerSMB يعمل الآن على المنفذ $Port!" -ForegroundColor Green
Write-Host "💡 لإيقاف الخدمة، اضغط Ctrl+C أو شغل: docker stop ledgersmb-app ledgersmb-db" -ForegroundColor Yellow
