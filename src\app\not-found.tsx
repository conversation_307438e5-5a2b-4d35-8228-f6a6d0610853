'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { 
  Search, 
  Home, 
  ArrowRight,
  FileQuestion,
  MapPin,
  Compass
} from 'lucide-react'

export default function NotFound() {
  const router = useRouter()

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back()
    } else {
      router.push('/dashboard')
    }
  }

  const handleGoHome = () => {
    router.push('/dashboard')
  }

  const handleSearch = () => {
    router.push('/dashboard')
    // يمكن إضافة تركيز على مربع البحث الذكي هنا
  }

  const popularPages = [
    { name: 'لوحة التحكم', path: '/dashboard', icon: Home },
    { name: 'إدارة الموكلين', path: '/clients', icon: Search },
    { name: 'إدارة القضايا', path: '/issues', icon: FileQuestion },
    { name: 'توزيع القضايا', path: '/case-distribution', icon: MapPin },
  ]

  return (
    <MainLayout>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-100 p-4">
        <Card className="w-full max-w-2xl shadow-2xl border-0">
          <CardHeader className="text-center pb-2">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <div className="text-8xl font-bold text-purple-500 opacity-20">404</div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <Compass className="h-16 w-16 text-purple-600 animate-spin" style={{ animationDuration: '4s' }} />
                </div>
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-gray-800 mb-2">
              🔍 الصفحة غير موجودة
            </CardTitle>
            <p className="text-lg text-gray-600">
              عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها
            </p>
          </CardHeader>
          
          <CardContent className="text-center space-y-6">
            {/* رسالة توضيحية */}
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center justify-center mb-2">
                <FileQuestion className="h-5 w-5 text-purple-600 mr-2" />
                <span className="font-semibold text-purple-800">ماذا حدث؟</span>
              </div>
              <p className="text-purple-700 text-sm">
                قد تكون الصفحة قد تم نقلها، حذفها، أو أن الرابط غير صحيح.
              </p>
            </div>

            {/* الصفحات الشائعة */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-3">📋 الصفحات الشائعة:</h3>
              <div className="grid grid-cols-2 gap-2">
                {popularPages.map((page) => {
                  const IconComponent = page.icon
                  return (
                    <Button
                      key={page.path}
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(page.path)}
                      className="justify-start text-xs"
                    >
                      <IconComponent className="h-3 w-3 mr-1" />
                      {page.name}
                    </Button>
                  )
                })}
              </div>
            </div>

            {/* أزرار التنقل الرئيسية */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={handleGoBack}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white"
                size="lg"
              >
                <ArrowRight className="h-5 w-5 mr-2 rotate-180" />
                العودة للخلف
              </Button>
              <Button 
                onClick={handleSearch}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                size="lg"
              >
                <Search className="h-5 w-5 mr-2" />
                البحث الذكي
              </Button>
              <Button 
                onClick={handleGoHome}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                size="lg"
              >
                <Home className="h-5 w-5 mr-2" />
                الصفحة الرئيسية
              </Button>
            </div>

            {/* نصائح مفيدة */}
            <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-2">💡 نصائح مفيدة:</h4>
              <ul className="text-blue-700 text-sm space-y-1 text-right">
                <li>• استخدم البحث الذكي للعثور على ما تريد</li>
                <li>• تحقق من صحة الرابط المكتوب</li>
                <li>• تصفح القوائم الجانبية للوصول للصفحات</li>
                <li>• ابدأ من لوحة التحكم الرئيسية</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
