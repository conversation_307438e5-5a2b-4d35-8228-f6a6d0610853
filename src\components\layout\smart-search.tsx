'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Search, ExternalLink, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { VoiceSearch } from '@/components/ui/voice-search'

interface SearchResult {
  id: number
  page_title: string
  page_url: string
  page_description: string
  category: string
  keywords: string
}

export function SmartSearch() {
  const [searchTerm, setSearchTerm] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(null)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const searchRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // البحث في الصفحات
  const searchPages = async (term: string) => {
    if (term.trim().length < 2) {
      setResults([])
      setShowResults(false)
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/search/navigation?q=${encodeURIComponent(term)}`)
      const data = await response.json()

      if (data.success) {
        setResults(data.data)
        setShowResults(true)

        // اختيار النتيجة الأفضل تلقائياً
        if (data.data.length > 0) {
          // إذا كان هناك تطابق تام في العنوان، اختره
          const exactMatch = data.data.find((result: SearchResult) =>
            result.page_title.toLowerCase() === term.toLowerCase()
          )

          if (exactMatch) {
            setSelectedResult(exactMatch)
            setSelectedIndex(data.data.findIndex((r: SearchResult) => r.id === exactMatch.id))
          } else if (data.data.length === 1) {
            // إذا كان هناك نتيجة واحدة فقط، اختارها
            setSelectedResult(data.data[0])
            setSelectedIndex(0)
          } else {
            // اختر النتيجة الأولى كافتراضي
            setSelectedResult(data.data[0])
            setSelectedIndex(0)
          }
        } else {
          setSelectedResult(null)
          setSelectedIndex(0)
        }
      } else {
        setResults([])
        setShowResults(false)
        setSelectedResult(null)
      }
    } catch (error) {
      console.error('Error searching:', error)
      setResults([])
      setShowResults(false)
    } finally {
      setIsLoading(false)
    }
  }

  // التعامل مع نتائج البحث الصوتي
  const handleVoiceResult = (text: string) => {
    setSearchTerm(text)
    // البحث فوراً بدون تأخير للبحث الصوتي
    searchPages(text)
  }

  // تأخير البحث
  useEffect(() => {
    const timer = setTimeout(() => {
      searchPages(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // إخفاء النتائج عند النقر خارج المكون
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // قائمة الصفحات قيد الإنشاء
  const UNDER_CONSTRUCTION_PAGES = [
    '/service-distribution',
    '/lineages',
    '/journal-entries-new',
    '/advanced-reports',
    '/case-analytics',
    '/financial-reports',
    '/movements',
    '/follows',
    '/receipt-vouchers',
    '/payment-vouchers',
    '/contracts',
    '/documents',
    '/appointments',
    '/notifications',
    '/backup',
    '/system-logs'
  ]

  // الانتقال إلى الصفحة المحددة
  const navigateToPage = (result: SearchResult) => {
    // التحقق من حالة الصفحة قبل الانتقال
    if (UNDER_CONSTRUCTION_PAGES.includes(result.page_url)) {
      // توجيه للصفحة قيد الإنشاء
      router.push('/under-construction')
      setSearchTerm('')
      setShowResults(false)
      setSelectedResult(null)
      return
    }

    // تحديد الرابط الكامل بناءً على البيئة
    const baseUrl = window.location.origin // سيكون localhost:7443 أو IP:7443
    const fullUrl = `${baseUrl}${result.page_url}`

    // إذا كان الرابط يبدأ بـ http، فهو رابط خارجي
    if (result.page_url.startsWith('http')) {
      window.open(result.page_url, '_blank')
    } else {
      // رابط داخلي - استخدم router للانتقال
      router.push(result.page_url)
    }

    setSearchTerm('')
    setShowResults(false)
    setSelectedResult(null)
  }

  // الانتقال إلى النتيجة المحددة
  const handleNavigateToSelected = () => {
    if (selectedResult) {
      navigateToPage(selectedResult)
    }
  }

  // مسح البحث
  const clearSearch = () => {
    setSearchTerm('')
    setResults([])
    setShowResults(false)
    setSelectedResult(null)
  }

  // التعامل مع مفاتيح لوحة المفاتيح
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showResults || results.length === 0) {
      if (e.key === 'Escape') {
        setShowResults(false)
      }
      return
    }

    switch (e.key) {
      case 'Enter':
        if (selectedResult) {
          navigateToPage(selectedResult)
        }
        break
      case 'ArrowDown':
        e.preventDefault()
        const nextIndex = selectedIndex < results.length - 1 ? selectedIndex + 1 : 0
        setSelectedIndex(nextIndex)
        setSelectedResult(results[nextIndex])
        break
      case 'ArrowUp':
        e.preventDefault()
        const prevIndex = selectedIndex > 0 ? selectedIndex - 1 : results.length - 1
        setSelectedIndex(prevIndex)
        setSelectedResult(results[prevIndex])
        break
      case 'Escape':
        setShowResults(false)
        break
    }
  }

  return (
    <div ref={searchRef} className="relative flex items-center space-x-3 space-x-reverse">
      {/* مربع البحث */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="🔍 البحث في القوائم... (مثال: سند قبض)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
            className="pr-10 pl-8 w-80 bg-white border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            title="ابحث عن أي صفحة في النظام - مثال: سند قبض، دليل الحسابات، القضايا"
          />
          {searchTerm && (
            <button
              onClick={clearSearch}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* زر البحث الصوتي */}
        <VoiceSearch
          onResult={handleVoiceResult}
          className="mr-2"
        />

        {/* نتائج البحث */}
        {showResults && (
          <div className="absolute top-full right-0 mt-2 w-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500 flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                جاري البحث...
              </div>
            ) : results.length > 0 ? (
              <div className="py-2">
                <div className="px-4 py-2 bg-gray-50 border-b border-gray-100">
                  <span className="text-xs font-medium text-gray-600">
                    {results.length} نتيجة للبحث "{searchTerm}"
                  </span>
                </div>
                {results.map((result, index) => (
                  <button
                    key={result.id}
                    onClick={() => navigateToPage(result)}
                    className={`w-full px-4 py-3 text-right hover:bg-blue-50 border-b border-gray-100 last:border-b-0 transition-all duration-200 ${
                      selectedResult?.id === result.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          {selectedResult?.id === result.id && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          )}
                          <h4 className="font-medium text-gray-900 text-sm">
                            {result.page_title}
                          </h4>
                        </div>
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                          {result.page_description}
                        </p>
                        <div className="flex items-center mt-2 space-x-2 space-x-reverse">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            result.category === 'محاسبة' ? 'bg-green-100 text-green-700' :
                            result.category === 'قضايا' ? 'bg-purple-100 text-purple-700' :
                            result.category === 'تقارير' ? 'bg-orange-100 text-orange-700' :
                            result.category === 'إعدادات' ? 'bg-gray-100 text-gray-700' :
                            result.category === 'مالية' ? 'bg-yellow-100 text-yellow-700' :
                            result.category === 'إدارة' ? 'bg-indigo-100 text-indigo-700' :
                            'bg-blue-100 text-blue-700'
                          }`}>
                            {result.category}
                          </span>
                          {/* مؤشر حالة الصفحة */}
                          {UNDER_CONSTRUCTION_PAGES.includes(result.page_url) ? (
                            <span className="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-700 border border-yellow-300">
                              🚧 قيد الإنشاء
                            </span>
                          ) : (
                            <span className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-700 border border-green-300">
                              ✅ جاهزة
                            </span>
                          )}
                          <span
                            className="text-xs text-gray-400 truncate max-w-24 cursor-help"
                            title={`الرابط الكامل: ${window.location.origin}${result.page_url}`}
                          >
                            {result.page_url}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center flex-shrink-0 mr-3">
                        {selectedResult?.id === result.id && (
                          <span className="text-xs bg-blue-500 text-white px-2 py-1 rounded-full mr-2">
                            مختار
                          </span>
                        )}
                        <ExternalLink className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  </button>
                ))}

                {/* تلميحات الاستخدام */}
                <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>↑↓ للتنقل</span>
                    <span>Enter للانتقال</span>
                    <span>Esc للإغلاق</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-6 text-center">
                <div className="text-gray-400 mb-2">
                  <Search className="h-8 w-8 mx-auto" />
                </div>
                <p className="text-gray-500 text-sm">
                  لا توجد نتائج للبحث "{searchTerm}"
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  جرب كلمات مختلفة أو أقصر
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* الأيقونة الذكية للانتقال السريع */}
      {selectedResult && (
        <div className="flex items-center">
          <Button
            onClick={handleNavigateToSelected}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 py-2 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2 space-x-reverse"
            title={`الانتقال إلى: ${selectedResult.page_title}`}
          >
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <ExternalLink className="h-4 w-4" />
              <span className="font-semibold">{selectedResult.page_title}</span>
            </div>
          </Button>
        </div>
      )}

      {/* مؤشر البحث النشط */}
      {searchTerm && !selectedResult && results.length > 0 && (
        <div className="flex items-center bg-yellow-50 border border-yellow-200 rounded-lg px-3 py-2">
          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse mr-2"></div>
          <span className="text-sm text-yellow-700 font-medium">
            {results.length} نتيجة
          </span>
        </div>
      )}

      {/* مؤشر عدم وجود نتائج */}
      {searchTerm && !isLoading && results.length === 0 && (
        <div className="flex items-center bg-red-50 border border-red-200 rounded-lg px-3 py-2">
          <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
          <span className="text-sm text-red-700 font-medium">
            لا توجد نتائج
          </span>
        </div>
      )}
    </div>
  )
}
