'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Plus,
  Edit,
  Trash2,
  Calculator,
  Search,
  Save,
  X,
  DollarSign
} from 'lucide-react'

interface OpeningBalance {
  id: number
  account_id: number
  account_name: string
  account_code: string
  debit_balance: number
  credit_balance: number
  balance_date: string
  created_date: string
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_level: number
  is_linked_record: boolean
  original_table: string
  external_id: number
}

export default function OpeningBalancesPage() {
  const [balances, setBalances] = useState<OpeningBalance[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingBalance, setEditingBalance] = useState<OpeningBalance | null>(null)
  const [formData, setFormData] = useState({
    account_id: '',
    debit_balance: '',
    credit_balance: '',
    balance_date: new Date().toISOString().split('T')[0]
  })

  // جلب الأرصدة الافتتاحية
  const fetchOpeningBalances = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/opening-balances')
      const data = await response.json()
      if (data.success) {
        setBalances(data.data)
      }
    } catch (error) {
      console.error('Error fetching opening balances:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // جلب الحسابات
  const fetchAccounts = async () => {
    try {
      const response = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true')
      const data = await response.json()
      if (data.success) {
        setAccounts(data.accounts)
      }
    } catch (error) {
      console.error('Error fetching accounts:', error)
    }
  }

  useEffect(() => {
    fetchOpeningBalances()
    fetchAccounts()
  }, [])

  // تصفية الأرصدة
  const filteredBalances = balances.filter(balance =>
    balance.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    balance.account_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // إضافة رصيد افتتاحي جديد
  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await fetch('/api/accounting/opening-balances', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          debit_balance: parseFloat(formData.debit_balance) || 0,
          credit_balance: parseFloat(formData.credit_balance) || 0
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchOpeningBalances()
        setIsAddDialogOpen(false)
        setFormData({
          account_id: '',
          debit_balance: '',
          credit_balance: '',
          balance_date: new Date().toISOString().split('T')[0]
        })
        alert('تم إضافة الرصيد الافتتاحي بنجاح')
      } else {
        alert(`خطأ: ${data.error}`)
      }
    } catch (error) {
      console.error('Error adding opening balance:', error)
      alert('حدث خطأ أثناء إضافة الرصيد الافتتاحي')
    }
  }

  // تعديل رصيد افتتاحي
  const handleEdit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingBalance) return

    try {
      const response = await fetch('/api/accounting/opening-balances', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: editingBalance.id,
          ...formData,
          debit_balance: parseFloat(formData.debit_balance) || 0,
          credit_balance: parseFloat(formData.credit_balance) || 0
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchOpeningBalances()
        setIsEditDialogOpen(false)
        setEditingBalance(null)
        alert('تم تحديث الرصيد الافتتاحي بنجاح')
      } else {
        alert(`خطأ: ${data.error}`)
      }
    } catch (error) {
      console.error('Error updating opening balance:', error)
      alert('حدث خطأ أثناء تحديث الرصيد الافتتاحي')
    }
  }

  // حذف رصيد افتتاحي
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف الرصيد الافتتاحي؟')) return

    try {
      const response = await fetch(`/api/accounting/opening-balances?id=${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      if (data.success) {
        await fetchOpeningBalances()
        alert('تم حذف الرصيد الافتتاحي بنجاح')
      } else {
        alert(`خطأ: ${data.error}`)
      }
    } catch (error) {
      console.error('Error deleting opening balance:', error)
      alert('حدث خطأ أثناء حذف الرصيد الافتتاحي')
    }
  }

  // فتح نافذة التعديل
  const openEditDialog = (balance: OpeningBalance) => {
    setEditingBalance(balance)
    setFormData({
      account_id: balance.account_id.toString(),
      debit_balance: balance.debit_balance.toString(),
      credit_balance: balance.credit_balance.toString(),
      balance_date: balance.balance_date
    })
    setIsEditDialogOpen(true)
  }

  // حساب إجمالي الأرصدة
  const totalDebits = filteredBalances.reduce((sum, balance) => sum + balance.debit_balance, 0)
  const totalCredits = filteredBalances.reduce((sum, balance) => sum + balance.credit_balance, 0)

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان الرئيسي */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">الأرصدة الافتتاحية</h1>
            <p className="text-gray-600 mt-1">إدارة الأرصدة الافتتاحية للحسابات</p>
          </div>
          
          {/* زر إضافة رصيد افتتاحي جديد */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة رصيد افتتاحي
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة رصيد افتتاحي جديد</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleAdd} className="space-y-4">
                <div>
                  <Label htmlFor="account_id">الحساب *</Label>
                  <Select value={formData.account_id} onValueChange={(value) => setFormData({...formData, account_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الحساب" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      {accounts.map((account) => (
                        <SelectItem key={account.id} value={account.id.toString()}>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="text-blue-600">
                              {account.is_linked_record ?
                                (account.original_table === 'employees' ? '👤' : '🏢') :
                                '📁'
                              }
                            </span>
                            <span className="font-medium">{account.account_code}</span>
                            <span>-</span>
                            <span>{account.account_name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="debit_balance">الرصيد المدين</Label>
                  <Input
                    id="debit_balance"
                    type="number"
                    step="0.01"
                    value={formData.debit_balance}
                    onChange={(e) => setFormData({...formData, debit_balance: e.target.value})}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="credit_balance">الرصيد الدائن</Label>
                  <Input
                    id="credit_balance"
                    type="number"
                    step="0.01"
                    value={formData.credit_balance}
                    onChange={(e) => setFormData({...formData, credit_balance: e.target.value})}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="balance_date">تاريخ الرصيد</Label>
                  <Input
                    id="balance_date"
                    type="date"
                    value={formData.balance_date}
                    onChange={(e) => setFormData({...formData, balance_date: e.target.value})}
                    required
                  />
                </div>
                <div className="flex space-x-3 space-x-reverse">
                  <Button type="submit" className="flex-1">
                    <Save className="h-4 w-4 mr-2" />
                    حفظ
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsAddDialogOpen(false)}
                    className="flex-1"
                  >
                    <X className="h-4 w-4 mr-2" />
                    إلغاء
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* البحث والإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="md:col-span-2">
            <CardContent className="p-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الأرصدة..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">إجمالي المدين</p>
                  <p className="text-lg font-bold text-green-600">{totalDebits.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <DollarSign className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">إجمالي الدائن</p>
                  <p className="text-lg font-bold text-red-600">{totalCredits.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* جدول الأرصدة الافتتاحية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              الأرصدة الافتتاحية ({filteredBalances.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">جاري التحميل...</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>كود الحساب</TableHead>
                    <TableHead>اسم الحساب</TableHead>
                    <TableHead>الرصيد المدين</TableHead>
                    <TableHead>الرصيد الدائن</TableHead>
                    <TableHead>تاريخ الرصيد</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBalances.map((balance) => (
                    <TableRow key={balance.id}>
                      <TableCell className="font-medium">{balance.account_code}</TableCell>
                      <TableCell>{balance.account_name}</TableCell>
                      <TableCell className="text-green-600 font-medium">
                        {balance.debit_balance > 0 ? balance.debit_balance.toLocaleString() : '-'}
                      </TableCell>
                      <TableCell className="text-red-600 font-medium">
                        {balance.credit_balance > 0 ? balance.credit_balance.toLocaleString() : '-'}
                      </TableCell>
                      <TableCell>{new Date(balance.balance_date).toLocaleDateString('en-GB')}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(balance)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(balance.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* نافذة التعديل */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل الرصيد الافتتاحي</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleEdit} className="space-y-4">
              <div>
                <Label htmlFor="edit_account_id">الحساب *</Label>
                <Select value={formData.account_id} onValueChange={(value) => setFormData({...formData, account_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحساب" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {accounts.map((account) => (
                      <SelectItem key={account.id} value={account.id.toString()}>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-blue-600">
                            {account.is_linked_record ?
                              (account.original_table === 'employees' ? '👤' : '🏢') :
                              '📁'
                            }
                          </span>
                          <span className="font-medium">{account.account_code}</span>
                          <span>-</span>
                          <span>{account.account_name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit_debit_balance">الرصيد المدين</Label>
                <Input
                  id="edit_debit_balance"
                  type="number"
                  step="0.01"
                  value={formData.debit_balance}
                  onChange={(e) => setFormData({...formData, debit_balance: e.target.value})}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label htmlFor="edit_credit_balance">الرصيد الدائن</Label>
                <Input
                  id="edit_credit_balance"
                  type="number"
                  step="0.01"
                  value={formData.credit_balance}
                  onChange={(e) => setFormData({...formData, credit_balance: e.target.value})}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label htmlFor="edit_balance_date">تاريخ الرصيد</Label>
                <Input
                  id="edit_balance_date"
                  type="date"
                  value={formData.balance_date}
                  onChange={(e) => setFormData({...formData, balance_date: e.target.value})}
                  required
                />
              </div>
              <div className="flex space-x-3 space-x-reverse">
                <Button type="submit" className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  حفظ التغييرات
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditDialogOpen(false)}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  إلغاء
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
